# Gemini Balance + WARP Docker 集成说明

本文档说明了如何在 gemini-balance 项目中集成 Cloudflare WARP 代理服务。

## 集成内容

### 1. 新增的服务

在 `docker-compose.yml` 中新增了 `warp` 服务：

- **镜像**: `caomingjun/warp`
- **容器名**: `gemini-balance-warp`
- **端口**: `1080` (SOCKS5/HTTP 代理)
- **数据持久化**: 使用 `warp_data` 卷存储 WARP 客户端数据

### 2. 服务依赖关系

- `gemini-balance` 服务现在依赖于 `warp` 服务的健康检查
- 确保 WARP 代理在 gemini-balance 启动前已经就绪

### 3. 代理配置

在 `.env` 文件中已配置：
```
PROXIES=["socks5://warp:1080"]
```

这使得 gemini-balance 的所有外部请求都会通过 WARP 代理。

## 启动服务

```bash
cd gemini-balance
docker-compose up -d
```

## 验证 WARP 连接

### 1. 检查 WARP 服务状态
```bash
docker-compose logs warp
```

### 2. 测试 WARP 代理连接
```bash
# 从主机测试
curl --socks5-hostname 127.0.0.1:1080 https://cloudflare.com/cdn-cgi/trace

# 从容器内测试
docker exec gemini-balance-warp curl --socks5-hostname 127.0.0.1:1080 https://cloudflare.com/cdn-cgi/trace
```

如果输出包含 `warp=on` 或 `warp=plus`，说明 WARP 工作正常。

### 3. 检查 gemini-balance 是否使用代理
```bash
docker-compose logs gemini-balance
```

## 高级配置

### WARP+ 许可证密钥

如果您有 WARP+ 订阅，可以在 `docker-compose.yml` 中取消注释并设置：
```yaml
environment:
  - WARP_LICENSE_KEY=your_warp_plus_license_key
```

### NAT 模式

如果需要启用 NAT 模式，取消注释相关配置：
```yaml
environment:
  - WARP_ENABLE_NAT=1
sysctls:
  - net.ipv4.ip_forward=1
  - net.ipv6.conf.all.forwarding=1
  - net.ipv6.conf.all.accept_ra=2
```

### 自定义 GOST 参数

可以通过环境变量自定义 GOST 代理参数：
```yaml
environment:
  - GOST_ARGS=-L :1080  # 默认值
```

## 故障排除

### 1. WARP 连接失败

如果 WARP 无法连接，检查：
- 容器是否有足够的权限 (NET_ADMIN capability)
- 系统是否支持 TUN 设备
- 防火墙设置

### 2. 代理连接问题

如果 gemini-balance 无法通过代理连接：
- 确认 WARP 服务健康检查通过
- 检查网络连接配置
- 验证代理地址配置正确

### 3. 性能问题

如果遇到性能问题：
- 调整 `WARP_SLEEP` 参数
- 检查服务器性能
- 考虑使用本地 WARP 客户端

## 网络架构

```
Internet
    ↓
Cloudflare WARP
    ↓
warp:1080 (SOCKS5 Proxy)
    ↓
gemini-balance:8000
    ↓
Google Gemini API
```

## 注意事项

1. **首次启动**: WARP 服务首次启动可能需要较长时间建立连接
2. **数据持久化**: WARP 配置数据存储在 `warp_data` 卷中
3. **许可证变更**: 如果更改 WARP 许可证密钥，需要删除数据卷重新注册
4. **系统兼容性**: 某些 NAS 系统可能不支持 nftables，需要使用代理模式

## 相关链接

- [WARP Docker 项目](https://github.com/cmj2002/warp-docker)
- [Gemini Balance 项目](https://github.com/snailyp/gemini-balance)
- [Cloudflare WARP 官方文档](https://1.1.1.1/)
