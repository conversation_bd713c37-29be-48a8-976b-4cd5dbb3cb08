#!/bin/bash

# Gemini Balance + WARP 集成测试脚本
# 用于验证 WARP 代理是否正常工作

set -e

echo "🚀 开始测试 Gemini Balance + WARP 集成..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查 docker-compose 是否存在
if [ ! -f "docker-compose.yml" ]; then
    echo -e "${RED}❌ 错误: 未找到 docker-compose.yml 文件${NC}"
    exit 1
fi

echo -e "${BLUE}📋 检查服务状态...${NC}"

# 检查服务是否运行
if ! docker-compose ps | grep -q "Up"; then
    echo -e "${YELLOW}⚠️  服务未运行，正在启动...${NC}"
    docker-compose up -d
    echo -e "${BLUE}⏳ 等待服务启动 (60秒)...${NC}"
    sleep 60
else
    echo -e "${GREEN}✅ 服务已运行${NC}"
fi

# 测试 1: 检查容器状态
echo -e "\n${BLUE}🔍 测试 1: 检查容器状态${NC}"
containers=("gemini-balance" "gemini-balance-mysql" "gemini-balance-warp")

for container in "${containers[@]}"; do
    if docker ps --format "table {{.Names}}\t{{.Status}}" | grep -q "$container.*Up"; then
        echo -e "${GREEN}✅ $container: 运行中${NC}"
    else
        echo -e "${RED}❌ $container: 未运行${NC}"
        docker-compose logs "$container" | tail -10
    fi
done

# 测试 2: 检查 WARP 代理连接
echo -e "\n${BLUE}🔍 测试 2: 检查 WARP 代理连接${NC}"
if curl --connect-timeout 10 --socks5-hostname 127.0.0.1:1080 https://cloudflare.com/cdn-cgi/trace 2>/dev/null | grep -q "warp=on\|warp=plus"; then
    echo -e "${GREEN}✅ WARP 代理连接正常${NC}"
    echo -e "${BLUE}📊 WARP 连接信息:${NC}"
    curl --socks5-hostname 127.0.0.1:1080 https://cloudflare.com/cdn-cgi/trace 2>/dev/null | grep -E "(warp|loc|ip)" | head -5
else
    echo -e "${RED}❌ WARP 代理连接失败${NC}"
    echo -e "${YELLOW}🔧 检查 WARP 容器日志:${NC}"
    docker-compose logs warp | tail -20
fi

# 测试 3: 检查 gemini-balance 健康状态
echo -e "\n${BLUE}🔍 测试 3: 检查 gemini-balance 健康状态${NC}"
if curl --connect-timeout 10 -s http://localhost:38000/health >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Gemini Balance 健康检查通过${NC}"
else
    echo -e "${RED}❌ Gemini Balance 健康检查失败${NC}"
    echo -e "${YELLOW}🔧 检查 gemini-balance 容器日志:${NC}"
    docker-compose logs gemini-balance | tail -20
fi

# 测试 4: 检查服务间网络连通性
echo -e "\n${BLUE}🔍 测试 4: 检查服务间网络连通性${NC}"
if docker exec gemini-balance-warp curl --connect-timeout 5 -s http://gemini-balance:8000/health >/dev/null 2>&1; then
    echo -e "${GREEN}✅ WARP -> Gemini Balance 网络连通${NC}"
else
    echo -e "${RED}❌ WARP -> Gemini Balance 网络连接失败${NC}"
fi

if docker exec gemini-balance curl --connect-timeout 5 --socks5-hostname warp:1080 https://cloudflare.com/cdn-cgi/trace 2>/dev/null | grep -q "warp=on\|warp=plus"; then
    echo -e "${GREEN}✅ Gemini Balance -> WARP 代理连通${NC}"
else
    echo -e "${RED}❌ Gemini Balance -> WARP 代理连接失败${NC}"
fi

# 测试 5: 检查数据卷
echo -e "\n${BLUE}🔍 测试 5: 检查数据卷${NC}"
volumes=("mysql_data" "warp_data")

for volume in "${volumes[@]}"; do
    if docker volume ls | grep -q "gemini-balance_$volume"; then
        echo -e "${GREEN}✅ 数据卷 $volume 存在${NC}"
    else
        echo -e "${RED}❌ 数据卷 $volume 不存在${NC}"
    fi
done

# 显示服务信息
echo -e "\n${BLUE}📊 服务信息摘要:${NC}"
echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
docker-compose ps

echo -e "\n${BLUE}🌐 访问地址:${NC}"
echo -e "  • Gemini Balance: ${YELLOW}http://localhost:38000${NC}"
echo -e "  • WARP SOCKS5 代理: ${YELLOW}socks5://localhost:1080${NC}"

echo -e "\n${BLUE}📝 有用的命令:${NC}"
echo -e "  • 查看所有日志: ${YELLOW}docker-compose logs${NC}"
echo -e "  • 查看 WARP 日志: ${YELLOW}docker-compose logs warp${NC}"
echo -e "  • 重启服务: ${YELLOW}docker-compose restart${NC}"
echo -e "  • 停止服务: ${YELLOW}docker-compose down${NC}"

echo -e "\n${GREEN}🎉 测试完成！${NC}"
