volumes:
  mysql_data:
  warp_data:

services:
  gemini-balance:
    image: ghcr.io/snailyp/gemini-balance:latest
    container_name: gemini-balance
    restart: unless-stopped
    ports:
      - "38000:8000"
    env_file:
      - .env
    depends_on:
      mysql:
        condition: service_healthy
      warp:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "python -c \"import requests; exit(0) if requests.get('http://localhost:8000/health').status_code == 200 else exit(1)\""]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 10s

  mysql:
    image: mysql:8
    container_name: gemini-balance-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: redkaytop_your_root_password
      MYSQL_DATABASE: ${MYSQL_DATABASE}
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    # ports:
    #   - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "127.0.0.1"]
      interval: 10s # 每隔10秒检查一次
      timeout: 5s   # 每次检查的超时时间为5秒
      retries: 3    # 重试3次失败后标记为 unhealthy
      start_period: 30s # 容器启动后等待30秒再开始第一次健康检查

  warp:
    image: caomingjun/warp
    container_name: gemini-balance-warp
    restart: unless-stopped
    # add removed rule back (https://github.com/opencontainers/runc/pull/3468)
    device_cgroup_rules:
      - 'c 10:200 rwm'
    ports:
      - "1080:1080"  # SOCKS5/HTTP proxy port
    environment:
      - WARP_SLEEP=2
      # - WARP_LICENSE_KEY= # optional, uncomment and add your WARP+ license key if you have one
      # - WARP_ENABLE_NAT=1 # enable nat mode if needed
    cap_add:
      # Docker already have them, these are for podman users
      - MKNOD
      - AUDIT_WRITE
      # additional required cap for warp, both for podman and docker
      - NET_ADMIN
    sysctls:
      - net.ipv6.conf.all.disable_ipv6=0
      - net.ipv4.conf.all.src_valid_mark=1
      # uncomment for nat mode
      # - net.ipv4.ip_forward=1
      # - net.ipv6.conf.all.forwarding=1
      # - net.ipv6.conf.all.accept_ra=2
    volumes:
      - warp_data:/var/lib/cloudflare-warp
    healthcheck:
      test: ["CMD-SHELL", "curl --socks5-hostname 127.0.0.1:1080 https://cloudflare.com/cdn-cgi/trace | grep -q 'warp=on\\|warp=plus' || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s # WARP needs more time to establish connection
