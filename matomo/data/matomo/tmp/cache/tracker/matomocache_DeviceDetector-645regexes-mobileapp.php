<?php return unserialize('a:2:{s:8:"lifetime";i:1753701387;s:4:"data";a:587:{i:0;a:3:{s:5:"regex";s:22:"jpameblo;(\\d+\\.[.\\d]+)";s:4:"name";s:5:"Ameba";s:7:"version";s:2:"$1";}i:1;a:3:{s:5:"regex";s:21:"CSDNApp/(\\d+\\.[.\\d]+)";s:4:"name";s:4:"CSDN";s:7:"version";s:2:"$1";}i:2;a:3:{s:5:"regex";s:21:"binu(?:/(\\d+[.\\d]+))?";s:4:"name";s:4:"Moya";s:7:"version";s:0:"";}i:3;a:3:{s:5:"regex";s:27:"KP<PERSON>_Veilig[ /](\\d+\\.[.\\d]+)";s:4:"name";s:10:"KPN Veilig";s:7:"version";s:2:"$1";}i:4;a:3:{s:5:"regex";s:27:"NAVER/(\\d+[.\\d]+) CFNetwork";s:4:"name";s:5:"Naver";s:7:"version";s:2:"$1";}i:5;a:3:{s:5:"regex";s:42:"NAVER\\(inapp; search; .+; (\\d+[.\\d]+);.+\\)";s:4:"name";s:5:"Naver";s:7:"version";s:2:"$1";}i:6;a:3:{s:5:"regex";s:39:"NAVER\\(inapp; search; .+; (\\d+[.\\d]+)\\)";s:4:"name";s:5:"Naver";s:7:"version";s:2:"$1";}i:7;a:3:{s:5:"regex";s:44:"NAVER\\(inapp; naverdicapp; .+; (\\d+[.\\d]+)\\)";s:4:"name";s:16:"NAVER Dictionary";s:7:"version";s:2:"$1";}i:8;a:3:{s:5:"regex";s:23:"Chrome/Soldier_([\\d.]+)";s:4:"name";s:7:"Soldier";s:7:"version";s:2:"$1";}i:9;a:3:{s:5:"regex";s:39:"AndroidDownloadManager(?:[ /]([\\d.]+))?";s:4:"name";s:22:"AndroidDownloadManager";s:7:"version";s:2:"$1";}i:10;a:3:{s:5:"regex";s:54:"(?:Apple)?News(?:[ /][\\d.]+)? Version(?:[ /]([\\d.]+))?";s:4:"name";s:10:"Apple News";s:7:"version";s:2:"$1";}i:11;a:3:{s:5:"regex";s:15:"appletv\\.client";s:4:"name";s:8:"Apple TV";s:7:"version";s:0:"";}i:12;a:3:{s:5:"regex";s:52:"AudienceNetworkForAndroid.+(?:FBAV)(?:[ /]([\\d.]+))?";s:4:"name";s:25:"Facebook Audience Network";s:7:"version";s:2:"$1";}i:13;a:3:{s:5:"regex";s:61:"(?:mLite|MessengerLite(?:ForiOS)?).*(?:FBAV)(?:[ /]([\\d.]+))?";s:4:"name";s:23:"Facebook Messenger Lite";s:7:"version";s:2:"$1";}i:14;a:3:{s:5:"regex";s:76:"(?:MessengerForiOS|MESSENGER|FB_IAB/Orca-Android).*(?:FBAV)(?:[ /]([\\d.]+))?";s:4:"name";s:18:"Facebook Messenger";s:7:"version";s:2:"$1";}i:15;a:3:{s:5:"regex";s:43:"(?:GroupsForiOS).*(?:FBAV)(?:[ /]([\\d.]+))?";s:4:"name";s:15:"Facebook Groups";s:7:"version";s:2:"$1";}i:16;a:3:{s:5:"regex";s:31:"FBAN/EMA.+FBAV(?:[ /]([\\d.]+))?";s:4:"name";s:13:"Facebook Lite";s:7:"version";s:2:"$1";}i:17;a:3:{s:5:"regex";s:39:"FBAN/FBPageAdmin.+FBAV(?:[ /]([\\d.]+))?";s:4:"name";s:19:"Meta Business Suite";s:7:"version";s:2:"$1";}i:18;a:3:{s:5:"regex";s:95:"(?:FBAV|com\\.facebook\\.katana|facebook-mobile/1\\.0|facebook-mobile/|Facebook/)(?:[ /]([\\d.]+))?";s:4:"name";s:8:"Facebook";s:7:"version";s:2:"$1";}i:19;a:3:{s:5:"regex";s:24:"(?:FBAN|FBSV|FBID|FBBV)/";s:4:"name";s:8:"Facebook";s:7:"version";s:0:"";}i:20;a:3:{s:5:"regex";s:22:"Instagram[ /]([\\d.]+)?";s:4:"name";s:9:"Instagram";s:7:"version";s:2:"$1";}i:21;a:3:{s:5:"regex";s:22:"Barcelona[ /]([\\d.]+)?";s:4:"name";s:7:"Threads";s:7:"version";s:2:"$1";}i:22;a:3:{s:5:"regex";s:28:"FeedR(?!eader)(?:/([\\d.]+))?";s:4:"name";s:5:"FeedR";s:7:"version";s:2:"$1";}i:23;a:3:{s:5:"regex";s:38:"com\\.google\\.android\\.apps\\.searchlite";s:4:"name";s:9:"Google Go";s:7:"version";s:0:"";}i:24;a:3:{s:5:"regex";s:34:"com\\.google\\.android\\.apps\\.photos";s:4:"name";s:13:"Google Photos";s:7:"version";s:0:"";}i:25;a:3:{s:5:"regex";s:37:"com\\.google\\.android\\.apps\\.magazines";s:4:"name";s:21:"Google Play Newsstand";s:7:"version";s:0:"";}i:26;a:3:{s:5:"regex";s:23:"com\\.google\\.GooglePlus";s:4:"name";s:11:"Google Plus";s:7:"version";s:0:"";}i:27;a:3:{s:5:"regex";s:36:"Google\\.DriveExtension(?:/([\\d.]+))?";s:4:"name";s:12:"Google Drive";s:7:"version";s:2:"$1";}i:28;a:3:{s:5:"regex";s:12:"OPA/([\\d.]+)";s:4:"name";s:16:"Google Assistant";s:7:"version";s:2:"$1";}i:29;a:3:{s:5:"regex";s:23:"MicroMessenger/([\\d.]+)";s:4:"name";s:6:"WeChat";s:7:"version";s:2:"$1";}i:30;a:3:{s:5:"regex";s:15:"WeChat/([\\d.]+)";s:4:"name";s:6:"WeChat";s:7:"version";s:2:"$1";}i:31;a:3:{s:5:"regex";s:32:"WeChatShareExtensionNew/([\\d.]+)";s:4:"name";s:22:"WeChat Share Extension";s:7:"version";s:2:"$1";}i:32;a:3:{s:5:"regex";s:18:"DingTalk/([0-9.]+)";s:4:"name";s:8:"DingTalk";s:7:"version";s:2:"$1";}i:33;a:3:{s:5:"regex";s:22:".*__weibo__([0-9.]+)__";s:4:"name";s:10:"Sina Weibo";s:7:"version";s:2:"$1";}i:34;a:3:{s:5:"regex";s:60:"Pinterest(?: for (?:Android(?: Tablet)?|iOS))?(?:/([\\d.]+))?";s:4:"name";s:9:"Pinterest";s:7:"version";s:2:"$1";}i:35;a:3:{s:5:"regex";s:17:"Podcatcher Deluxe";s:4:"name";s:17:"Podcatcher Deluxe";s:7:"version";s:0:"";}i:36;a:3:{s:5:"regex";s:43:"com\\.google\\.android\\.youtube(?:/([\\d.]+))?";s:4:"name";s:7:"YouTube";s:7:"version";s:2:"$1";}i:37;a:3:{s:5:"regex";s:20:"YouTube/([\\d.]+)[JK]";s:4:"name";s:7:"YouTube";s:7:"version";s:2:"$1";}i:38;a:3:{s:5:"regex";s:25:"Rutube(?:TV)?BlackAndroid";s:4:"name";s:6:"Rutube";s:7:"version";s:2:"$1";}i:39;a:3:{s:5:"regex";s:44:"com\\.netflix\\.mediaclient(?:/(\\d+\\.[\\d.]+))?";s:4:"name";s:7:"Netflix";s:7:"version";s:2:"$1";}i:40;a:3:{s:5:"regex";s:39:"Downcast/(\\d+\\.[\\d.]+)?(?:.+(?:!Mac)|$)";s:4:"name";s:8:"Downcast";s:7:"version";s:2:"$1";}i:41;a:3:{s:5:"regex";s:21:"Flipp-iOS/.+CFNetwork";s:4:"name";s:5:"Flipp";s:7:"version";s:0:"";}i:42;a:3:{s:5:"regex";s:21:"Flipp-iOS/(\\d+[.\\d]+)";s:4:"name";s:5:"Flipp";s:7:"version";s:2:"$1";}i:43;a:3:{s:5:"regex";s:16:"WhatsApp(?:/2)?$";s:4:"name";s:6:"Signal";s:7:"version";s:0:"";}i:44;a:3:{s:5:"regex";s:27:"WhatsApp/([\\d.]+).+CloudAPI";s:4:"name";s:17:"WhatsApp Business";s:7:"version";s:2:"$1";}i:45;a:3:{s:5:"regex";s:56:"WhatsApp(?:Electron|\\-app|\\-linux-app)?(?:[ /]([\\d.]+))?";s:4:"name";s:8:"WhatsApp";s:7:"version";s:2:"$1";}i:46;a:3:{s:5:"regex";s:32:"YoWhatsApp2Plus(?:/(\\d+[.\\d]+))?";s:4:"name";s:10:"WhatsApp+2";s:7:"version";s:2:"$1";}i:47;a:3:{s:5:"regex";s:29:"YoFMWhatsApp(?:/(\\d+[.\\d]+))?";s:4:"name";s:11:"FM WhatsApp";s:7:"version";s:2:"$1";}i:48;a:3:{s:5:"regex";s:34:"(?:Yo)?GBWhatsApp(?:/(\\d+[.\\d]+))?";s:4:"name";s:10:"GBWhatsApp";s:7:"version";s:2:"$1";}i:49;a:3:{s:5:"regex";s:29:"YoYoWhatsApp(?:/(\\d+[.\\d]+))?";s:4:"name";s:11:"Yo WhatsApp";s:7:"version";s:2:"$1";}i:50;a:3:{s:5:"regex";s:27:"ANWhatsApp(?:/(\\d+[.\\d]+))?";s:4:"name";s:11:"AN WhatsApp";s:7:"version";s:2:"$1";}i:51;a:3:{s:5:"regex";s:30:"Telegram/(\\d+[.\\d]+) CFNetwork";s:4:"name";s:8:"Telegram";s:7:"version";s:0:"";}i:52;a:3:{s:5:"regex";s:80:"(?:^com\\.google\\.android\\.apps\\.youtube\\.music/|^YouTubeMusic(?:Dev)?/)([\\d.]+)?";s:4:"name";s:13:"Youtube Music";s:7:"version";s:2:"$1";}i:53;a:3:{s:5:"regex";s:20:"Line(?:[ /]([\\d.]+))";s:4:"name";s:4:"Line";s:7:"version";s:2:"$1";}i:54;a:3:{s:5:"regex";s:64:"Instacast(?:HD)?/([\\d\\.abc]+) CFNetwork/([\\d.]+) Darwin/([\\d.]+)";s:4:"name";s:9:"Instacast";s:7:"version";s:2:"$1";}i:55;a:3:{s:5:"regex";s:44:"Pocket Casts(?:, (?:Android|iOS) v([\\d.]+))?";s:4:"name";s:12:"Pocket Casts";s:7:"version";s:2:"$1";}i:56;a:3:{s:5:"regex";s:15:"Podcat/([\\d.]+)";s:4:"name";s:6:"Podcat";s:7:"version";s:2:"$1";}i:57;a:3:{s:5:"regex";s:9:"BeyondPod";s:4:"name";s:9:"BeyondPod";s:7:"version";s:0:"";}i:58;a:3:{s:5:"regex";s:45:"(?:^Overcast/([\\d.]+)|^Overcast.*Apple Watch)";s:4:"name";s:8:"Overcast";s:7:"version";s:2:"$1";}i:59;a:3:{s:5:"regex";s:61:"(?:CastBox|fm\\.castbox\\.audiobook\\.radio\\.podcast)/?([\\d.]+)?";s:4:"name";s:7:"CastBox";s:7:"version";s:2:"$1";}i:60;a:3:{s:5:"regex";s:37:"Podkicker( (?:Pro|Classic))?/([\\d.]+)";s:4:"name";s:11:"Podkicker$1";s:7:"version";s:2:"$2";}i:61;a:3:{s:5:"regex";s:24:"PodcastRepublic/([\\d.]+)";s:4:"name";s:16:"Podcast Republic";s:7:"version";s:2:"$1";}i:62;a:3:{s:5:"regex";s:12:"Castro/(\\d+)";s:4:"name";s:6:"Castro";s:7:"version";s:2:"$1";}i:63;a:3:{s:5:"regex";s:35:"Castro 2 ([\\d.]+)/[\\d]+ Like iTunes";s:4:"name";s:8:"Castro 2";s:7:"version";s:2:"$1";}i:64;a:3:{s:5:"regex";s:8:"Castro 2";s:4:"name";s:8:"Castro 2";s:7:"version";s:0:"";}i:65;a:3:{s:5:"regex";s:11:"DoggCatcher";s:4:"name";s:11:"DoggCatcher";s:7:"version";s:0:"";}i:66;a:3:{s:5:"regex";s:42:"(?:PodcastAddict/v([\\d]+)|^Podcast Addict)";s:4:"name";s:22:"Podcast & Radio Addict";s:7:"version";s:2:"$1";}i:67;a:3:{s:5:"regex";s:33:"Podcat(?:%202)?/([\\d]+) CFNetwork";s:4:"name";s:6:"Podcat";s:7:"version";s:2:"$1";}i:68;a:3:{s:5:"regex";s:22:"iCatcher[^\\d]+([\\d.]+)";s:4:"name";s:8:"iCatcher";s:7:"version";s:2:"$1";}i:69;a:3:{s:5:"regex";s:16:"YelpApp/([\\d.]+)";s:4:"name";s:11:"Yelp Mobile";s:7:"version";s:2:"$1";}i:70;a:3:{s:5:"regex";s:53:"jp\\.co\\.yahoo\\.(?:android\\.yjtop|ipn\\.appli)/([\\d.]+)";s:4:"name";s:12:"Yahoo! Japan";s:7:"version";s:2:"$1";}i:71;a:3:{s:5:"regex";s:17:"RSSRadio/([\\d]+)?";s:4:"name";s:8:"RSSRadio";s:7:"version";s:2:"$1";}i:72;a:3:{s:5:"regex";s:42:"SogouSearch Android[\\d.]+ version([\\d.]+)?";s:4:"name";s:15:"SogouSearch App";s:7:"version";s:2:"$1";}i:73;a:3:{s:5:"regex";s:21:"NewsArticle/([\\d.]+)?";s:4:"name";s:15:"NewsArticle App";s:7:"version";s:2:"$1";}i:74;a:3:{s:5:"regex";s:15:"tieba/([\\d.]+)?";s:4:"name";s:5:"tieba";s:7:"version";s:2:"$1";}i:75;a:3:{s:5:"regex";s:28:"com\\.douban\\.group/([\\d.]+)?";s:4:"name";s:10:"douban App";s:7:"version";s:2:"$1";}i:76;a:3:{s:5:"regex";s:53:"(?:com\\.google\\.GoogleMobile|GSA|GoogleApp)/([\\d.]+)?";s:4:"name";s:17:"Google Search App";s:7:"version";s:2:"$1";}i:77;a:3:{s:5:"regex";s:29:"Google/(\\d+[.\\d]+)? CFNetwork";s:4:"name";s:17:"Google Search App";s:7:"version";s:2:"$1";}i:78;a:3:{s:5:"regex";s:38:"(?:Google|SearchWith)Lens/(\\d+[.\\d]+)?";s:4:"name";s:11:"Google Lens";s:7:"version";s:2:"$1";}i:79;a:3:{s:5:"regex";s:19:"Flipboard/([\\d.]+)?";s:4:"name";s:13:"Flipboard App";s:7:"version";s:2:"$1";}i:80;a:3:{s:5:"regex";s:21:"baiduboxapp/([\\d.]+)?";s:4:"name";s:13:"Baidu Box App";s:7:"version";s:2:"$1";}i:81;a:3:{s:5:"regex";s:20:"baiduinput/([\\d.]+)?";s:4:"name";s:11:"Baidu Input";s:7:"version";s:2:"$1";}i:82;a:3:{s:5:"regex";s:21:"PetalSearch/([\\d.]+)?";s:4:"name";s:12:"Petal Search";s:7:"version";s:2:"$1";}i:83;a:3:{s:5:"regex";s:48:"Crosswalk(?!.*(?:Streamy|QwantMobile))/([\\d.]+)?";s:4:"name";s:12:"CrosswalkApp";s:7:"version";s:2:"$1";}i:84;a:3:{s:5:"regex";s:31:"Twitter for iPhone[/]?([\\d.]+)?";s:4:"name";s:7:"Twitter";s:7:"version";s:2:"$1";}i:85;a:3:{s:5:"regex";s:16:"Twitter/([\\d.]+)";s:4:"name";s:7:"Twitter";s:7:"version";s:2:"$1";}i:86;a:3:{s:5:"regex";s:27:"TwitterAndroid[/]?([\\d.]+)?";s:4:"name";s:7:"Twitter";s:7:"version";s:2:"$1";}i:87;a:3:{s:5:"regex";s:13:"^Pocket Casts";s:4:"name";s:12:"Pocket Casts";s:7:"version";s:0:"";}i:88;a:3:{s:5:"regex";s:46:"(?:^GaanaAndroid-|^Gaana-iOS|^Gaana/)([\\d.]+)?";s:4:"name";s:5:"Gaana";s:7:"version";s:2:"$1";}i:89;a:3:{s:5:"regex";s:16:"TopBuzz/([\\d.]+)";s:4:"name";s:7:"TopBuzz";s:7:"version";s:2:"$1";}i:90;a:3:{s:5:"regex";s:36:"(?:Safari/[\\d.]+)?Snapchat/?([\\d.]+)";s:4:"name";s:8:"Snapchat";s:7:"version";s:2:"$1";}i:91;a:3:{s:5:"regex";s:20:"CronetSnapDevSheldon";s:4:"name";s:8:"Snapchat";s:7:"version";s:0:"";}i:92;a:3:{s:5:"regex";s:18:"AhaRadio2/([\\d.]+)";s:4:"name";s:11:"Aha Radio 2";s:7:"version";s:2:"$1";}i:93;a:3:{s:5:"regex";s:15:"Unibox/([\\d.]+)";s:4:"name";s:6:"Unibox";s:7:"version";s:0:"";}i:94;a:3:{s:5:"regex";s:38:"strimio(?:-desktop)/(\\d+\\.(?:[.\\d]+))?";s:4:"name";s:7:"Strimio";s:7:"version";s:2:"$1";}i:95;a:3:{s:5:"regex";s:20:"UnityPlayer/([\\d.]+)";s:4:"name";s:11:"UnityPlayer";s:7:"version";s:2:"$1";}i:96;a:3:{s:5:"regex";s:17:"UCURSOS/v([\\d.]+)";s:4:"name";s:8:"U-Cursos";s:7:"version";s:2:"$1";}i:97;a:3:{s:5:"regex";s:22:"HeyTapBrowser/([\\d.]+)";s:4:"name";s:13:"HeyTapBrowser";s:7:"version";s:2:"$1";}i:98;a:3:{s:5:"regex";s:18:"RobloxApp/([\\d.]+)";s:4:"name";s:6:"Roblox";s:7:"version";s:2:"$1";}i:99;a:3:{s:5:"regex";s:22:"Viber(?:/(\\d+[.\\d]+))?";s:4:"name";s:5:"Viber";s:7:"version";s:2:"$1";}i:100;a:3:{s:5:"regex";s:6:"Siri/1";s:4:"name";s:4:"Siri";s:7:"version";s:3:"1.0";}i:101;a:3:{s:5:"regex";s:33:"LinkedIn(?:App)?(?:\\]?/([\\d.]+))?";s:4:"name";s:8:"LinkedIn";s:7:"version";s:2:"$1";}i:102;a:3:{s:5:"regex";s:19:"Instapaper/([\\d.]+)";s:4:"name";s:10:"Instapaper";s:7:"version";s:2:"$1";}i:103;a:3:{s:5:"regex";s:15:"Keeper/([\\d.]+)";s:4:"name";s:23:"Keeper Password Manager";s:7:"version";s:2:"$1";}i:104;a:3:{s:5:"regex";s:24:"Skyeng Teachers/([\\d.]+)";s:4:"name";s:15:"Skyeng Teachers";s:7:"version";s:2:"$1";}i:105;a:3:{s:5:"regex";s:22:"Kik/([\\d.]+) \\(Android";s:4:"name";s:3:"Kik";s:7:"version";s:2:"$1";}i:106;a:3:{s:5:"regex";s:18:"Procast/?([\\d.]+)?";s:4:"name";s:7:"Procast";s:7:"version";s:2:"$1";}i:107;a:3:{s:5:"regex";s:19:"DeviantArt/([\\d.]+)";s:4:"name";s:10:"DeviantArt";s:7:"version";s:0:"";}i:108;a:3:{s:5:"regex";s:26:"discord/([\\d.]+).+Electron";s:4:"name";s:7:"Discord";s:7:"version";s:2:"$1";}i:109;a:3:{s:5:"regex";s:29:"discord(?:-Updater)?/([\\d.]+)";s:4:"name";s:7:"Discord";s:7:"version";s:0:"";}i:110;a:3:{s:5:"regex";s:24:"Covenant%20Eyes/([\\d.]+)";s:4:"name";s:13:"Covenant Eyes";s:7:"version";s:2:"$1";}i:111;a:3:{s:5:"regex";s:19:"HP%20Smart/([\\d.]+)";s:4:"name";s:8:"HP Smart";s:7:"version";s:0:"";}i:112;a:3:{s:5:"regex";s:18:"Bitsboard/([\\d.]+)";s:4:"name";s:9:"Bitsboard";s:7:"version";s:2:"$1";}i:113;a:3:{s:5:"regex";s:16:"Betbull/([\\d.]+)";s:4:"name";s:7:"BetBull";s:7:"version";s:0:"";}i:114;a:3:{s:5:"regex";s:17:"U-Cursos/([\\d.]+)";s:4:"name";s:8:"U-Cursos";s:7:"version";s:0:"";}i:115;a:3:{s:5:"regex";s:24:"1PasswordThumbs/([\\d.]+)";s:4:"name";s:9:"1Password";s:7:"version";s:2:"$1";}i:116;a:3:{s:5:"regex";s:139:"(?:Microsoft Office )?(Access|Excel|OneDrive for Business|OneNote|PowerPoint|Project|Publisher|Visio|Word)(?: 20\\d{2})?[ /]\\(?(\\d+\\.[\\d.]*)";s:4:"name";s:19:"Microsoft Office $1";s:7:"version";s:2:"$2";}i:117;a:3:{s:5:"regex";s:59:"^Mozilla/4\\.0 \\(compatible; ms-office; MSOffice[ /]([\\d.]+)";s:4:"name";s:16:"Microsoft Office";s:7:"version";s:2:"$1";}i:118;a:3:{s:5:"regex";s:34:"Microsoft Office SyncProc ([\\d.]+)";s:4:"name";s:16:"Microsoft Office";s:7:"version";s:2:"$1";}i:119;a:3:{s:5:"regex";s:23:"Microsoft Lync ([\\d.]+)";s:4:"name";s:14:"Microsoft Lync";s:7:"version";s:2:"$1";}i:120;a:3:{s:5:"regex";s:23:"Microsoft\\.Data\\.Mashup";s:4:"name";s:21:"Microsoft Power Query";s:7:"version";s:0:"";}i:121;a:3:{s:5:"regex";s:20:"WpsM?office/([\\d.]+)";s:4:"name";s:10:"WPS Office";s:7:"version";s:2:"$1";}i:122;a:3:{s:5:"regex";s:23:"OneDriveiOSApp/([\\d.]+)";s:4:"name";s:18:"Microsoft OneDrive";s:7:"version";s:2:"$1";}i:123;a:3:{s:5:"regex";s:36:"Microsoft Office Existence Discovery";s:4:"name";s:16:"Microsoft Office";s:7:"version";s:0:"";}i:124;a:3:{s:5:"regex";s:52:"(?:Microsoft Office Mobile|officemobile)[ /]([\\d.]+)";s:4:"name";s:23:"Microsoft Office Mobile";s:7:"version";s:2:"$1";}i:125;a:3:{s:5:"regex";s:14:"Skype/([\\d.]+)";s:4:"name";s:5:"Skype";s:7:"version";s:2:"$1";}i:126;a:3:{s:5:"regex";s:34:"OC/([\\d.]+) \\(Skype for Business\\)";s:4:"name";s:18:"Skype for Business";s:7:"version";s:2:"$1";}i:127;a:3:{s:5:"regex";s:19:"iPhoneLync/([\\d.]+)";s:4:"name";s:18:"Skype for Business";s:7:"version";s:2:"$1";}i:128;a:3:{s:5:"regex";s:10:"WebexTeams";s:4:"name";s:11:"Webex Teams";s:7:"version";s:0:"";}i:129;a:3:{s:5:"regex";s:16:"GroupMe/([\\d.]+)";s:4:"name";s:7:"GroupMe";s:7:"version";s:2:"$1";}i:130;a:3:{s:5:"regex";s:49:"AppName/(?:musical_ly|trill) app_version/([\\d.]+)";s:4:"name";s:6:"TikTok";s:7:"version";s:2:"$1";}i:131;a:3:{s:5:"regex";s:78:"(?:TikTok[/ ]|com\\.zhiliaoapp\\.musically|musical_ly_|trill_)(\\d+\\.(?:[.\\d]+))?";s:4:"name";s:6:"TikTok";s:7:"version";s:2:"$1";}i:132;a:3:{s:5:"regex";s:47:"(?:musically_go|ultralite) app_version/([\\d.]+)";s:4:"name";s:11:"TikTok Lite";s:7:"version";s:2:"$1";}i:133;a:3:{s:5:"regex";s:31:"aweme(?: app_version)?/([\\d.]+)";s:4:"name";s:6:"Douyin";s:7:"version";s:2:"$1";}i:134;a:3:{s:5:"regex";s:28:"Copied/(\\d+[.\\d]+) CFNetwork";s:4:"name";s:6:"Copied";s:7:"version";s:0:"";}i:135;a:3:{s:5:"regex";s:35:"Pic%20Collage/(\\d+[.\\d]+) CFNetwork";s:4:"name";s:11:"Pic Collage";s:7:"version";s:2:"$1";}i:136;a:3:{s:5:"regex";s:28:"Papers/(\\d+[.\\d]+) CFNetwork";s:4:"name";s:6:"Papers";s:7:"version";s:2:"$1";}i:137;a:3:{s:5:"regex";s:30:"RoboForm/(\\d+[.\\d]+) CFNetwork";s:4:"name";s:8:"RoboForm";s:7:"version";s:0:"";}i:138;a:3:{s:5:"regex";s:27:"Slack/(\\d+[.\\d]+) CFNetwork";s:4:"name";s:5:"Slack";s:7:"version";s:0:"";}i:139;a:3:{s:5:"regex";s:36:"com\\.tinyspeck\\.chatlyio/(\\d+[.\\d]+)";s:4:"name";s:5:"Slack";s:7:"version";s:2:"$1";}i:140;a:3:{s:5:"regex";s:28:"KAKAOTALK (\\d+\\.(?:[.\\d]+))?";s:4:"name";s:9:"KakaoTalk";s:7:"version";s:2:"$1";}i:141;a:3:{s:5:"regex";s:17:"ShopeeVN/([\\d.]+)";s:4:"name";s:6:"Shopee";s:7:"version";s:2:"$1";}i:142;a:3:{s:5:"regex";s:15:"SPORT1/([\\d.]+)";s:4:"name";s:6:"SPORT1";s:7:"version";s:0:"";}i:143;a:3:{s:5:"regex";s:15:"Clovia/([\\d.]+)";s:4:"name";s:6:"Clovia";s:7:"version";s:2:"$1";}i:144;a:3:{s:5:"regex";s:15:"ShowMe/([\\d.]+)";s:4:"name";s:6:"ShowMe";s:7:"version";s:2:"$1";}i:145;a:3:{s:5:"regex";s:16:"Wattpad/([\\d.]+)";s:4:"name";s:7:"Wattpad";s:7:"version";s:2:"$1";}i:146;a:3:{s:5:"regex";s:12:"WSJ/([\\d.]+)";s:4:"name";s:23:"The Wall Street Journal";s:7:"version";s:0:"";}i:147;a:3:{s:5:"regex";s:23:"WH%20Questions/([\\d.]+)";s:4:"name";s:12:"WH Questions";s:7:"version";s:2:"$1";}i:148;a:3:{s:5:"regex";s:16:"whisper/([\\d.]+)";s:4:"name";s:7:"Whisper";s:7:"version";s:0:"";}i:149;a:3:{s:5:"regex";s:13:"Opal/([\\d.]+)";s:4:"name";s:11:"Opal Travel";s:7:"version";s:2:"$1";}i:150;a:3:{s:5:"regex";s:34:"Zalo/([\\d.]+)|Zalo (?:android|iOS)";s:4:"name";s:4:"Zalo";s:7:"version";s:2:"$1";}i:151;a:3:{s:5:"regex";s:15:"Yandex/([\\d.]+)";s:4:"name";s:6:"Yandex";s:7:"version";s:0:"";}i:152;a:3:{s:5:"regex";s:15:"ZenKit/([\\d.]+)";s:4:"name";s:3:"Zen";s:7:"version";s:2:"$1";}i:153;a:3:{s:5:"regex";s:20:"Zoho%20Chat/([\\d.]+)";s:4:"name";s:9:"Zoho Chat";s:7:"version";s:2:"$1";}i:154;a:3:{s:5:"regex";s:19:"Thunder/(\\d+[.\\d]+)";s:4:"name";s:7:"Thunder";s:7:"version";s:2:"$1";}i:155;a:3:{s:5:"regex";s:22:"CGNBrowser/(\\d+[.\\d]+)";s:4:"name";s:3:"CGN";s:7:"version";s:2:"$1";}i:156;a:3:{s:5:"regex";s:77:"(?:Podbean/.+App |Podbean/Android generic |Podbean/iOS \\([^)]+\\) )(\\d+[.\\d]+)";s:4:"name";s:7:"Podbean";s:7:"version";s:2:"$1";}i:157;a:3:{s:5:"regex";s:37:"TuneIn Radio Pro(?:[^/]*)/(\\d+[.\\d]+)";s:4:"name";s:16:"TuneIn Radio Pro";s:7:"version";s:2:"$1";}i:158;a:3:{s:5:"regex";s:48:"TuneIn(?:(?: |%20)Radio(?:[^/]*))?/?(\\d+[.\\d]+)?";s:4:"name";s:12:"TuneIn Radio";s:7:"version";s:2:"$1";}i:159;a:3:{s:5:"regex";s:20:"devcasts/(\\d+[.\\d]+)";s:4:"name";s:8:"DevCasts";s:7:"version";s:2:"$1";}i:160;a:3:{s:5:"regex";s:17:"Swoot/(\\d+[.\\d]+)";s:4:"name";s:5:"Swoot";s:7:"version";s:2:"$1";}i:161;a:3:{s:5:"regex";s:52:"(?:^RadioPublic[/ ](?:Android|iOS)[- ])(\\d+\\.[.\\d]+)";s:4:"name";s:11:"RadioPublic";s:7:"version";s:2:"$1";}i:162;a:3:{s:5:"regex";s:18:"Podimo/(\\d+[.\\d]+)";s:4:"name";s:6:"Podimo";s:7:"version";s:2:"$1";}i:163;a:3:{s:5:"regex";s:32:"com\\.evolve\\.podcast/(\\d+[.\\d]+)";s:4:"name";s:14:"Evolve Podcast";s:7:"version";s:2:"$1";}i:164;a:3:{s:5:"regex";s:27:"Rocket\\.Chat\\+?/(\\d+[.\\d]+)";s:4:"name";s:11:"Rocket Chat";s:7:"version";s:2:"$1";}i:165;a:3:{s:5:"regex";s:23:"^Pandora Audio.+Android";s:4:"name";s:7:"Pandora";s:7:"version";s:0:"";}i:166;a:3:{s:5:"regex";s:33:"^WirtschaftsWoche-iOS-(\\d+[.\\d]+)";s:4:"name";s:17:"Wirtschafts Woche";s:7:"version";s:2:"$1";}i:167;a:3:{s:5:"regex";s:18:"^TVirl/(\\d+[.\\d]+)";s:4:"name";s:5:"TVirl";s:7:"version";s:2:"$1";}i:168;a:3:{s:5:"regex";s:20:"2?chMate/(\\d+[.\\d]+)";s:4:"name";s:6:"ChMate";s:7:"version";s:2:"$1";}i:169;a:3:{s:5:"regex";s:16:"2tch/(\\d+[.\\d]+)";s:4:"name";s:4:"2tch";s:7:"version";s:2:"$1";}i:170;a:3:{s:5:"regex";s:18:"Ciisaa/(\\d+[.\\d]+)";s:4:"name";s:6:"Ciisaa";s:7:"version";s:2:"$1";}i:171;a:3:{s:5:"regex";s:16:"BB2C (\\d+[.\\d]+)";s:4:"name";s:4:"BB2C";s:7:"version";s:2:"$1";}i:172;a:3:{s:5:"regex";s:19:"twinkle/(\\d+[.\\d]+)";s:4:"name";s:7:"twinkle";s:7:"version";s:2:"$1";}i:173;a:3:{s:5:"regex";s:25:"JaneStyle_iOS/(\\d+[.\\d]+)";s:4:"name";s:9:"JaneStyle";s:7:"version";s:2:"$1";}i:174;a:3:{s:5:"regex";s:29:"BNC/.+\\(Android (\\d+[.\\d]+)\\)";s:4:"name";s:7:"Binance";s:7:"version";s:2:"$1";}i:175;a:3:{s:5:"regex";s:19:"Binance/(\\d+[.\\d]+)";s:4:"name";s:7:"Binance";s:7:"version";s:2:"$1";}i:176;a:3:{s:5:"regex";s:24:"ru\\.mail\\.my/(\\d+[.\\d]+)";s:4:"name";s:8:"My World";s:7:"version";s:2:"$1";}i:177;a:3:{s:5:"regex";s:26:"OK(?:Android|iOS)/([\\d.]+)";s:4:"name";s:13:"Odnoklassniki";s:7:"version";s:2:"$1";}i:178;a:3:{s:5:"regex";s:18:"yakyak/(\\d+[.\\d]+)";s:4:"name";s:6:"YakYak";s:7:"version";s:2:"$1";}i:179;a:3:{s:5:"regex";s:28:"(?:maglev|Teams)/(\\d+[.\\d]+)";s:4:"name";s:5:"Teams";s:7:"version";s:2:"$1";}i:180;a:3:{s:5:"regex";s:27:"TeamsMobile-(?:Android|iOS)";s:4:"name";s:5:"Teams";s:7:"version";s:0:"";}i:181;a:3:{s:5:"regex";s:22:"SohuNews/(\\d+\\.[.\\d]+)";s:4:"name";s:8:"SohuNews";s:7:"version";s:2:"$1";}i:182;a:3:{s:5:"regex";s:25:"StreamlabsOBS/(\\d+[.\\d]+)";s:4:"name";s:14:"Streamlabs OBS";s:7:"version";s:2:"$1";}i:183;a:3:{s:5:"regex";s:14:"Blitz/([\\d.]+)";s:4:"name";s:5:"Blitz";s:7:"version";s:2:"$1";}i:184;a:3:{s:5:"regex";s:16:"OfferUp/([\\d.]+)";s:4:"name";s:7:"OfferUp";s:7:"version";s:2:"$1";}i:185;a:3:{s:5:"regex";s:14:"Vuhuv/([\\d.]+)";s:4:"name";s:5:"Vuhuv";s:7:"version";s:2:"$1";}i:186;a:3:{s:5:"regex";s:32:".+/(?:gfibertv|gftv200)-([\\d]+)-";s:4:"name";s:15:"Google Fiber TV";s:7:"version";s:2:"$1";}i:187;a:3:{s:5:"regex";s:10:"QuickCast$";s:4:"name";s:9:"QuickCast";s:7:"version";s:0:"";}i:188;a:3:{s:5:"regex";s:31:"Aliexpress(?:Android)?/([\\d.]+)";s:4:"name";s:10:"AliExpress";s:7:"version";s:2:"$1";}i:189;a:3:{s:5:"regex";s:47:"(?:lazada_android|AliApp\\(LA)[/ _](\\d+\\.[\\d.]+)";s:4:"name";s:6:"Lazada";s:7:"version";s:2:"$1";}i:190;a:3:{s:5:"regex";s:47:"(?:taobao_android|AliApp\\(TB)[/ _](\\d+\\.[\\d.]+)";s:4:"name";s:6:"Taobao";s:7:"version";s:2:"$1";}i:191;a:3:{s:5:"regex";s:45:"(?:AlipayClient|AliApp\\(AP)[/ _](\\d+\\.[\\d.]+)";s:4:"name";s:6:"Alipay";s:7:"version";s:2:"$1";}i:192;a:3:{s:5:"regex";s:19:"Blue Proxy/([\\d.]+)";s:4:"name";s:10:"Blue Proxy";s:7:"version";s:2:"$1";}i:193;a:3:{s:5:"regex";s:9:"ntvmobil/";s:4:"name";s:9:"NTV Mobil";s:7:"version";s:0:"";}i:194;a:3:{s:5:"regex";s:23:"COAF%20SMART%20Citizen/";s:4:"name";s:18:"COAF SMART Citizen";s:7:"version";s:0:"";}i:195;a:3:{s:5:"regex";s:24:"GitHub ?Desktop/([\\d.]+)";s:4:"name";s:14:"GitHub Desktop";s:7:"version";s:2:"$1";}i:196;a:3:{s:5:"regex";s:24:"logioptionsplus/([\\d.]+)";s:4:"name";s:13:"Logi Options+";s:7:"version";s:2:"$1";}i:197;a:3:{s:5:"regex";s:20:"EmbyTheater/([\\d.]+)";s:4:"name";s:12:"Emby Theater";s:7:"version";s:2:"$1";}i:198;a:3:{s:5:"regex";s:19:"y8-browser/([\\d.]+)";s:4:"name";s:10:"Y8 Browser";s:7:"version";s:2:"$1";}i:199;a:3:{s:5:"regex";s:22:"NuMuKiBrowser/([\\d.]+)";s:4:"name";s:14:"NuMuKi Browser";s:7:"version";s:2:"$1";}i:200;a:3:{s:5:"regex";s:31:"LandisGyrAIMbrowser/(\\d+[.\\d]+)";s:4:"name";s:22:"Landis+Gyr AIM Browser";s:7:"version";s:2:"$1";}i:201;a:3:{s:5:"regex";s:28:"(?:Code/|VSCode )(\\d+[.\\d]+)";s:4:"name";s:18:"Visual Studio Code";s:7:"version";s:2:"$1";}i:202;a:3:{s:5:"regex";s:21:"Wireshark/(\\d+[.\\d]+)";s:4:"name";s:9:"Wireshark";s:7:"version";s:2:"$1";}i:203;a:3:{s:5:"regex";s:8:"Magician";s:4:"name";s:16:"Samsung Magician";s:7:"version";s:0:"";}i:204;a:3:{s:5:"regex";s:16:"Razer Central PC";s:4:"name";s:13:"Razer Synapse";s:7:"version";s:0:"";}i:205;a:3:{s:5:"regex";s:15:"git/(\\d+[.\\d]+)";s:4:"name";s:3:"Git";s:7:"version";s:2:"$1";}i:206;a:3:{s:5:"regex";s:29:"^GooglePodcasts/(\\d+\\.[.\\d]+)";s:4:"name";s:15:"Google Podcasts";s:7:"version";s:2:"$1";}i:207;a:3:{s:5:"regex";s:31:"Microsoft-CryptoAPI/(\\d+[.\\d]+)";s:4:"name";s:17:"Windows CryptoAPI";s:7:"version";s:0:"";}i:208;a:3:{s:5:"regex";s:31:"Microsoft-Delivery-Optimization";s:4:"name";s:29:"Windows Delivery Optimization";s:7:"version";s:0:"";}i:209;a:3:{s:5:"regex";s:20:"Windows-Update-Agent";s:4:"name";s:20:"Windows Update Agent";s:7:"version";s:0:"";}i:210;a:3:{s:5:"regex";s:5:"^MSDW";s:4:"name";s:10:"Dr. Watson";s:7:"version";s:0:"";}i:211;a:3:{s:5:"regex";s:23:"qBittorrent/(\\d+[.\\d]+)";s:4:"name";s:11:"qBittorrent";s:7:"version";s:2:"$1";}i:212;a:3:{s:5:"regex";s:6:"^CPUID";s:4:"name";s:5:"CPU-Z";s:7:"version";s:0:"";}i:213;a:3:{s:5:"regex";s:6:"AIDA64";s:4:"name";s:6:"AIDA64";s:7:"version";s:0:"";}i:214;a:3:{s:5:"regex";s:29:"HandBrake Win Upd (\\d+[.\\d]+)";s:4:"name";s:9:"HandBrake";s:7:"version";s:2:"$1";}i:215;a:3:{s:5:"regex";s:21:"CCleaner, (\\d+[.\\d]+)";s:4:"name";s:8:"CCleaner";s:7:"version";s:2:"$1";}i:216;a:3:{s:5:"regex";s:33:"Microsoft Edge Update/(\\d+[.\\d]+)";s:4:"name";s:11:"Edge Update";s:7:"version";s:2:"$1";}i:217;a:3:{s:5:"regex";s:38:"Google(?:Software| )Update/(\\d+[.\\d]+)";s:4:"name";s:13:"Chrome Update";s:7:"version";s:2:"$1";}i:218;a:3:{s:5:"regex";s:10:"Bose Music";s:4:"name";s:10:"Bose Music";s:7:"version";s:0:"";}i:219;a:3:{s:5:"regex";s:10:"HikConnect";s:4:"name";s:11:"Hik-Connect";s:7:"version";s:0:"";}i:220;a:3:{s:5:"regex";s:19:"Cortana (\\d+[.\\d]+)";s:4:"name";s:7:"Cortana";s:7:"version";s:2:"$1";}i:221;a:3:{s:5:"regex";s:22:"Opera News/(\\d+[.\\d]+)";s:4:"name";s:10:"Opera News";s:7:"version";s:2:"$1";}i:222;a:3:{s:5:"regex";s:38:"(?:Acrobat|ReaderServices)/(\\d+[.\\d]+)";s:4:"name";s:20:"Adobe Acrobat Reader";s:7:"version";s:2:"$1";}i:223;a:3:{s:5:"regex";s:25:"CreativeCloud/(\\d+[.\\d]+)";s:4:"name";s:20:"Adobe Creative Cloud";s:7:"version";s:2:"$1";}i:224;a:3:{s:5:"regex";s:21:"rekordbox/(\\d+[.\\d]+)";s:4:"name";s:9:"rekordbox";s:7:"version";s:2:"$1";}i:225;a:3:{s:5:"regex";s:25:"Microsoft-WNS/(\\d+[.\\d]+)";s:4:"name";s:34:"Windows Push Notification Services";s:7:"version";s:2:"$1";}i:226;a:3:{s:5:"regex";s:26:"Microsoft BITS/(\\d+[.\\d]+)";s:4:"name";s:39:"Background Intelligent Transfer Service";s:7:"version";s:2:"$1";}i:227;a:3:{s:5:"regex";s:16:"ERA Agent Update";s:4:"name";s:25:"ESET Remote Administrator";s:7:"version";s:0:"";}i:228;a:3:{s:5:"regex";s:29:"EpicGamesLauncher/(\\d+[.\\d]+)";s:4:"name";s:19:"Epic Games Launcher";s:7:"version";s:2:"$1";}i:229;a:3:{s:5:"regex";s:26:"Microsoft-WebDAV-MiniRedir";s:4:"name";s:6:"WebDAV";s:7:"version";s:0:"";}i:230;a:3:{s:5:"regex";s:23:"Battle\\.net/(\\d+[.\\d]+)";s:4:"name";s:10:"Battle.net";s:7:"version";s:2:"$1";}i:231;a:3:{s:5:"regex";s:29:"Bookshelf-Android/(\\d+[.\\d]+)";s:4:"name";s:9:"Bookshelf";s:7:"version";s:2:"$1";}i:232;a:3:{s:5:"regex";s:22:"RaveSocial/(\\d+[.\\d]+)";s:4:"name";s:11:"Rave Social";s:7:"version";s:2:"$1";}i:233;a:3:{s:5:"regex";s:23:"wordcookies/(\\d+[.\\d]+)";s:4:"name";s:13:"Word Cookies!";s:7:"version";s:2:"$1";}i:234;a:3:{s:5:"regex";s:33:"com\\.meevii\\.bibleKJV/(\\d+[.\\d]+)";s:4:"name";s:9:"Bible KJV";s:7:"version";s:2:"$1";}i:235;a:3:{s:5:"regex";s:33:"MetaTrader 5 Terminal/(\\d+[.\\d]+)";s:4:"name";s:10:"MetaTrader";s:7:"version";s:2:"$1";}i:236;a:3:{s:5:"regex";s:32:"com\\.paint\\.bynumber/(\\d+[.\\d]+)";s:4:"name";s:15:"Paint by Number";s:7:"version";s:2:"$1";}i:237;a:3:{s:5:"regex";s:25:"zepeto_global/(\\d+[.\\d]+)";s:4:"name";s:6:"ZEPETO";s:7:"version";s:2:"$1";}i:238;a:3:{s:5:"regex";s:26:"Jungle Disk Workgroup HTTP";s:4:"name";s:11:"Jungle Disk";s:7:"version";s:0:"";}i:239;a:3:{s:5:"regex";s:40:"(?:mirall|Nextcloud-android)/(\\d+[.\\d]+)";s:4:"name";s:9:"Nextcloud";s:7:"version";s:2:"$1";}i:240;a:3:{s:5:"regex";s:23:"GoNativeIOS/(\\d+[.\\d]+)";s:4:"name";s:8:"GoNative";s:7:"version";s:2:"$1";}i:241;a:3:{s:5:"regex";s:19:"Pandora/(\\d+[.\\d]+)";s:4:"name";s:7:"Pandora";s:7:"version";s:2:"$1";}i:242;a:3:{s:5:"regex";s:23:"Blackboard/(\\d+[.\\d]+)?";s:4:"name";s:10:"Blackboard";s:7:"version";s:2:"$1";}i:243;a:3:{s:5:"regex";s:15:"TIM/(\\d+[.\\d]+)";s:4:"name";s:3:"TIM";s:7:"version";s:2:"$1";}i:244;a:3:{s:5:"regex";s:23:"TencentDocs/(\\d+[.\\d]+)";s:4:"name";s:12:"Tencent Docs";s:7:"version";s:2:"$1";}i:245;a:3:{s:5:"regex";s:25:"QQ/(\\d+[.\\d]+) V1_IPH_SQ_";s:4:"name";s:2:"QQ";s:7:"version";s:2:"$1";}i:246;a:3:{s:5:"regex";s:19:"QQMusic/(\\d+[.\\d]+)";s:4:"name";s:7:"QQMusic";s:7:"version";s:2:"$1";}i:247;a:3:{s:5:"regex";s:17:"etoro-cordova-app";s:4:"name";s:5:"eToro";s:7:"version";s:0:"";}i:248;a:3:{s:5:"regex";s:33:"Avid Link Desktop App/(\\d+[.\\d]+)";s:4:"name";s:9:"Avid Link";s:7:"version";s:2:"$1";}i:249;a:3:{s:5:"regex";s:19:"Netflix/(\\d+[.\\d]+)";s:4:"name";s:7:"Netflix";s:7:"version";s:2:"$1";}i:250;a:3:{s:5:"regex";s:28:"GoogleTagManager/(\\d+[.\\d]+)";s:4:"name";s:18:"Google Tag Manager";s:7:"version";s:2:"$1";}i:251;a:3:{s:5:"regex";s:30:"Adobe Synchronizer (\\d+[.\\d]+)";s:4:"name";s:18:"Adobe Synchronizer";s:7:"version";s:2:"$1";}i:252;a:3:{s:5:"regex";s:29:"BlueStacks(?: 5)?/(\\d+[.\\d]+)";s:4:"name";s:10:"BlueStacks";s:7:"version";s:2:"$1";}i:253;a:3:{s:5:"regex";s:29:"WindowsPowerShell/(\\d+[.\\d]+)";s:4:"name";s:10:"PowerShell";s:7:"version";s:2:"$1";}i:254;a:3:{s:5:"regex";s:29:"PAN GlobalProtect/(\\d+[.\\d]+)";s:4:"name";s:13:"GlobalProtect";s:7:"version";s:2:"$1";}i:255;a:3:{s:5:"regex";s:19:"Theyub v(\\d+[.\\d]+)";s:4:"name";s:6:"Theyub";s:7:"version";s:2:"$1";}i:256;a:3:{s:5:"regex";s:29:"BBCNewsUKWatchApp/(\\d+[.\\d]+)";s:4:"name";s:8:"BBC News";s:7:"version";s:2:"$1";}i:257;a:3:{s:5:"regex";s:23:"TradingView/(\\d+[.\\d]+)";s:4:"name";s:11:"TradingView";s:7:"version";s:2:"$1";}i:258;a:3:{s:5:"regex";s:25:"Instabridge(?:/([\\d.]+))?";s:4:"name";s:11:"Instabridge";s:7:"version";s:2:"$1";}i:259;a:3:{s:5:"regex";s:25:"Be Focused/(\\d+\\.[.\\d]+)?";s:4:"name";s:10:"Be Focused";s:7:"version";s:2:"$1";}i:260;a:3:{s:5:"regex";s:27:"Focus Matrix/(\\d+\\.[.\\d]+)?";s:4:"name";s:12:"Focus Matrix";s:7:"version";s:2:"$1";}i:261;a:3:{s:5:"regex";s:26:"Focuskeeper/(\\d+\\.[.\\d]+)?";s:4:"name";s:12:"Focus Keeper";s:7:"version";s:2:"$1";}i:262;a:3:{s:5:"regex";s:15:"WindowsStoreSDK";s:4:"name";s:15:"Microsoft Store";s:7:"version";s:0:"";}i:263;a:3:{s:5:"regex";s:25:"Asus Update/(\\d+\\.[.\\d]+)";s:4:"name";s:12:"ASUS Updater";s:7:"version";s:2:"$1";}i:264;a:3:{s:5:"regex";s:28:"imoAndroid/(20\\d{2}\\.[.\\d]+)";s:4:"name";s:25:"IMO HD Video Calls & Chat";s:7:"version";s:2:"$1";}i:265;a:3:{s:5:"regex";s:24:"imoAndroid/(\\d+\\.[.\\d]+)";s:4:"name";s:30:"IMO International Calls & Chat";s:7:"version";s:2:"$1";}i:266;a:3:{s:5:"regex";s:31:"(?:Bing)?Sapphire/(\\d+\\.[.\\d]+)";s:4:"name";s:14:"Microsoft Bing";s:7:"version";s:2:"$1";}i:267;a:3:{s:5:"regex";s:36:"BingWeb(?:/([\\d.]+))?|bingipadclient";s:4:"name";s:14:"Microsoft Bing";s:7:"version";s:2:"$1";}i:268;a:3:{s:5:"regex";s:26:"NewsSapphire/(\\d+\\.[.\\d]+)";s:4:"name";s:15:"Microsoft Start";s:7:"version";s:2:"$1";}i:269;a:3:{s:5:"regex";s:29:"CopilotSapphire/(\\d+\\.[.\\d]+)";s:4:"name";s:17:"Microsoft Copilot";s:7:"version";s:2:"$1";}i:270;a:3:{s:5:"regex";s:24:".+HiSearch/(\\d+\\.[.\\d]+)";s:4:"name";s:8:"HiSearch";s:7:"version";s:2:"$1";}i:271;a:3:{s:5:"regex";s:25:"RDDocuments/(\\d+\\.[.\\d]+)";s:4:"name";s:11:"RDDocuments";s:7:"version";s:2:"$1";}i:272;a:3:{s:5:"regex";s:26:"FSCDCSafe[ /](\\d+\\.[.\\d]+)";s:4:"name";s:13:"F-Secure SAFE";s:7:"version";s:2:"$1";}i:273;a:3:{s:5:"regex";s:12:"Twitterrific";s:4:"name";s:12:"Twitterrific";s:7:"version";s:0:"";}i:274;a:3:{s:5:"regex";s:12:"UconnectLive";s:4:"name";s:13:"Uconnect LIVE";s:7:"version";s:0:"";}i:275;a:3:{s:5:"regex";s:29:"Wayback%20Machine%20Extension";s:4:"name";s:15:"Wayback Machine";s:7:"version";s:0:"";}i:276;a:3:{s:5:"regex";s:46:"com\\.Nanoteq\\.QmunicateH10p.+/(\\d+\\.[.\\d]+) \\(";s:4:"name";s:10:"Q-municate";s:7:"version";s:2:"$1";}i:277;a:3:{s:5:"regex";s:9:"NET\\.mede";s:4:"name";s:8:"NET.mede";s:7:"version";s:0:"";}i:278;a:3:{s:5:"regex";s:12:"My%20Bentley";s:4:"name";s:10:"My Bentley";s:7:"version";s:0:"";}i:279;a:3:{s:5:"regex";s:12:"Skyeng%20App";s:4:"name";s:6:"Skyeng";s:7:"version";s:0:"";}i:280;a:3:{s:5:"regex";s:17:"Skyeng%20Teachers";s:4:"name";s:15:"Skyeng Teachers";s:7:"version";s:0:"";}i:281;a:3:{s:5:"regex";s:31:"(Millennium/|Millennium%20Corp)";s:4:"name";s:14:"Bank Millenium";s:7:"version";s:0:"";}i:282;a:3:{s:5:"regex";s:6:"MBolsa";s:4:"name";s:6:"MBolsa";s:7:"version";s:0:"";}i:283;a:3:{s:5:"regex";s:33:"(MEmpresas|Millennium%20Empresas)";s:4:"name";s:9:"MEmpresas";s:7:"version";s:0:"";}i:284;a:3:{s:5:"regex";s:25:"OrangeRadio/(\\d+\\.[.\\d]+)";s:4:"name";s:12:"Orange Radio";s:7:"version";s:2:"$1";}i:285;a:3:{s:5:"regex";s:30:"Radio%20Italiane/(\\d+\\.[.\\d]+)";s:4:"name";s:14:"Radio Italiane";s:7:"version";s:2:"$1";}i:286;a:3:{s:5:"regex";s:46:"com\\.apple\\.Safari\\.SearchHelper/(\\d+\\.[.\\d]+)";s:4:"name";s:20:"Safari Search Helper";s:7:"version";s:2:"$1";}i:287;a:3:{s:5:"regex";s:15:"Citrix%20Viewer";s:4:"name";s:16:"Citrix Workspace";s:7:"version";s:0:"";}i:288;a:3:{s:5:"regex";s:27:"com\\.mercbank\\.s1mobileipad";s:4:"name";s:27:"Mercantile Bank of Michigan";s:7:"version";s:0:"";}i:289;a:3:{s:5:"regex";s:14:"D-Stream%20Air";s:4:"name";s:11:"DStream Air";s:7:"version";s:0:"";}i:290;a:3:{s:5:"regex";s:15:"ExpediaBookings";s:4:"name";s:7:"Expedia";s:7:"version";s:0:"";}i:291;a:3:{s:5:"regex";s:31:"Windows Antivirus (\\d+\\.[.\\d]+)";s:4:"name";s:17:"Windows Antivirus";s:7:"version";s:2:"$1";}i:292;a:3:{s:5:"regex";s:8:"^Reflect";s:4:"name";s:15:"Macrium Reflect";s:7:"version";s:0:"";}i:293;a:3:{s:5:"regex";s:22:"Opera autoupdate agent";s:4:"name";s:13:"Opera Updater";s:7:"version";s:0:"";}i:294;a:3:{s:5:"regex";s:19:"Ballz/(\\d+\\.[.\\d]+)";s:4:"name";s:5:"Ballz";s:7:"version";s:2:"$1";}i:295;a:3:{s:5:"regex";s:31:"rnps-action-cards/(\\d+\\.[.\\d]+)";s:4:"name";s:17:"RNPS Action Cards";s:7:"version";s:2:"$1";}i:296;a:3:{s:5:"regex";s:29:"PlexMediaServer/(\\d+\\.[.\\d]+)";s:4:"name";s:17:"Plex Media Server";s:7:"version";s:2:"$1";}i:297;a:3:{s:5:"regex";s:10:"FreeSafeIP";s:4:"name";s:6:"SafeIP";s:7:"version";s:0:"";}i:298;a:3:{s:5:"regex";s:30:"Surfshark(?:Android)?/([\\d.]+)";s:4:"name";s:9:"Surfshark";s:7:"version";s:2:"$1";}i:299;a:3:{s:5:"regex";s:29:"APP/yym-hago-and(\\d+\\.[.\\d]+)";s:4:"name";s:4:"Hago";s:7:"version";s:2:"$1";}i:300;a:3:{s:5:"regex";s:21:"Azureus (\\d+\\.[.\\d]+)";s:4:"name";s:4:"Vuze";s:7:"version";s:2:"$1";}i:301;a:3:{s:5:"regex";s:3:"IPM";s:4:"name";s:9:"Adobe IPM";s:7:"version";s:0:"";}i:302;a:3:{s:5:"regex";s:34:"Adobe NGL|NGL Client/(\\d+\\.[.\\d]+)";s:4:"name";s:9:"Adobe NGL";s:7:"version";s:2:"$1";}i:303;a:3:{s:5:"regex";s:23:"/Satoshi:(\\d+\\.[.\\d]+)/";s:4:"name";s:12:"Bitcoin Core";s:7:"version";s:2:"$1";}i:304;a:3:{s:5:"regex";s:26:"/Shibetoshi:(\\d+\\.[.\\d]+)/";s:4:"name";s:13:"Dogecoin Core";s:7:"version";s:2:"$1";}i:305;a:3:{s:5:"regex";s:25:"Amazon\\.com/(\\d+\\.[.\\d]+)";s:4:"name";s:15:"Amazon Shopping";s:7:"version";s:2:"$1";}i:306;a:3:{s:5:"regex";s:38:"de\\.mobile\\.android\\.app/(\\d+\\.[.\\d]+)";s:4:"name";s:9:"mobile.de";s:7:"version";s:2:"$1";}i:307;a:3:{s:5:"regex";s:47:"de\\.mobile\\.android\\.app/(.*) \\((\\d+\\.[.\\d]+)\\)";s:4:"name";s:9:"mobile.de";s:7:"version";s:2:"$2";}i:308;a:3:{s:5:"regex";s:11:"jitsi-meet/";s:4:"name";s:10:"Jitsi Meet";s:7:"version";s:0:"";}i:309;a:3:{s:5:"regex";s:38:"Waste My Time! Extension/(\\d+\\.[.\\d]+)";s:4:"name";s:20:"Don\'t Waste My Time!";s:7:"version";s:2:"$1";}i:310;a:3:{s:5:"regex";s:23:"1Password/(\\d+\\.[.\\d]+)";s:4:"name";s:9:"1Password";s:7:"version";s:2:"$1";}i:311;a:3:{s:5:"regex";s:16:"iOSStartsidenApp";s:4:"name";s:10:"Startsiden";s:7:"version";s:0:"";}i:312;a:3:{s:5:"regex";s:12:"HisThumbnail";s:4:"name";s:12:"HisThumbnail";s:7:"version";s:0:"";}i:313;a:3:{s:5:"regex";s:23:"OneSearch/(\\d+\\.[.\\d]+)";s:4:"name";s:15:"Yahoo OneSearch";s:7:"version";s:2:"$1";}i:314;a:3:{s:5:"regex";s:23:"anonymized by Abelssoft";s:4:"name";s:14:"AntiBrowserSpy";s:7:"version";s:0:"";}i:315;a:3:{s:5:"regex";s:48:"Anonymisiert durch AlMiSoft(?! Browser-Maulkorb)";s:4:"name";s:18:"Browser-Anonymizer";s:7:"version";s:0:"";}i:316;a:3:{s:5:"regex";s:23:"DaumApps/(\\d+\\.[.\\d]+)?";s:4:"name";s:4:"Daum";s:7:"version";s:2:"$1";}i:317;a:3:{s:5:"regex";s:7:"AT&T TV";s:4:"name";s:7:"DIRECTV";s:7:"version";s:0:"";}i:318;a:3:{s:5:"regex";s:29:"Reddit/Version (\\d+\\.[.\\d]+)/";s:4:"name";s:6:"Reddit";s:7:"version";s:2:"$1";}i:319;a:3:{s:5:"regex";s:23:"TuyaSmart/(\\d+\\.[.\\d]+)";s:4:"name";s:15:"Tuya Smart Life";s:7:"version";s:2:"$1";}i:320;a:3:{s:5:"regex";s:51:"(?:Spotify(?:-Lite)?/(\\d+\\.[.\\d]+|12\\d+)|^spotify_)";s:4:"name";s:7:"Spotify";s:7:"version";s:2:"$1";}i:321;a:3:{s:5:"regex";s:50:"(?:AmazonMusic|^Harley)(?:(?:%2F|/)(\\d+\\.[.\\d]+))?";s:4:"name";s:12:"Amazon Music";s:7:"version";s:2:"$1";}i:322;a:3:{s:5:"regex";s:21:"Klarna/(\\d+\\.[.\\d]+)?";s:4:"name";s:6:"Klarna";s:7:"version";s:2:"$1";}i:323;a:3:{s:5:"regex";s:14:"^R/(\\d+[.\\d]+)";s:4:"name";s:1:"R";s:7:"version";s:2:"$1";}i:324;a:3:{s:5:"regex";s:13:"RadioAppFree/";s:4:"name";s:8:"RadioApp";s:7:"version";s:0:"";}i:325;a:3:{s:5:"regex";s:79:"^(?:Audible, Android, |com\\.audible\\.playersdk\\.player/|Audible/)(\\d+\\.[.\\d]+)?";s:4:"name";s:7:"Audible";s:7:"version";s:2:"$1";}i:326;a:3:{s:5:"regex";s:78:"Overcast/?(\\d+\\.[.\\d]+)? \\(\\+http://overcast\\.fm/; (?:Apple Watch|iOS) podcast";s:4:"name";s:8:"Overcast";s:7:"version";s:2:"$1";}i:327;a:3:{s:5:"regex";s:17:"^HTTPrequestmaker";s:4:"name";s:18:"HTTP request maker";s:7:"version";s:0:"";}i:328;a:3:{s:5:"regex";s:33:"^bonprix mobile App (\\d+\\.[.\\d]+)";s:4:"name";s:7:"BonPrix";s:7:"version";s:2:"$1";}i:329;a:3:{s:5:"regex";s:26:"Safari Quora (\\d+\\.[.\\d]+)";s:4:"name";s:5:"Quora";s:7:"version";s:2:"$1";}i:330;a:3:{s:5:"regex";s:59:"RelesysApp/(\\d+\\.[.\\d]+) \\(\\d{1,2}\\) net\\.relesysapp\\.jj2go";s:4:"name";s:5:"JJ2GO";s:7:"version";s:2:"$1";}i:331;a:3:{s:5:"regex";s:26:"MyWatchParty/(\\d+\\.[.\\d]+)";s:4:"name";s:14:"My Watch Party";s:7:"version";s:2:"$1";}i:332;a:3:{s:5:"regex";s:21:"LoseIt!/(\\d+\\.[.\\d]+)";s:4:"name";s:7:"LoseIt!";s:7:"version";s:2:"$1";}i:333;a:3:{s:5:"regex";s:24:"ActionExtension/([\\d.]+)";s:4:"name";s:15:"ActionExtension";s:7:"version";s:2:"$1";}i:334;a:3:{s:5:"regex";s:32:"^Adori(?:-Dev|-Listen)?/([\\d.]+)";s:4:"name";s:5:"Adori";s:7:"version";s:2:"$1";}i:335;a:3:{s:5:"regex";s:15:"^Agora/([\\d.]+)";s:4:"name";s:5:"Agora";s:7:"version";s:2:"$1";}i:336;a:3:{s:5:"regex";s:35:"^Airr(?:%20Beta)?/([\\d.]+)|^Airr \\(";s:4:"name";s:4:"Airr";s:7:"version";s:2:"$1";}i:337;a:3:{s:5:"regex";s:23:"^Airsonic/(\\d+\\.[.\\d]+)";s:4:"name";s:8:"Airsonic";s:7:"version";s:2:"$1";}i:338;a:3:{s:5:"regex";s:46:"(?:AllYouCanBooksApp|^AllYouCanBooks/([\\d.]+))";s:4:"name";s:17:"All You Can Books";s:7:"version";s:2:"$1";}i:339;a:3:{s:5:"regex";s:26:"^AllHitMusicRadio/([\\d.]+)";s:4:"name";s:16:"AllHitMusicRadio";s:7:"version";s:2:"$1";}i:340;a:3:{s:5:"regex";s:10:"^Amazon;AF";s:4:"name";s:11:"Amazon Fire";s:7:"version";s:0:"";}i:341;a:3:{s:5:"regex";s:16:"^Anchor/([\\d.]+)";s:4:"name";s:6:"Anchor";s:7:"version";s:2:"$1";}i:342;a:3:{s:5:"regex";s:23:"^AnchorFM/(\\d+\\.[.\\d]+)";s:4:"name";s:8:"AnchorFM";s:7:"version";s:2:"$1";}i:343;a:3:{s:5:"regex";s:54:"(?:^Anghami Android |^Anghami/|^أنغامي/)([\\d.]+)";s:4:"name";s:7:"Anghami";s:7:"version";s:2:"$1";}i:344;a:3:{s:5:"regex";s:59:"(?:^AntennaPod/|^de\\.danoeh\\.antennapod/|antenna/)([\\d.]+)?";s:4:"name";s:10:"AntennaPod";s:7:"version";s:2:"$1";}i:345;a:3:{s:5:"regex";s:16:"^Anybox/([\\d.]+)";s:4:"name";s:6:"Anybox";s:7:"version";s:2:"$1";}i:346;a:3:{s:5:"regex";s:34:"^Anytime/(\\d+\\.[.\\d]+).*amugofjava";s:4:"name";s:22:"Anytime Podcast Player";s:7:"version";s:2:"$1";}i:347;a:3:{s:5:"regex";s:7:"^APKXDL";s:4:"name";s:14:"APK Downloader";s:7:"version";s:0:"";}i:348;a:3:{s:5:"regex";s:16:"^Apollo/([\\d.]+)";s:4:"name";s:6:"Apollo";s:7:"version";s:2:"$1";}i:349;a:3:{s:5:"regex";s:103:"(?:^MessagesViewService/|^Messages/|^Messages Share Extension/|^MessagesNotificationExtension/)([\\d.]+)";s:4:"name";s:14:"Apple iMessage";s:7:"version";s:2:"$1";}i:350;a:3:{s:5:"regex";s:378:"(?:^Podcasts/|^Balados/|^Podcasti/|^Podcastit/|^Podcasturi/|^Podcasty/|^Podcast’ler/|^Podkaster/|^Podcaster/|^Podcastok/|^Подкасти/|^Подкасты/|^פודקאסטים/|^البودكاست/|^पॉडकास्ट/|^พ็อดคาสท์/|^播客/|^팟캐스트/|^ポッドキャスト/|^إسمعلي/|^Подкасттар/|^Podcast/|AirPodcasts/)([\\d.]+)?";s:4:"name";s:14:"Apple Podcasts";s:7:"version";s:2:"$1";}i:351;a:3:{s:5:"regex";s:23:"^Recordatorios/([\\d.]+)";s:4:"name";s:15:"Apple Reminders";s:7:"version";s:2:"$1";}i:352;a:3:{s:5:"regex";s:18:"^Arvocast/([\\d.]+)";s:4:"name";s:8:"Arvocast";s:7:"version";s:2:"$1";}i:353;a:3:{s:5:"regex";s:25:"^Radio\\.com/(\\d+\\.[.\\d]+)";s:4:"name";s:6:"Audacy";s:7:"version";s:2:"$1";}i:354;a:3:{s:5:"regex";s:15:"^Audio/([\\d.]+)";s:4:"name";s:5:"Audio";s:7:"version";s:2:"$1";}i:355;a:3:{s:5:"regex";s:17:"^Android_AudioNow";s:4:"name";s:9:"Audio Now";s:7:"version";s:0:"";}i:356;a:3:{s:5:"regex";s:20:"^Awasu/(\\d+\\.[.\\d]+)";s:4:"name";s:5:"Awasu";s:7:"version";s:2:"$1";}i:357;a:3:{s:5:"regex";s:14:"^Bear/([\\d.]+)";s:4:"name";s:4:"Bear";s:7:"version";s:2:"$1";}i:358;a:3:{s:5:"regex";s:15:"^Bible/([\\d.]+)";s:4:"name";s:5:"Bible";s:7:"version";s:2:"$1";}i:359;a:3:{s:5:"regex";s:14:"^Bolt/([\\d.]+)";s:4:"name";s:4:"Bolt";s:7:"version";s:2:"$1";}i:360;a:3:{s:5:"regex";s:20:"^Bookmobile/([\\d.]+)";s:4:"name";s:10:"Bookmobile";s:7:"version";s:2:"$1";}i:361;a:3:{s:5:"regex";s:14:"^Boom/([\\d.]+)";s:4:"name";s:4:"Boom";s:7:"version";s:2:"$1";}i:362;a:3:{s:5:"regex";s:23:"^Boomplay/(\\d+\\.[.\\d]+)";s:4:"name";s:8:"Boomplay";s:7:"version";s:2:"$1";}i:363;a:3:{s:5:"regex";s:19:"^Bose/(\\d+\\.[.\\d]+)";s:4:"name";s:15:"Bose SoundTouch";s:7:"version";s:2:"$1";}i:364;a:3:{s:5:"regex";s:6:"^bPod$";s:4:"name";s:4:"bPod";s:7:"version";s:0:"";}i:365;a:3:{s:5:"regex";s:20:"^breez/(\\d+\\.[.\\d]+)";s:4:"name";s:5:"Breez";s:7:"version";s:2:"$1";}i:366;a:3:{s:5:"regex";s:24:"^Broadcast/(\\d+\\.[.\\d]+)";s:4:"name";s:9:"Broadcast";s:7:"version";s:2:"$1";}i:367;a:3:{s:5:"regex";s:26:"BroadwayPodcastNetwork/iOS";s:4:"name";s:24:"Broadway Podcast Network";s:7:"version";s:0:"";}i:368;a:3:{s:5:"regex";s:33:"^(?:Browser|browser_iso)/([\\d.]+)";s:4:"name";s:11:"Browser app";s:7:"version";s:2:"$1";}i:369;a:3:{s:5:"regex";s:21:"^BrowserPlus/([\\d.]+)";s:4:"name";s:11:"BrowserPlus";s:7:"version";s:2:"$1";}i:370;a:3:{s:5:"regex";s:23:"^Bullhorn(?:/([\\d.]+))?";s:4:"name";s:8:"Bullhorn";s:7:"version";s:2:"$1";}i:371;a:3:{s:5:"regex";s:17:"^Capital/([\\d.]+)";s:4:"name";s:7:"Capital";s:7:"version";s:2:"$1";}i:372;a:3:{s:5:"regex";s:38:"^capsule\\.fm/([\\d.]+)|^capsule-android";s:4:"name";s:10:"capsule.fm";s:7:"version";s:2:"$1";}i:373;a:3:{s:5:"regex";s:20:"^Castamatic/([\\d.]+)";s:4:"name";s:10:"Castamatic";s:7:"version";s:2:"$1";}i:374;a:3:{s:5:"regex";s:18:"^Castaway/([\\d.]+)";s:4:"name";s:8:"Castaway";s:7:"version";s:2:"$1";}i:375;a:3:{s:5:"regex";s:22:"^CastBox/(\\d+\\.[.\\d]+)";s:4:"name";s:7:"CastBox";s:7:"version";s:2:"$1";}i:376;a:3:{s:5:"regex";s:20:"^Classic FM/([\\d.]+)";s:4:"name";s:10:"Classic FM";s:7:"version";s:2:"$1";}i:377;a:3:{s:5:"regex";s:16:"^Client/([\\d.]+)";s:4:"name";s:6:"Client";s:7:"version";s:2:"$1";}i:378;a:3:{s:5:"regex";s:19:"^Cosmicast/([\\d.]+)";s:4:"name";s:9:"Cosmicast";s:7:"version";s:2:"$1";}i:379;a:3:{s:5:"regex";s:18:"CPod/(\\d+\\.[.\\d]+)";s:4:"name";s:4:"CPod";s:7:"version";s:2:"$1";}i:380;a:3:{s:5:"regex";s:15:"^damus/([\\d.]+)";s:4:"name";s:5:"Damus";s:7:"version";s:2:"$1";}i:381;a:3:{s:5:"regex";s:60:"(?:be\\.standaard\\.audio|^DS podcast/|DS%20Podcast/)([\\d.]+)?";s:4:"name";s:12:"De Standaard";s:7:"version";s:2:"$1";}i:382;a:3:{s:5:"regex";s:18:"^DManager/([\\d.]+)";s:4:"name";s:8:"DManager";s:7:"version";s:2:"$1";}i:383;a:3:{s:5:"regex";s:24:"^doubleTwist CloudPlayer";s:4:"name";s:23:"DoubleTwist CloudPlayer";s:7:"version";s:0:"";}i:384;a:3:{s:5:"regex";s:18:"^Doughnut/([\\d.]+)";s:4:"name";s:8:"Doughnut";s:7:"version";s:2:"$1";}i:385;a:3:{s:5:"regex";s:16:"^Downie/([\\d.]+)";s:4:"name";s:6:"Downie";s:7:"version";s:2:"$1";}i:386;a:3:{s:5:"regex";s:20:"^Downloader/([\\d.]+)";s:4:"name";s:10:"Downloader";s:7:"version";s:2:"$1";}i:387;a:3:{s:5:"regex";s:28:"^EMAudioPlayer (\\d+\\.[.\\d]+)";s:4:"name";s:13:"EMAudioPlayer";s:7:"version";s:2:"$1";}i:388;a:3:{s:5:"regex";s:19:"^Expo/(\\d+\\.[.\\d]+)";s:4:"name";s:4:"Expo";s:7:"version";s:2:"$1";}i:389;a:3:{s:5:"regex";s:20:"^CFR%20Plus/([\\d.]+)";s:4:"name";s:5:"faidr";s:7:"version";s:2:"$1";}i:390;a:3:{s:5:"regex";s:16:"^Fathom/([\\d.]+)";s:4:"name";s:6:"Fathom";s:7:"version";s:2:"$1";}i:391;a:3:{s:5:"regex";s:26:"^FeedStation/(\\d+\\.[.\\d]+)";s:4:"name";s:11:"FeedStation";s:7:"version";s:2:"$1";}i:392;a:3:{s:5:"regex";s:15:"^Files/([\\d.]+)";s:4:"name";s:5:"Files";s:7:"version";s:2:"$1";}i:393;a:3:{s:5:"regex";s:26:"^Fountain(?:app)?/([\\d.]+)";s:4:"name";s:8:"Fountain";s:7:"version";s:2:"$1";}i:394;a:3:{s:5:"regex";s:35:"^Garmin fenix 5X Plus/(\\d+\\.[.\\d]+)";s:4:"name";s:15:"Garmin fenix 5X";s:7:"version";s:2:"$1";}i:395;a:3:{s:5:"regex";s:58:"^Garmin Forerunner (?:\\d+)(?: Music| Solar)?/(\\d+\\.[.\\d]+)";s:4:"name";s:17:"Garmin Forerunner";s:7:"version";s:2:"$1";}i:396;a:3:{s:5:"regex";s:14:"^Gold/([\\d.]+)";s:4:"name";s:4:"Gold";s:7:"version";s:2:"$1";}i:397;a:3:{s:5:"regex";s:24:"^GoldenPod/(\\d+\\.[.\\d]+)";s:4:"name";s:9:"GoldenPod";s:7:"version";s:2:"$1";}i:398;a:3:{s:5:"regex";s:16:"^GoLoud/([\\d.]+)";s:4:"name";s:6:"GoLoud";s:7:"version";s:2:"$1";}i:399;a:3:{s:5:"regex";s:41:"Goodpods(?:\\.Android|\\.iOS)? ?/ ?([\\d.]+)";s:4:"name";s:8:"Goodpods";s:7:"version";s:2:"$1";}i:400;a:3:{s:5:"regex";s:31:"^GoodReader(?:4|IPad)?/([\\d.]+)";s:4:"name";s:10:"GoodReader";s:7:"version";s:2:"$1";}i:401;a:3:{s:5:"regex";s:35:"\\(Fuchsia\\).* CrKey/(:?\\d+\\.[.\\d]+)";s:4:"name";s:15:"Google Nest Hub";s:7:"version";s:0:"";}i:402;a:3:{s:5:"regex";s:19:"^Guacamole/([\\d.]+)";s:4:"name";s:9:"Guacamole";s:7:"version";s:2:"$1";}i:403;a:3:{s:5:"regex";s:16:"^Hammel/([\\d.]+)";s:4:"name";s:6:"Hammel";s:7:"version";s:2:"$1";}i:404;a:3:{s:5:"regex";s:18:"^HardCast/([\\d.]+)";s:4:"name";s:8:"HardCast";s:7:"version";s:2:"$1";}i:405;a:3:{s:5:"regex";s:14:"^Hark/([\\d.]+)";s:4:"name";s:10:"Hark Audio";s:7:"version";s:2:"$1";}i:406;a:3:{s:5:"regex";s:15:"^Heart/([\\d.]+)";s:4:"name";s:5:"Heart";s:7:"version";s:2:"$1";}i:407;a:3:{s:5:"regex";s:25:"hermespod\\.com/v?([\\d.]+)";s:4:"name";s:9:"HermesPod";s:7:"version";s:2:"$1";}i:408;a:3:{s:5:"regex";s:16:"^HiCast/([\\d.]+)";s:4:"name";s:6:"HiCast";s:7:"version";s:2:"$1";}i:409;a:3:{s:5:"regex";s:28:"^Himalaya(?:_test)?/([\\d.]+)";s:4:"name";s:8:"Himalaya";s:7:"version";s:2:"$1";}i:410;a:3:{s:5:"regex";s:22:"^HyperCatcher/([\\d.]+)";s:4:"name";s:12:"HyperCatcher";s:7:"version";s:2:"$1";}i:411;a:3:{s:5:"regex";s:40:"^(?:iHeartRadio|iHeartPodcasts)/([\\d.]+)";s:4:"name";s:11:"iHeartRadio";s:7:"version";s:2:"$1";}i:412;a:3:{s:5:"regex";s:23:"^IOSAudiobooks/([\\d.]+)";s:4:"name";s:10:"Audiobooks";s:7:"version";s:2:"$1";}i:413;a:3:{s:5:"regex";s:32:"^iVoox(?:App|New)?[ /]?([\\d.]+)?";s:4:"name";s:5:"iVoox";s:7:"version";s:2:"$1";}i:414;a:3:{s:5:"regex";s:18:"^Jam/(\\d+\\.[.\\d]+)";s:4:"name";s:3:"Jam";s:7:"version";s:2:"$1";}i:415;a:3:{s:5:"regex";s:74:"^(?:com\\.jio\\.media\\.jiobeats/(\\d+\\.[.\\d]+)|com\\.saavn\\.android|^[sS]aavn)";s:4:"name";s:8:"JioSaavn";s:7:"version";s:2:"$1";}i:416;a:3:{s:5:"regex";s:29:"KajabiMobileApp|KajabiPodcast";s:4:"name";s:6:"Kajabi";s:7:"version";s:0:"";}i:417;a:3:{s:5:"regex";s:24:"^KakaoTalk/(\\d+\\.[.\\d]+)";s:4:"name";s:9:"KakaoTalk";s:7:"version";s:2:"$1";}i:418;a:3:{s:5:"regex";s:29:"^Kids(?:%20| )Listen/([\\d.]+)";s:4:"name";s:11:"Kids Listen";s:7:"version";s:2:"$1";}i:419;a:3:{s:5:"regex";s:29:"^KidspodMobileClient/([\\d.]+)";s:4:"name";s:7:"KidsPod";s:7:"version";s:2:"$1";}i:420;a:3:{s:5:"regex";s:20:"^KKBOX/(\\d+\\.[.\\d]+)";s:4:"name";s:5:"KKBOX";s:7:"version";s:2:"$1";}i:421;a:3:{s:5:"regex";s:43:"^(?:Laughable.+iOS|Laughable)/(\\d+\\.[.\\d]+)";s:4:"name";s:9:"Laughable";s:7:"version";s:2:"$1";}i:422;a:3:{s:5:"regex";s:13:"^LBC/([\\d.]+)";s:4:"name";s:3:"LBC";s:7:"version";s:2:"$1";}i:423;a:3:{s:5:"regex";s:23:"LG Player (\\d+\\.[.\\d]+)";s:4:"name";s:9:"LG Player";s:7:"version";s:2:"$1";}i:424;a:3:{s:5:"regex";s:33:"^Listen(?:(?: |%20)App)?/([\\d.]+)";s:4:"name";s:6:"Listen";s:7:"version";s:2:"$1";}i:425;a:3:{s:5:"regex";s:15:"^Liulo/([\\d.]+)";s:4:"name";s:5:"Liulo";s:7:"version";s:2:"$1";}i:426;a:3:{s:5:"regex";s:19:"Listen5[ /]([\\d.]+)";s:4:"name";s:11:"Just Listen";s:7:"version";s:2:"$1";}i:427;a:3:{s:5:"regex";s:49:"^(?:Luminary(?:Preprod)?|luminary\\.next)/([\\d.]+)";s:4:"name";s:8:"Luminary";s:7:"version";s:2:"$1";}i:428;a:3:{s:5:"regex";s:14:"^Megaphone\\.fm";s:4:"name";s:9:"Megaphone";s:7:"version";s:0:"";}i:429;a:3:{s:5:"regex";s:23:"^Menucast/(\\d+\\.[.\\d]+)";s:4:"name";s:8:"Menucast";s:7:"version";s:2:"$1";}i:430;a:3:{s:5:"regex";s:19:"^Messenger/([\\d.]+)";s:4:"name";s:10:"MessengerX";s:7:"version";s:2:"$1";}i:431;a:3:{s:5:"regex";s:26:"^Mimir(?:-macOS)?/([\\d.]+)";s:4:"name";s:5:"Mimir";s:7:"version";s:2:"$1";}i:432;a:3:{s:5:"regex";s:19:"^MobileSMS/([\\d.]+)";s:4:"name";s:9:"MobileSMS";s:7:"version";s:2:"$1";}i:433;a:3:{s:5:"regex";s:18:"^Moon ?FM/([\\d.]+)";s:4:"name";s:6:"MoonFM";s:7:"version";s:2:"$1";}i:434;a:3:{s:5:"regex";s:78:"^myTuner(?:(?:%20Radio%20app|iOS%20Free|_podcasts_androidplayer)/ ?([\\d.]+)?)?";s:4:"name";s:7:"MyTuner";s:7:"version";s:2:"$1";}i:435;a:3:{s:5:"regex";s:8:"^Newsly$";s:4:"name";s:6:"Newsly";s:7:"version";s:0:"";}i:436;a:3:{s:5:"regex";s:27:"^NRC(?: |%20)Audio/([\\d.]+)";s:4:"name";s:9:"NRC Audio";s:7:"version";s:2:"$1";}i:437;a:3:{s:5:"regex";s:63:"(?:NRC-Nieuws/|nl\\.nrc\\.nrcapp |com\\.twipemobile\\.nrc )([\\d.]+)";s:4:"name";s:3:"NRC";s:7:"version";s:2:"$1";}i:438;a:3:{s:5:"regex";s:22:"^Outcast[/ ]?([\\d.]+)?";s:4:"name";s:7:"Outcast";s:7:"version";s:2:"$1";}i:439;a:3:{s:5:"regex";s:31:"^Podcast Overhaul/(\\d+\\.[.\\d]+)";s:4:"name";s:11:"Overhaul FM";s:7:"version";s:2:"$1";}i:440;a:3:{s:5:"regex";s:24:"^Palco MP3/(\\d+\\.[.\\d]+)";s:4:"name";s:9:"Palco MP3";s:7:"version";s:2:"$1";}i:441;a:3:{s:5:"regex";s:22:"^PeaCast/(\\d+\\.[.\\d]+)";s:4:"name";s:7:"PeaCast";s:7:"version";s:2:"$1";}i:442;a:3:{s:5:"regex";s:42:"^Player FM|^Player%20FM|^Alpha%20PlayerFM/";s:4:"name";s:9:"Player FM";s:7:"version";s:0:"";}i:443;a:3:{s:5:"regex";s:16:"^Podbay/([\\d.]+)";s:4:"name";s:6:"Podbay";s:7:"version";s:2:"$1";}i:444;a:3:{s:5:"regex";s:24:"^PodcastGuru[ /]([\\d.]+)";s:4:"name";s:12:"Podcast Guru";s:7:"version";s:2:"$1";}i:445;a:3:{s:5:"regex";s:29:"^Podcast Player/(\\d+\\.[.\\d]+)";s:4:"name";s:14:"Podcast Player";s:7:"version";s:2:"$1";}i:446;a:3:{s:5:"regex";s:30:"^PodcastRepublic/(\\d+\\.[.\\d]+)";s:4:"name";s:16:"Podcast Republic";s:7:"version";s:2:"$1";}i:447;a:3:{s:5:"regex";s:29:"^Podcastly[/ ]?(\\d+\\.[.\\d]+)?";s:4:"name";s:9:"Podcastly";s:7:"version";s:2:"$1";}i:448;a:3:{s:5:"regex";s:29:"^Podchaser |^Podchaser-Parser";s:4:"name";s:9:"Podchaser";s:7:"version";s:0:"";}i:449;a:3:{s:5:"regex";s:20:"^Podclipper/([\\d.]+)";s:4:"name";s:10:"Podclipper";s:7:"version";s:2:"$1";}i:450;a:3:{s:5:"regex";s:26:"^PodCruncher/(\\d+\\.[.\\d]+)";s:4:"name";s:11:"PodCruncher";s:7:"version";s:2:"$1";}i:451;a:3:{s:5:"regex";s:15:"^Podeo/([\\d.]+)";s:4:"name";s:5:"Podeo";s:7:"version";s:2:"$1";}i:452;a:3:{s:5:"regex";s:27:"^Podfriend[ /](\\d+\\.[.\\d]+)";s:4:"name";s:9:"Podfriend";s:7:"version";s:2:"$1";}i:453;a:3:{s:5:"regex";s:59:"(?:^Podhero(?:%20Alpha)?/|^Swoot[/ ](?:Agent[/ ])?)([\\d.]+)";s:4:"name";s:7:"Podhero";s:7:"version";s:2:"$1";}i:454;a:3:{s:5:"regex";s:21:"^Podimo/(\\d+\\.[.\\d]+)";s:4:"name";s:6:"Podimo";s:7:"version";s:2:"$1";}i:455;a:3:{s:5:"regex";s:8:"PodKast$";s:4:"name";s:7:"PodKast";s:7:"version";s:0:"";}i:456;a:3:{s:5:"regex";s:32:"^Podkicker(?: Pro)/(\\d+\\.[.\\d]+)";s:4:"name";s:13:"Podkicker Pro";s:7:"version";s:2:"$1";}i:457;a:3:{s:5:"regex";s:19:"PodLP/(\\d+\\.[.\\d]+)";s:4:"name";s:5:"PodLP";s:7:"version";s:2:"$1";}i:458;a:3:{s:5:"regex";s:43:"^(?:Podme android app|PodMe)/(\\d+\\.[.\\d]+)?";s:4:"name";s:5:"PodMe";s:7:"version";s:2:"$1";}i:459;a:3:{s:5:"regex";s:36:"^PodMN/(?:iOS|Android) (\\d+\\.[.\\d]+)";s:4:"name";s:5:"PodMN";s:7:"version";s:2:"$1";}i:460;a:3:{s:5:"regex";s:15:"^PodNL/([\\d.]+)";s:4:"name";s:5:"PodNL";s:7:"version";s:2:"$1";}i:461;a:3:{s:5:"regex";s:32:"^(?:Podopolo|podopolo)/?([\\d.]+)";s:4:"name";s:8:"Podopolo";s:7:"version";s:2:"$1";}i:462;a:3:{s:5:"regex";s:22:"^Podplay/(\\d+\\.[.\\d]+)";s:4:"name";s:7:"Podplay";s:7:"version";s:2:"$1";}i:463;a:3:{s:5:"regex";s:6:"^Pods/";s:4:"name";s:4:"Pods";s:7:"version";s:2:"$1";}i:464;a:3:{s:5:"regex";s:23:"^Podurama/(\\d+\\.[.\\d]+)";s:4:"name";s:8:"Podurama";s:7:"version";s:2:"$1";}i:465;a:3:{s:5:"regex";s:12:"^PodTrapper$";s:4:"name";s:10:"PodTrapper";s:7:"version";s:0:"";}i:466;a:3:{s:5:"regex";s:22:"^Podvine/(\\d+\\.[.\\d]+)";s:4:"name";s:7:"Podvine";s:7:"version";s:2:"$1";}i:467;a:3:{s:5:"regex";s:10:"^Podverse/";s:4:"name";s:8:"Podverse";s:7:"version";s:0:"";}i:468;a:3:{s:5:"regex";s:83:"(?:Podyssey App|com\\.toysinboxes\\.Echo|fm\\.podyssey\\.podcasts|^Podyssey)/?([\\d.]+)?";s:4:"name";s:8:"Podyssey";s:7:"version";s:2:"$1";}i:469;a:3:{s:5:"regex";s:25:"^PugpigBolt (\\d+\\.[.\\d]+)";s:4:"name";s:11:"PugPig Bolt";s:7:"version";s:2:"$1";}i:470;a:3:{s:5:"regex";s:34:"^radio\\.([a-z]{2}|net)[ /]([\\d.]+)";s:4:"name";s:8:"radio.$1";s:7:"version";s:2:"$2";}i:471;a:3:{s:5:"regex";s:23:"^GetPodcast[ /]([\\d.]+)";s:4:"name";s:10:"GetPodcast";s:7:"version";s:2:"$1";}i:472;a:3:{s:5:"regex";s:24:"^radio\\.next[ /]([\\d.]+)";s:4:"name";s:10:"Radio Next";s:7:"version";s:2:"$1";}i:473;a:3:{s:5:"regex";s:44:"(?:^Radioline%202/(\\d+\\.[.\\d]+)|^Radioline$)";s:4:"name";s:9:"Radioline";s:7:"version";s:2:"$1";}i:474;a:3:{s:5:"regex";s:20:"^Repod/(\\d+\\.[.\\d]+)";s:4:"name";s:5:"Repod";s:7:"version";s:2:"$1";}i:475;a:3:{s:5:"regex";s:24:"^rhythmbox/(\\d+\\.[.\\d]+)";s:4:"name";s:9:"Rhythmbox";s:7:"version";s:2:"$1";}i:476;a:3:{s:5:"regex";s:23:"^SachNoi\\.?app/([\\d.]+)";s:4:"name";s:7:"SachNoi";s:7:"version";s:2:"$1";}i:477;a:3:{s:5:"regex";s:9:"^sp-agent";s:4:"name";s:16:"Samsung Podcasts";s:7:"version";s:0:"";}i:478;a:3:{s:5:"regex";s:44:"^(?:ServeStream(?: Dynamo)?/?(\\d+\\.[.\\d]+)?)";s:4:"name";s:11:"ServeStream";s:7:"version";s:2:"$1";}i:479;a:3:{s:5:"regex";s:16:"^Shadow/([\\d.]+)";s:4:"name";s:6:"Shadow";s:7:"version";s:2:"$1";}i:480;a:3:{s:5:"regex";s:22:"^Shadowrocket/([\\d.]+)";s:4:"name";s:12:"Shadowrocket";s:7:"version";s:2:"$1";}i:481;a:3:{s:5:"regex";s:44:"^(?:SiriusXM|sxm-android|sxm-apple)/([\\d.]+)";s:4:"name";s:8:"SiriusXM";s:7:"version";s:2:"$1";}i:482;a:3:{s:5:"regex";s:15:"^Snipd/([\\d.]+)";s:4:"name";s:5:"Snipd";s:7:"version";s:2:"$1";}i:483;a:3:{s:5:"regex";s:15:"^Sodes/([\\d.]+)";s:4:"name";s:6:"\'sodes";s:7:"version";s:2:"$1";}i:484;a:3:{s:5:"regex";s:63:"(?:Sonnet/(?:Android|iOS)|^Simple Podcast Player/(\\d+\\.[.\\d]+))";s:4:"name";s:6:"Sonnet";s:7:"version";s:2:"$1";}i:485;a:3:{s:5:"regex";s:13:"^sony_tv;ps5;";s:4:"name";s:18:"Sony PlayStation 5";s:7:"version";s:0:"";}i:486;a:3:{s:5:"regex";s:17:"^SoundOn/([\\d.]+)";s:4:"name";s:7:"SoundOn";s:7:"version";s:2:"$1";}i:487;a:3:{s:5:"regex";s:25:"^SoundWaves-(\\d+\\.[.\\d]+)";s:4:"name";s:10:"SoundWaves";s:7:"version";s:2:"$1";}i:488;a:3:{s:5:"regex";s:17:"Spreaker/([\\d.]+)";s:4:"name";s:8:"Spreaker";s:7:"version";s:2:"$1";}i:489;a:3:{s:5:"regex";s:53:"^Stitcher/|^Stitcher Demo/|^AlexaMediaPlayer/Stitcher";s:4:"name";s:8:"Stitcher";s:7:"version";s:0:"";}i:490;a:3:{s:5:"regex";s:25:"^StoryShots/(\\d+\\.[.\\d]+)";s:4:"name";s:10:"StoryShots";s:7:"version";s:2:"$1";}i:491;a:3:{s:5:"regex";s:18:"^Swinsian/([\\d.]+)";s:4:"name";s:8:"Swinsian";s:7:"version";s:2:"$1";}i:492;a:3:{s:5:"regex";s:28:"^ThePodcastApp/(\\d+\\.[.\\d]+)";s:4:"name";s:11:"Podcast App";s:7:"version";s:2:"$1";}i:493;a:3:{s:5:"regex";s:16:"^TREBLE/([\\d.]+)";s:4:"name";s:9:"Treble.fm";s:7:"version";s:2:"$1";}i:494;a:3:{s:5:"regex";s:25:"^Turtlecast/(\\d+\\.[.\\d]+)";s:4:"name";s:10:"Turtlecast";s:7:"version";s:2:"$1";}i:495;a:3:{s:5:"regex";s:14:"^Ubook Player$";s:4:"name";s:12:"Ubook Player";s:7:"version";s:0:"";}i:496;a:3:{s:5:"regex";s:25:"^VictorReader Stream Trek";s:4:"name";s:25:"Victor Reader Stream Trek";s:7:"version";s:0:"";}i:497;a:3:{s:5:"regex";s:23:"^VictorReader Stream V3";s:4:"name";s:22:"Victor Reader Stream 3";s:7:"version";s:0:"";}i:498;a:3:{s:5:"regex";s:42:"^(?:VictorReader Stream 503|VictorReader_)";s:4:"name";s:35:"Victor Reader Stream New Generation";s:7:"version";s:0:"";}i:499;a:3:{s:5:"regex";s:18:"^Vodacast/([\\d.]+)";s:4:"name";s:8:"Vodacast";s:7:"version";s:2:"$1";}i:500;a:3:{s:5:"regex";s:24:"^WynkMusic/(\\d+\\.[.\\d]+)";s:4:"name";s:10:"Wynk Music";s:7:"version";s:2:"$1";}i:501;a:3:{s:5:"regex";s:25:"^Xiaoyuzhou/(\\d+\\.[.\\d]+)";s:4:"name";s:12:"Xiao Yu Zhou";s:7:"version";s:2:"$1";}i:502;a:3:{s:5:"regex";s:18:"^Ya(ndex)?\\.Music/";s:4:"name";s:12:"Yandex Music";s:7:"version";s:0:"";}i:503;a:3:{s:5:"regex";s:14:"^yapa/([\\d.]+)";s:4:"name";s:4:"Yapa";s:7:"version";s:2:"$1";}i:504;a:3:{s:5:"regex";s:18:"Zune/(\\d+\\.[.\\d]+)";s:4:"name";s:4:"Zune";s:7:"version";s:0:"";}i:505;a:3:{s:5:"regex";s:14:"UCast/([\\d.]+)";s:4:"name";s:5:"UCast";s:7:"version";s:2:"$1";}i:506;a:3:{s:5:"regex";s:58:"(?:^NPROneAndroid$|(?:^NPR%20One|nprone_android)/([\\d.]+))";s:4:"name";s:3:"NPR";s:7:"version";s:2:"$1";}i:507;a:3:{s:5:"regex";s:15:"Uforia/([\\d.]+)";s:4:"name";s:6:"Uforia";s:7:"version";s:2:"$1";}i:508;a:3:{s:5:"regex";s:15:"^LAT-Native-App";s:4:"name";s:10:"L.A. Times";s:7:"version";s:0:"";}i:509;a:3:{s:5:"regex";s:48:"(?:^NYT(?: |%20)?Audio(?:-iOS)?|nytios)/([\\d.]+)";s:4:"name";s:18:"The New York Times";s:7:"version";s:2:"$1";}i:510;a:3:{s:5:"regex";s:22:"^LiSTNR[.\\w]*/([\\d.]+)";s:4:"name";s:6:"LiSTNR";s:7:"version";s:2:"$1";}i:511;a:3:{s:5:"regex";s:13:"^Podu_player$";s:4:"name";s:4:"podU";s:7:"version";s:2:"$1";}i:512;a:3:{s:5:"regex";s:18:"^TiviMate/([\\d.]+)";s:4:"name";s:8:"TiviMate";s:7:"version";s:2:"$1";}i:513;a:3:{s:5:"regex";s:13:"IPTV/([\\d.]+)";s:4:"name";s:4:"IPTV";s:7:"version";s:2:"$1";}i:514;a:3:{s:5:"regex";s:17:"IPTV Pro/([\\d.]+)";s:4:"name";s:8:"IPTV Pro";s:7:"version";s:2:"$1";}i:515;a:3:{s:5:"regex";s:33:"^com\\.audials(?:\\.paid)?/([\\d.]+)";s:4:"name";s:7:"Audials";s:7:"version";s:2:"$1";}i:516;a:3:{s:5:"regex";s:18:"^CoolerFM/([\\d.]+)";s:4:"name";s:6:"Cooler";s:7:"version";s:2:"$1";}i:517;a:3:{s:5:"regex";s:18:"^Metacast/([\\d.]+)";s:4:"name";s:8:"Metacast";s:7:"version";s:2:"$1";}i:518;a:3:{s:5:"regex";s:16:"^mowPod/([\\d.]+)";s:4:"name";s:6:"mowPod";s:7:"version";s:2:"$1";}i:519;a:3:{s:5:"regex";s:35:"com\\.meecel\\.feedreader\\.RssDemonAd";s:4:"name";s:8:"RSSDemon";s:7:"version";s:0:"";}i:520;a:3:{s:5:"regex";s:31:"^Virgin(?:%20|\\s)Radio/([\\d.]+)";s:4:"name";s:12:"Virgin Radio";s:7:"version";s:2:"$1";}i:521;a:3:{s:5:"regex";s:19:"MetaMask(?:Mobile)?";s:4:"name";s:8:"MetaMask";s:7:"version";s:0:"";}i:522;a:3:{s:5:"regex";s:16:"\\+Simple Browser";s:4:"name";s:7:"+Simple";s:7:"version";s:0:"";}i:523;a:3:{s:5:"regex";s:25:"Bitwarden_Mobile/([\\d.]+)";s:4:"name";s:9:"Bitwarden";s:7:"version";s:2:"$1";}i:524;a:3:{s:5:"regex";s:17:"MXPlayer/([\\d.]+)";s:4:"name";s:9:"MX Player";s:7:"version";s:2:"$1";}i:525;a:3:{s:5:"regex";s:21:"HistoryHound/([\\d.]+)";s:4:"name";s:12:"HistoryHound";s:7:"version";s:2:"$1";}i:526;a:3:{s:5:"regex";s:11:"Quicksilver";s:4:"name";s:11:"Quicksilver";s:7:"version";s:0:"";}i:527;a:3:{s:5:"regex";s:19:"CamScanner/([\\d.]+)";s:4:"name";s:10:"CamScanner";s:7:"version";s:2:"$1";}i:528;a:3:{s:5:"regex";s:15:"OBS/(\\d+[.\\d]+)";s:4:"name";s:10:"OBS Studio";s:7:"version";s:2:"$1";}i:529;a:3:{s:5:"regex";s:29:"XSplitBroadcaster/(\\d+[.\\d]+)";s:4:"name";s:18:"XSplit Broadcaster";s:7:"version";s:2:"$1";}i:530;a:3:{s:5:"regex";s:55:"twitch-desktop-electron-platform.*spotlight/(\\d+[.\\d]+)";s:4:"name";s:13:"Twitch Studio";s:7:"version";s:2:"$1";}i:531;a:3:{s:5:"regex";s:14:"Stream Master$";s:4:"name";s:13:"Stream Master";s:7:"version";s:0:"";}i:532;a:3:{s:5:"regex";s:23:"Freespoke/(\\d+\\.[.\\d]+)";s:4:"name";s:9:"Freespoke";s:7:"version";s:2:"$1";}i:533;a:3:{s:5:"regex";s:8:"Whatplay";s:4:"name";s:8:"Whatplay";s:7:"version";s:0:"";}i:534;a:3:{s:5:"regex";s:16:"Lark/(\\d+[.\\d]+)";s:4:"name";s:4:"Lark";s:7:"version";s:2:"$1";}i:535;a:3:{s:5:"regex";s:23:"SearchCraft/(\\d+[.\\d]+)";s:4:"name";s:11:"SearchCraft";s:7:"version";s:2:"$1";}i:536;a:3:{s:5:"regex";s:22:"DeFiWallet/(\\d+[.\\d]+)";s:4:"name";s:22:"Crypto.com DeFi Wallet";s:7:"version";s:2:"$1";}i:537;a:3:{s:5:"regex";s:21:"Clipbox\\+/(\\d+[.\\d]+)";s:4:"name";s:8:"Clipbox+";s:7:"version";s:2:"$1";}i:538;a:3:{s:5:"regex";s:13:"appname/HideX";s:4:"name";s:5:"HideX";s:7:"version";s:0:"";}i:539;a:3:{s:5:"regex";s:43:"HMSCore(?:[ /]([\\d.]+))?(?!.*HuaweiBrowser)";s:4:"name";s:22:"Huawei Mobile Services";s:7:"version";s:2:"$1";}i:540;a:3:{s:5:"regex";s:14:"appname/PLAYit";s:4:"name";s:6:"PLAYit";s:7:"version";s:0:"";}i:541;a:3:{s:5:"regex";s:25:"Autopliuslt(?:/([\\d.]+))?";s:4:"name";s:12:"Autoplius.lt";s:7:"version";s:2:"$1";}i:542;a:3:{s:5:"regex";s:18:"HCom(?:/([\\d.]+))?";s:4:"name";s:10:"Hotels.com";s:7:"version";s:2:"$1";}i:543;a:3:{s:5:"regex";s:28:"CoinbaseRetail(?:/([\\d.]+))?";s:4:"name";s:8:"Coinbase";s:7:"version";s:2:"$1";}i:544;a:3:{s:5:"regex";s:26:"De Telegraaf(?:/([\\d.]+))?";s:4:"name";s:12:"De Telegraaf";s:7:"version";s:2:"$1";}i:545;a:3:{s:5:"regex";s:14:"waipu/([\\d.]+)";s:4:"name";s:8:"waipu.tv";s:7:"version";s:2:"$1";}i:546;a:3:{s:5:"regex";s:8:"Redditor";s:4:"name";s:8:"Redditor";s:7:"version";s:0:"";}i:547;a:3:{s:5:"regex";s:30:"com\\.topbuzz\\.videoen/([\\d.]+)";s:4:"name";s:9:"BuzzVideo";s:7:"version";s:2:"$1";}i:548;a:3:{s:5:"regex";s:13:"GlobalProtect";s:4:"name";s:13:"GlobalProtect";s:7:"version";s:0:"";}i:549;a:3:{s:5:"regex";s:22:"Trade Me(?:/([\\d.]+))?";s:4:"name";s:8:"Trade Me";s:7:"version";s:2:"$1";}i:550;a:3:{s:5:"regex";s:25:"XING(?:-iPhone)?/([\\d.]+)";s:4:"name";s:4:"XING";s:7:"version";s:2:"$1";}i:551;a:3:{s:5:"regex";s:15:"Bridge/([\\d.]+)";s:4:"name";s:6:"Bridge";s:7:"version";s:2:"$1";}i:552;a:3:{s:5:"regex";s:16:"iPlayTV/([\\d.]+)";s:4:"name";s:7:"iPlayTV";s:7:"version";s:2:"$1";}i:553;a:3:{s:5:"regex";s:20:"momoWebView/([\\d.]+)";s:4:"name";s:4:"MOMO";s:7:"version";s:2:"$1";}i:554;a:3:{s:5:"regex";s:24:"nate_app;appver:([\\d.]+)";s:4:"name";s:4:"nate";s:7:"version";s:2:"$1";}i:555;a:3:{s:5:"regex";s:25:"SOFI_APP_VERSION=([\\d.]+)";s:4:"name";s:4:"SoFi";s:7:"version";s:2:"$1";}i:556;a:3:{s:5:"regex";s:27:"WNYC(?: App)?[/ ]?([\\d.]+)?";s:4:"name";s:4:"WNYC";s:7:"version";s:2:"$1";}i:557;a:3:{s:5:"regex";s:35:"com\\.theepochtimes\\.mobile/([\\d.]+)";s:4:"name";s:15:"The Epoch Times";s:7:"version";s:2:"$1";}i:558;a:3:{s:5:"regex";s:34:"ReutersNews(?: App)?[/ ]?([\\d.]+)?";s:4:"name";s:12:"Reuters News";s:7:"version";s:2:"$1";}i:559;a:3:{s:5:"regex";s:18:"WatchFree/([\\d.]+)";s:4:"name";s:10:"WatchFree+";s:7:"version";s:2:"$1";}i:560;a:3:{s:5:"regex";s:17:"obsidian/([\\d.]+)";s:4:"name";s:8:"Obsidian";s:7:"version";s:2:"$1";}i:561;a:3:{s:5:"regex";s:10:"Perplexity";s:4:"name";s:10:"Perplexity";s:7:"version";s:0:"";}i:562;a:3:{s:5:"regex";s:6:"XShare";s:4:"name";s:6:"XShare";s:7:"version";s:0:"";}i:563;a:3:{s:5:"regex";s:17:"VISHAAPP ([\\d.]+)";s:4:"name";s:5:"Visha";s:7:"version";s:2:"$1";}i:564;a:3:{s:5:"regex";s:20:"nyt_android/([\\d.]+)";s:4:"name";s:13:"The Crossword";s:7:"version";s:2:"$1";}i:565;a:3:{s:5:"regex";s:10:"mojeek-app";s:4:"name";s:6:"Mojeek";s:7:"version";s:0:"";}i:566;a:3:{s:5:"regex";s:19:"Fiddler/(\\d+[.\\d]+)";s:4:"name";s:15:"Fiddler Classic";s:7:"version";s:2:"$1";}i:567;a:3:{s:5:"regex";s:31:"ZONApp/(?:iOS|Android)/([\\d.]+)";s:4:"name";s:11:"ZEIT ONLINE";s:7:"version";s:2:"$1";}i:568;a:3:{s:5:"regex";s:27:"tracepal(?:\\.app)?/([\\d.]+)";s:4:"name";s:8:"TracePal";s:7:"version";s:2:"$1";}i:569;a:3:{s:5:"regex";s:24:"topsecret\\.chat/([\\d.]+)";s:4:"name";s:14:"TopSecret Chat";s:7:"version";s:2:"$1";}i:570;a:3:{s:5:"regex";s:13:"Kwai/([\\d.]+)";s:4:"name";s:4:"Kwai";s:7:"version";s:2:"$1";}i:571;a:3:{s:5:"regex";s:17:"Kwai_Pro/([\\d.]+)";s:4:"name";s:8:"Kwai Pro";s:7:"version";s:2:"$1";}i:572;a:3:{s:5:"regex";s:25:"Sooplive Webview/([\\d.]+)";s:4:"name";s:4:"SOOP";s:7:"version";s:2:"$1";}i:573;a:3:{s:5:"regex";s:31:"GoEuro(?:Android|IOS )/([\\d.]+)";s:4:"name";s:6:"GoEuro";s:7:"version";s:2:"$1";}i:574;a:3:{s:5:"regex";s:14:"BOOM v([\\d.]+)";s:4:"name";s:7:"Boom360";s:7:"version";s:2:"$1";}i:575;a:3:{s:5:"regex";s:18:"WallaNews/([\\d.]+)";s:4:"name";s:10:"Walla News";s:7:"version";s:2:"$1";}i:576;a:3:{s:5:"regex";s:23:"TRP_iPhone_App/([\\d.]+)";s:4:"name";s:18:"TRP Retail Locator";s:7:"version";s:2:"$1";}i:577;a:3:{s:5:"regex";s:21:"Townnews-Now/([\\d.]+)";s:4:"name";s:12:"TownNews Now";s:7:"version";s:2:"$1";}i:578;a:3:{s:5:"regex";s:7:"^Klara/";s:4:"name";s:5:"Klara";s:7:"version";s:0:"";}i:579;a:3:{s:5:"regex";s:22:"EdmodoAndroid/([\\d.]+)";s:4:"name";s:6:"Edmodo";s:7:"version";s:2:"$1";}i:580;a:3:{s:5:"regex";s:23:"HFEducationIOS/([\\d.]+)";s:4:"name";s:20:"HeartFocus Education";s:7:"version";s:2:"$1";}i:581;a:3:{s:5:"regex";s:19:"OpenVAS-VT ([\\d.]+)";s:4:"name";s:7:"OpenVAS";s:7:"version";s:2:"$1";}i:582;a:3:{s:5:"regex";s:14:"appdb/([\\d.]+)";s:4:"name";s:5:"appdb";s:7:"version";s:2:"$1";}i:583;a:3:{s:5:"regex";s:15:"Apache/([\\d.]+)";s:4:"name";s:6:"Apache";s:7:"version";s:2:"$1";}i:584;a:3:{s:5:"regex";s:285:" (?!(?:AppleWebKit|brave|Cypress|Franz|Mailspring|Notion|Basecamp|Evernote|catalyst|ramboxpro|BlueMail|BeakerBrowser|Dezor|TweakStyle|Colibri|Polypane|Singlebox|Skye|VibeMate|(?:d|LT|Glass|Sushi|Flash|OhHai)Browser|Sizzy))([a-z0-9]*)(?:-desktop|-electron-app)?/(\\d+\\.[\\d.]+).*Electron/";s:4:"name";s:2:"$1";s:7:"version";s:2:"$2";}i:585;a:3:{s:5:"regex";s:17:"appname/([^/; ]*)";s:4:"name";s:2:"$1";s:7:"version";s:0:"";}i:586;a:3:{s:5:"regex";s:97:"(?!AlohaBrowser)([^/;]*)/(\\d+\\.[\\d.]+) \\((?:iPhone|iPad); (?:iOS|iPadOS) [0-9.]+; Scale/[0-9.]+\\)";s:4:"name";s:2:"$1";s:7:"version";s:2:"$2";}}}', ['allowed_classes' => false]);