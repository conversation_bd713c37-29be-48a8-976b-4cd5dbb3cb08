<?php return unserialize('a:2:{s:8:"lifetime";i:1753701387;s:4:"data";a:573:{i:0;a:4:{s:5:"regex";s:20:"Alti<PERSON>rowser/([\\d.]+)";s:4:"name";s:11:"AltiBrowser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:1;a:4:{s:5:"regex";s:35:"Maple (?!III)(\\d+[.\\d]+)|Maple\\d{4}";s:4:"name";s:5:"Maple";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Maple";}}i:2;a:4:{s:5:"regex";s:24:"Singlebox/(\\d+\\.[\\.\\d]+)";s:4:"name";s:9:"Singlebox";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:3;a:3:{s:5:"regex";s:14:"RCATorExplorer";s:4:"name";s:16:"RCA Tor Explorer";s:7:"version";s:0:"";}i:4;a:3:{s:5:"regex";s:9:"TQBrowser";s:4:"name";s:10:"TQ Browser";s:7:"version";s:0:"";}i:5;a:4:{s:5:"regex";s:13:"XXXAndroidApp";s:4:"name";s:8:"XnBrowse";s:7:"version";s:0:"";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:6;a:3:{s:5:"regex";s:8:"ProxyFox";s:4:"name";s:8:"ProxyFox";s:7:"version";s:0:"";}i:7;a:4:{s:5:"regex";s:14:"PrivacyBrowser";s:4:"name";s:15:"Privacy Browser";s:7:"version";s:0:"";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:8;a:3:{s:5:"regex";s:16:"TUSK/(\\d+[.\\d]+)";s:4:"name";s:4:"TUSK";s:7:"version";s:2:"$1";}i:9;a:3:{s:5:"regex";s:17:"Dezor/(\\d+[.\\d]+)";s:4:"name";s:5:"Dezor";s:7:"version";s:2:"$1";}i:10;a:3:{s:5:"regex";s:20:"OJR Browser/([\\d.]+)";s:4:"name";s:11:"OJR Browser";s:7:"version";s:2:"$1";}i:11;a:3:{s:5:"regex";s:22:"SecureBrowser/([\\d.]+)";s:4:"name";s:21:"AppTec Secure Browser";s:7:"version";s:2:"$1";}i:12;a:3:{s:5:"regex";s:14:"Veera/([\\d.]+)";s:4:"name";s:5:"Veera";s:7:"version";s:2:"$1";}i:13;a:4:{s:5:"regex";s:36:"Ninesky(?:-android-mobile)?/([\\d.]+)";s:4:"name";s:7:"Ninesky";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:14;a:3:{s:5:"regex";s:13:"Perk/([\\d.]+)";s:4:"name";s:4:"Perk";s:7:"version";s:2:"$1";}i:15;a:3:{s:5:"regex";s:21:"Presearch \\(Tempest\\)";s:4:"name";s:9:"Presearch";s:7:"version";s:0:"";}i:16;a:4:{s:5:"regex";s:39:"QtWeb Internet Browser(?:/(\\d+[.\\d]+))?";s:4:"name";s:5:"QtWeb";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:17;a:3:{s:5:"regex";s:27:"UPhoneWebBrowser(\\d+[.\\d]+)";s:4:"name";s:14:"UPhone Browser";s:7:"version";s:2:"$1";}i:18;a:3:{s:5:"regex";s:15:"MIB/(\\d+[.\\d]+)";s:4:"name";s:25:"Motorola Internet Browser";s:7:"version";s:2:"$1";}i:19;a:3:{s:5:"regex";s:29:"iNet Browser(?: (\\d+[.\\d]+))?";s:4:"name";s:12:"iNet Browser";s:7:"version";s:2:"$1";}i:20;a:4:{s:5:"regex";s:14:"Prism/([\\d.]+)";s:4:"name";s:5:"Prism";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Gecko";}}i:21;a:4:{s:5:"regex";s:18:"Awesomium/([\\d.]+)";s:4:"name";s:9:"Awesomium";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:22;a:3:{s:5:"regex";s:23:"Roccat(?:/(\\d+[.\\d]+))?";s:4:"name";s:6:"Roccat";s:7:"version";s:2:"$1";}i:23;a:4:{s:5:"regex";s:28:"Swiftweasel(?:/(\\d+[.\\d]+))?";s:4:"name";s:11:"Swiftweasel";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Gecko";}}i:24;a:3:{s:5:"regex";s:21:"wkbrowser (\\d+[.\\d]+)";s:4:"name";s:14:"Wukong Browser";s:7:"version";s:2:"$1";}i:25;a:3:{s:5:"regex";s:15:"KUN/(\\d+[.\\d]+)";s:4:"name";s:3:"KUN";s:7:"version";s:2:"$1";}i:26;a:4:{s:5:"regex";s:31:"NaenaraBrowser(?:/(\\d+[.\\d]+))?";s:4:"name";s:15:"Naenara Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Gecko";}}i:27;a:3:{s:5:"regex";s:29:"nook browser(?:/(\\d+[.\\d]+))?";s:4:"name";s:12:"NOOK Browser";s:7:"version";s:2:"$1";}i:28;a:3:{s:5:"regex";s:31:"xChaos_Arachne/5\\.(\\d+\\.[.\\d]+)";s:4:"name";s:7:"Arachne";s:7:"version";s:2:"$1";}i:29;a:3:{s:5:"regex";s:35:"WeltweitimnetzBrowser/(\\d+\\.[.\\d]+)";s:4:"name";s:22:"Weltweitimnetz Browser";s:7:"version";s:2:"$1";}i:30;a:4:{s:5:"regex";s:50:"(?:Ladybird|LibWeb\\+LibJS/.*Browser)/(\\d+\\.[.\\d]+)";s:4:"name";s:8:"Ladybird";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"LibWeb";}}i:31;a:3:{s:5:"regex";s:18:"Kitt/(\\d+\\.[.\\d]+)";s:4:"name";s:4:"Kitt";s:7:"version";s:2:"$1";}i:32;a:3:{s:5:"regex";s:15:"sppm_bizbrowser";s:4:"name";s:10:"BizBrowser";s:7:"version";s:0:"";}i:33;a:3:{s:5:"regex";s:21:"SkyLeap/(\\d+\\.[.\\d]+)";s:4:"name";s:7:"SkyLeap";s:7:"version";s:2:"$1";}i:34;a:3:{s:5:"regex";s:24:"MaxBrowser/(\\d+\\.[.\\d]+)";s:4:"name";s:10:"MaxBrowser";s:7:"version";s:2:"$1";}i:35;a:3:{s:5:"regex";s:24:"YouBrowser/(\\d+\\.[.\\d]+)";s:4:"name";s:10:"YouBrowser";s:7:"version";s:2:"$1";}i:36;a:3:{s:5:"regex";s:16:"MixerBox-Browser";s:4:"name";s:11:"MixerBox AI";s:7:"version";s:0:"";}i:37;a:3:{s:5:"regex";s:21:"EudoraWeb (\\d+[.\\d]+)";s:4:"name";s:9:"EudoraWeb";s:7:"version";s:2:"$1";}i:38;a:3:{s:5:"regex";s:5:"Eolie";s:4:"name";s:5:"Eolie";s:7:"version";s:0:"";}i:39;a:4:{s:5:"regex";s:16:"^w3m/(\\d+[.\\d]+)";s:4:"name";s:3:"w3m";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:10:"Text-based";}}i:40;a:4:{s:5:"regex";s:10:"Classilla/";s:4:"name";s:9:"Classilla";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"Clecko";}}i:41;a:4:{s:5:"regex";s:23:"WebianShell/(\\d+[.\\d]+)";s:4:"name";s:12:"Webian Shell";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Gecko";}}i:42;a:4:{s:5:"regex";s:25:"Vonkeror(?:/(\\d+[.\\d]+))?";s:4:"name";s:8:"Vonkeror";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Gecko";}}i:43;a:3:{s:5:"regex";s:18:"Wyzo/(\\d+\\.[.\\d]+)";s:4:"name";s:4:"Wyzo";s:7:"version";s:2:"$1";}i:44;a:3:{s:5:"regex";s:18:"Liri/(\\d+\\.[.\\d]+)";s:4:"name";s:12:"Liri Browser";s:7:"version";s:2:"$1";}i:45;a:3:{s:5:"regex";s:22:"Columbus/(\\d+\\.[.\\d]+)";s:4:"name";s:16:"Columbus Browser";s:7:"version";s:2:"$1";}i:46;a:4:{s:5:"regex";s:12:"GreenBrowser";s:4:"name";s:12:"GreenBrowser";s:7:"version";s:0:"";s:6:"engine";a:1:{s:7:"default";s:7:"Trident";}}i:47;a:3:{s:5:"regex";s:21:"K-Ninja/(\\d+\\.[.\\d]+)";s:4:"name";s:7:"K-Ninja";s:7:"version";s:2:"$1";}i:48;a:3:{s:5:"regex";s:16:"^PB(\\d+\\.[.\\d]+)";s:4:"name";s:13:"PirateBrowser";s:7:"version";s:2:"$1";}i:49;a:3:{s:5:"regex";s:25:"EastBrowser/(\\d+\\.[.\\d]+)";s:4:"name";s:12:"East Browser";s:7:"version";s:2:"$1";}i:50;a:3:{s:5:"regex";s:18:"Qiyu/(\\d+\\.[.\\d]+)";s:4:"name";s:4:"Qiyu";s:7:"version";s:2:"$1";}i:51;a:3:{s:5:"regex";s:25:"WebDiscover/(\\d+\\.[.\\d]+)";s:4:"name";s:11:"WebDiscover";s:7:"version";s:2:"$1";}i:52;a:3:{s:5:"regex";s:26:"LeganBrowser/(\\d+\\.[.\\d]+)";s:4:"name";s:13:"Legan Browser";s:7:"version";s:2:"$1";}i:53;a:4:{s:5:"regex";s:12:"Acoo Browser";s:4:"name";s:12:"Acoo Browser";s:7:"version";s:0:"";s:6:"engine";a:1:{s:7:"default";s:7:"Trident";}}i:54;a:3:{s:5:"regex";s:30:"Aplix_.*_browser/(\\d+\\.[.\\d]+)";s:4:"name";s:5:"Aplix";s:7:"version";s:2:"$1";}i:55;a:3:{s:5:"regex";s:19:"Mogok/(\\d+\\.[.\\d]+)";s:4:"name";s:13:"Mogok Browser";s:7:"version";s:2:"$1";}i:56;a:3:{s:5:"regex";s:41:"(?:IOS)?TrueLocationBrowser/(\\d+\\.[.\\d]+)";s:4:"name";s:20:"TrueLocation Browser";s:7:"version";s:2:"$1";}i:57;a:3:{s:5:"regex";s:13:"DiigoBrowser$";s:4:"name";s:13:"Diigo Browser";s:7:"version";s:0:"";}i:58;a:3:{s:5:"regex";s:28:".*OnBrowserLite(\\d+\\.[.\\d]+)";s:4:"name";s:14:"OnBrowser Lite";s:7:"version";s:2:"$1";}i:59;a:3:{s:5:"regex";s:20:"Bluefy/(\\d+\\.[.\\d]+)";s:4:"name";s:6:"Bluefy";s:7:"version";s:2:"$1";}i:60;a:3:{s:5:"regex";s:50:"(?:Novarra-Vision|Vision-Browser)(?:/(\\d+[.\\d]+))?";s:4:"name";s:21:"Vision Mobile Browser";s:7:"version";s:2:"$1";}i:61;a:3:{s:5:"regex";s:24:"SurfyBrowser/(\\d+[.\\d]+)";s:4:"name";s:13:"Surfy Browser";s:7:"version";s:2:"$1";}i:62;a:3:{s:5:"regex";s:13:"18\\+/([\\d.]+)";s:4:"name";s:19:"18+ Privacy Browser";s:7:"version";s:2:"$1";}i:63;a:4:{s:5:"regex";s:20:"GoKu-iOS/(\\d+[.\\d]+)";s:4:"name";s:4:"GoKu";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:64;a:3:{s:5:"regex";s:23:"Ask\\.com Mobile Browser";s:4:"name";s:7:"Ask.com";s:7:"version";s:0:"";}i:65;a:3:{s:5:"regex";s:16:"Bang/(\\d+[.\\d]+)";s:4:"name";s:4:"Bang";s:7:"version";s:2:"$1";}i:66;a:3:{s:5:"regex";s:31:"ManagedBrowser(?:/(\\d+[.\\d]+))?";s:4:"name";s:22:"Intune Managed Browser";s:7:"version";s:2:"$1";}i:67;a:3:{s:5:"regex";s:17:"Lotus/(\\d+[.\\d]+)";s:4:"name";s:5:"Lotus";s:7:"version";s:2:"$1";}i:68;a:3:{s:5:"regex";s:11:"JuziBrowser";s:4:"name";s:12:"JUZI Browser";s:7:"version";s:0:"";}i:69;a:3:{s:5:"regex";s:26:"Ninetails(?:/(\\d+[.\\d]+))?";s:4:"name";s:9:"Ninetails";s:7:"version";s:2:"$1";}i:70;a:3:{s:5:"regex";s:23:"Wexond(?:/(\\d+[.\\d]+))?";s:4:"name";s:6:"Wexond";s:7:"version";s:2:"$1";}i:71;a:3:{s:5:"regex";s:25:"catalyst(?:/(\\d+[.\\d]+))?";s:4:"name";s:8:"Catalyst";s:7:"version";s:2:"$1";}i:72;a:4:{s:5:"regex";s:27:"Impervious(?:/(\\d+[.\\d]+))?";s:4:"name";s:18:"Impervious Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Gecko";}}i:73;a:3:{s:5:"regex";s:31:"RakutenBrowser(?:/(\\d+[.\\d]+))?";s:4:"name";s:15:"Rakuten Browser";s:7:"version";s:2:"$1";}i:74;a:3:{s:5:"regex";s:33:"RakutenWebSearch(?:/(\\d+[.\\d]+))?";s:4:"name";s:18:"Rakuten Web Search";s:7:"version";s:2:"$1";}i:75;a:3:{s:5:"regex";s:25:"VibeMate(?:/(\\d+[.\\d]+))?";s:4:"name";s:8:"VibeMate";s:7:"version";s:2:"$1";}i:76;a:3:{s:5:"regex";s:38:"yixia\\.browser/com\\.donerbrowser\\.app/";s:4:"name";s:13:"Colom Browser";s:7:"version";s:0:"";}i:77;a:3:{s:5:"regex";s:21:"tararia/(\\d+\\.[.\\d]+)";s:4:"name";s:7:"tararia";s:7:"version";s:2:"$1";}i:78;a:4:{s:5:"regex";s:25:"SberBrowser/(\\d+\\.[.\\d]+)";s:4:"name";s:11:"SberBrowser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:79;a:3:{s:5:"regex";s:34:"Raspbian Chromium/(?:(\\d+[.\\d]+))?";s:4:"name";s:17:"Raspbian Chromium";s:7:"version";s:2:"$1";}i:80;a:3:{s:5:"regex";s:55:"Quick Search TV(?:/(?:Wild Moon Edition )?(\\d+[.\\d]+))?";s:4:"name";s:15:"Quick Search TV";s:7:"version";s:2:"$1";}i:81;a:4:{s:5:"regex";s:18:"Skye/(\\d+\\.[.\\d]+)";s:4:"name";s:4:"Skye";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:82;a:4:{s:5:"regex";s:6:"VD/\\d+";s:4:"name";s:10:"VD Browser";s:7:"version";s:0:"";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:83;a:4:{s:5:"regex";s:26:"\\[(?:HB/29|PB/(?:66|81))\\]";s:4:"name";s:7:"SecureX";s:7:"version";s:0:"";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:84;a:4:{s:5:"regex";s:10:"\\[HS/\\d+\\]";s:4:"name";s:10:"HotBrowser";s:7:"version";s:0:"";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:85;a:4:{s:5:"regex";s:10:"\\[PB/\\d+\\]";s:4:"name";s:13:"Proxy Browser";s:7:"version";s:0:"";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:86;a:4:{s:5:"regex";s:42:"^Normalized (?:iPad|iPhone) \\(iOS Safari\\)";s:4:"name";s:13:"Onion Browser";s:7:"version";s:0:"";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:87;a:3:{s:5:"regex";s:5:"fGet/";s:4:"name";s:4:"fGet";s:7:"version";s:0:"";}i:88;a:3:{s:5:"regex";s:22:"Nuviu/(?:(\\d+[.\\d]+))?";s:4:"name";s:5:"Nuviu";s:7:"version";s:2:"$1";}i:89;a:3:{s:5:"regex";s:23:"DoCoMo/(?:(\\d+[.\\d]+))?";s:4:"name";s:6:"DoCoMo";s:7:"version";s:2:"$1";}i:90;a:4:{s:5:"regex";s:38:"com\\.airfind\\.browser/(?:(\\d+[.\\d]+))?";s:4:"name";s:22:"Airfind Secure Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:91;a:4:{s:5:"regex";s:30:"ArcMobile2(?:/(\\d+\\.[.\\d]+);)?";s:4:"name";s:10:"Arc Search";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:92;a:4:{s:5:"regex";s:29:"Nuanti(?:Meta)?/(\\d+\\.[.\\d]+)";s:4:"name";s:11:"Nuanti Meta";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:93;a:4:{s:5:"regex";s:25:"RokuBrowser/(\\d+\\.[.\\d]+)";s:4:"name";s:12:"Roku Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:94;a:4:{s:5:"regex";s:25:"PicoBrowser/(\\d+\\.[.\\d]+)";s:4:"name";s:12:"PICO Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:95;a:4:{s:5:"regex";s:18:"Alva/(\\d+\\.[.\\d]+)";s:4:"name";s:4:"ALVA";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:96;a:4:{s:5:"regex";s:20:"Norton/(\\d+\\.[.\\d]+)";s:4:"name";s:22:"Norton Private Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:97;a:4:{s:5:"regex";s:17:"Odd/(\\d+\\.[.\\d]+)";s:4:"name";s:11:"Odd Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:98;a:4:{s:5:"regex";s:36:"Safari/537\\.36 (?:Browser|Navegador)";s:4:"name";s:11:"APN Browser";s:7:"version";s:0:"";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:99;a:4:{s:5:"regex";s:18:"YAGI/(\\d+\\.[.\\d]+)";s:4:"name";s:4:"YAGI";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:100;a:3:{s:5:"regex";s:14:"InspectBrowser";s:4:"name";s:15:"Inspect Browser";s:7:"version";s:0:"";}i:101;a:3:{s:5:"regex";s:33:"Keepsafe Browser(?:/(\\d+[.\\d]+))?";s:4:"name";s:16:"Keepsafe Browser";s:7:"version";s:2:"$1";}i:102;a:4:{s:5:"regex";s:30:"(.*)Vast Browser/(\\d+\\.[.\\d]+)";s:4:"name";s:12:"Vast Browser";s:7:"version";s:2:"$2";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:103;a:4:{s:5:"regex";s:6:"bloket";s:4:"name";s:6:"Bloket";s:7:"version";s:0:"";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:104;a:4:{s:5:"regex";s:26:"(.*)U Browser(\\d+\\.[.\\d]+)";s:4:"name";s:9:"U Browser";s:7:"version";s:2:"$2";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:105;a:4:{s:5:"regex";s:35:"Chrome/(\\d+\\.[.\\d]+).+TeslaBrowser/";s:4:"name";s:13:"Tesla Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:106;a:4:{s:5:"regex";s:29:"Chrome/(\\d+\\.[.\\d]+).+Sparrow";s:4:"name";s:14:"Viasat Browser";s:7:"version";s:2:"$1";s:6:"engine";a:2:{s:7:"default";s:6:"WebKit";s:8:"versions";a:1:{i:28;s:5:"Blink";}}}i:107;a:4:{s:5:"regex";s:19:"Sparrow/.+CFNetwork";s:4:"name";s:14:"Viasat Browser";s:7:"version";s:0:"";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:108;a:4:{s:5:"regex";s:18:"Lilo/(\\d+\\.[.\\d]+)";s:4:"name";s:4:"Lilo";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:109;a:4:{s:5:"regex";s:16:"Lilo/.+CFNetwork";s:4:"name";s:4:"Lilo";s:7:"version";s:0:"";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:110;a:4:{s:5:"regex";s:16:"lexi/(\\d+[.\\d]+)";s:4:"name";s:12:"Lexi Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:111;a:4:{s:5:"regex";s:18:"Floorp/(\\d+[.\\d]+)";s:4:"name";s:6:"Floorp";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Gecko";}}i:112;a:4:{s:5:"regex";s:23:"SurfBrowser/(\\d+[.\\d]+)";s:4:"name";s:12:"Surf Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:113;a:4:{s:5:"regex";s:7:"Decentr";s:4:"name";s:7:"Decentr";s:7:"version";s:0:"";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:114;a:4:{s:5:"regex";s:19:"youcare-android-app";s:4:"name";s:7:"YouCare";s:7:"version";s:0:"";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:115;a:4:{s:5:"regex";s:15:"youcare-ios-app";s:4:"name";s:7:"YouCare";s:7:"version";s:0:"";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:116;a:4:{s:5:"regex";s:15:"ABB/(\\d+[.\\d]+)";s:4:"name";s:15:"AdBlock Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:117;a:3:{s:5:"regex";s:26:"\\d+/tclwebkit(?:\\d+[.\\d]*)";s:4:"name";s:10:"BrowseHere";s:7:"version";s:0:"";}i:118;a:4:{s:5:"regex";s:23:"HiBrowser/v?(\\d+[.\\d]+)";s:4:"name";s:10:"Hi Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:119;a:3:{s:5:"regex";s:21:"CYBrowser/(\\d+[.\\d]+)";s:4:"name";s:9:"CyBrowser";s:7:"version";s:2:"$1";}i:120;a:4:{s:5:"regex";s:31:"Chrome/.+ SiteKiosk (\\d+[.\\d]+)";s:4:"name";s:9:"SiteKiosk";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:121;a:3:{s:5:"regex";s:21:"SiteKiosk (\\d+[.\\d]+)";s:4:"name";s:9:"SiteKiosk";s:7:"version";s:2:"$1";}i:122;a:3:{s:5:"regex";s:26:"ReqwirelessWeb/(\\d+[.\\d]+)";s:4:"name";s:21:"Reqwireless WebViewer";s:7:"version";s:2:"$1";}i:123;a:4:{s:5:"regex";s:22:"T\\+Browser/(\\d+[.\\d]+)";s:4:"name";s:9:"T+Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:124;a:4:{s:5:"regex";s:35:"Private Browser/(\\d+[.\\d]+) Chrome/";s:4:"name";s:22:"Secure Private Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:125;a:4:{s:5:"regex";s:24:"ChanjetCloud/(\\d+[.\\d]+)";s:4:"name";s:12:"ChanjetCloud";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:126;a:4:{s:5:"regex";s:24:"SushiBrowser/(\\d+[.\\d]+)";s:4:"name";s:13:"Sushi Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:127;a:4:{s:5:"regex";s:20:"dBrowser/(\\d+[.\\d]+)";s:4:"name";s:14:"Peeps dBrowser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:128;a:4:{s:5:"regex";s:21:"LTBrowser/(\\d+[.\\d]+)";s:4:"name";s:10:"LT Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:129;a:4:{s:5:"regex";s:27:"lagatos-browser/(\\d+[.\\d]+)";s:4:"name";s:15:"Lagatos Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:130;a:4:{s:5:"regex";s:30:"psi-secure-browser/(\\d+[.\\d]+)";s:4:"name";s:18:"PSI Secure Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:131;a:4:{s:5:"regex";s:26:"Harman_Browser/(\\d+[.\\d]+)";s:4:"name";s:14:"Harman Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:132;a:4:{s:5:"regex";s:26:"bonsai-browser/(\\d+[.\\d]+)";s:4:"name";s:6:"Bonsai";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:133;a:4:{s:5:"regex";s:27:"spectre-browser/(\\d+[.\\d]+)";s:4:"name";s:15:"Spectre Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:134;a:4:{s:5:"regex";s:24:"FlashBrowser/(\\d+[.\\d]+)";s:4:"name";s:13:"Flash Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:135;a:4:{s:5:"regex";s:23:"Secure/(?:(\\d+[.\\d]+))?";s:4:"name";s:14:"Secure Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:136;a:4:{s:5:"regex";s:17:"Arvin/(\\d+[.\\d]+)";s:4:"name";s:5:"Arvin";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:137;a:4:{s:5:"regex";s:35:"Version/.+Chrome/.+EdgW/(\\d+[.\\d]+)";s:4:"name";s:12:"Edge WebView";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:138;a:4:{s:5:"regex";s:28:"Mandarin Browser/(\\d+[.\\d]+)";s:4:"name";s:8:"Mandarin";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:139;a:4:{s:5:"regex";s:19:"Torrent/(\\d+[.\\d]+)";s:4:"name";s:9:"Maelstrom";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:140;a:4:{s:5:"regex";s:17:"Helio/(\\d+[.\\d]+)";s:4:"name";s:5:"Helio";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:141;a:4:{s:5:"regex";s:23:"7654Browser/(\\d+[.\\d]+)";s:4:"name";s:12:"7654 Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:142;a:4:{s:5:"regex";s:18:"Qazweb/(\\d+[.\\d]+)";s:4:"name";s:6:"Qazweb";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Gecko";}}i:143;a:4:{s:5:"regex";s:20:"Degdegan/(\\d+[.\\d]+)";s:4:"name";s:9:"deg-degan";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:144;a:4:{s:5:"regex";s:18:"JavaFX/(\\d+[.\\d]+)";s:4:"name";s:6:"JavaFX";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:145;a:4:{s:5:"regex";s:18:"Chedot/(\\d+[.\\d]+)";s:4:"name";s:6:"Chedot";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:146;a:4:{s:5:"regex";s:40:"Chrome/(\\d+\\.[.\\d]+) .*\\(Chromium GOST\\)";s:4:"name";s:13:"Chromium GOST";s:7:"version";s:2:"$1";s:6:"engine";a:2:{s:7:"default";s:6:"WebKit";s:8:"versions";a:1:{i:28;s:5:"Blink";}}}i:147;a:4:{s:5:"regex";s:45:"(?:DeledaoPersonal|DeledaoFamily)/(\\d+[.\\d]+)";s:4:"name";s:7:"Deledao";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:148;a:4:{s:5:"regex";s:22:"HasBrowser/(\\d+[.\\d]+)";s:4:"name";s:10:"HasBrowser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:149;a:4:{s:5:"regex";s:18:"Byffox/(\\d+[.\\d]+)";s:4:"name";s:6:"Byffox";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Gecko";}}i:150;a:4:{s:5:"regex";s:42:"Chrome/(\\d+\\.[.\\d]+) .*AgentWeb.+UCBrowser";s:4:"name";s:11:"CoolBrowser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:151;a:4:{s:5:"regex";s:22:"DotBrowser/(\\d+[.\\d]+)";s:4:"name";s:11:"Dot Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:152;a:4:{s:5:"regex";s:27:"CravingExplorer/(\\d+[.\\d]+)";s:4:"name";s:16:"Craving Explorer";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:153;a:3:{s:5:"regex";s:22:"DeskBrowse/(\\d+[.\\d]+)";s:4:"name";s:10:"DeskBrowse";s:7:"version";s:2:"$1";}i:154;a:3:{s:5:"regex";s:19:"Lolifox/(\\d+[.\\d]+)";s:4:"name";s:7:"Lolifox";s:7:"version";s:2:"$1";}i:155;a:4:{s:5:"regex";s:21:"PiBrowser/(\\d+[.\\d]+)";s:4:"name";s:10:"Pi Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:156;a:4:{s:5:"regex";s:34:"qutebrowser/(\\d+\\.[.\\d]+) .*Chrome";s:4:"name";s:11:"Qutebrowser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:157;a:3:{s:5:"regex";s:28:"qutebrowser(?:/(\\d+[.\\d]+))?";s:4:"name";s:11:"Qutebrowser";s:7:"version";s:2:"$1";}i:158;a:4:{s:5:"regex";s:17:"flast/(\\d+[.\\d]+)";s:4:"name";s:5:"Flast";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:159;a:4:{s:5:"regex";s:23:"PolyBrowser/(\\d+[.\\d]+)";s:4:"name";s:11:"PolyBrowser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Gecko";}}i:160;a:4:{s:5:"regex";s:29:"Chrome.+BriskBard/(\\d+[.\\d]+)";s:4:"name";s:9:"BriskBard";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:161;a:3:{s:5:"regex";s:26:"BriskBard(?:/(\\d+[.\\d]+))?";s:4:"name";s:9:"BriskBard";s:7:"version";s:2:"$1";}i:162;a:4:{s:5:"regex";s:33:"GinxDroid(?:Browser)?/(\\d+[.\\d]+)";s:4:"name";s:17:"GinxDroid Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:163;a:4:{s:5:"regex";s:27:"Avira(?:Scout)?/(\\d+[.\\d]+)";s:4:"name";s:20:"Avira Secure Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:164;a:4:{s:5:"regex";s:24:"VenusBrowser/(\\d+[.\\d]+)";s:4:"name";s:13:"Venus Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:165;a:4:{s:5:"regex";s:33:"Chrome.+Otter(?:[ /](\\d+[.\\d]+))?";s:4:"name";s:13:"Otter Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:166;a:3:{s:5:"regex";s:25:"Otter(?:[ /](\\d+[.\\d]+))?";s:4:"name";s:13:"Otter Browser";s:7:"version";s:2:"$1";}i:167;a:4:{s:5:"regex";s:25:"Chrome.+Smooz/(\\d+[.\\d]+)";s:4:"name";s:5:"Smooz";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:168;a:3:{s:5:"regex";s:17:"Smooz/(\\d+[.\\d]+)";s:4:"name";s:5:"Smooz";s:7:"version";s:2:"$1";}i:169;a:4:{s:5:"regex";s:27:"BanglaBrowser/(\\d+\\.[.\\d]+)";s:4:"name";s:14:"Bangla Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:170;a:4:{s:5:"regex";s:21:"Cornowser/(\\d+[.\\d]+)";s:4:"name";s:9:"Cornowser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:171;a:3:{s:5:"regex";s:16:"Orca/(\\d+[.\\d]+)";s:4:"name";s:4:"Orca";s:7:"version";s:2:"$1";}i:172;a:4:{s:5:"regex";s:69:"Android (?:[\\d.]+;) ?(?:[^;]+;)? Flow\\) AppleWebKit/537.+Chrome/\\d{3}";s:4:"name";s:12:"Flow Browser";s:7:"version";s:0:"";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:173;a:4:{s:5:"regex";s:20:"Flow/(?:(\\d+[.\\d]+))";s:4:"name";s:4:"Flow";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:9:"EkiohFlow";}}i:174;a:4:{s:5:"regex";s:21:"Ekioh/(?:(\\d+[.\\d]+))";s:4:"name";s:4:"Flow";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:9:"EkiohFlow";}}i:175;a:4:{s:5:"regex";s:18:"xStand/(\\d+[.\\d]+)";s:4:"name";s:6:"xStand";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:176;a:3:{s:5:"regex";s:18:"Biyubi/(\\d+[.\\d]+)";s:4:"name";s:6:"Biyubi";s:7:"version";s:2:"$1";}i:177;a:4:{s:5:"regex";s:66:"(?:Perfect%20Browser(?:-iPad)?|Perfect(?:BrowserPro)?)/(\\d+[.\\d]+)";s:4:"name";s:15:"Perfect Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:178;a:3:{s:5:"regex";s:28:"Browser/Phantom/V(\\d+[.\\d]+)";s:4:"name";s:15:"Phantom Browser";s:7:"version";s:2:"$1";}i:179;a:3:{s:5:"regex";s:29:"AwoX(?:/(\\d+[.\\d]+))? Browser";s:4:"name";s:4:"AwoX";s:7:"version";s:2:"$1";}i:180;a:4:{s:5:"regex";s:19:"Zetakey/(\\d+[.\\d]+)";s:4:"name";s:7:"Zetakey";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:181;a:3:{s:5:"regex";s:32:"PlayFreeBrowser/(?:(\\d+[.\\d]+))?";s:4:"name";s:16:"PlayFree Browser";s:7:"version";s:2:"$1";}i:182;a:4:{s:5:"regex";s:43:"(?:chimlac_browser|chimlac)/(?:(\\d+[.\\d]+))";s:4:"name";s:8:"Chim Lac";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:183;a:4:{s:5:"regex";s:20:"Odin/(?:(\\d+[.\\d]+))";s:4:"name";s:4:"Odin";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:184;a:4:{s:5:"regex";s:20:"Tbrowser/(\\d+[.\\d]+)";s:4:"name";s:9:"T-Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:185;a:4:{s:5:"regex";s:17:"com\\.tcl\\.browser";s:4:"name";s:10:"BrowseHere";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:186;a:4:{s:5:"regex";s:24:"WhaleBrowser/(\\d+[.\\d]+)";s:4:"name";s:16:"Whale TV Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:187;a:4:{s:5:"regex";s:40:"SFive(?:_Android)?/.+ Chrome/(\\d+[.\\d]+)";s:4:"name";s:5:"SFive";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:188;a:3:{s:5:"regex";s:21:"SFive_IOS/(\\d+[.\\d]+)";s:4:"name";s:5:"SFive";s:7:"version";s:2:"$1";}i:189;a:3:{s:5:"regex";s:31:"Navigateur web/(?:(\\d+[.\\d]+))?";s:4:"name";s:14:"Navigateur Web";s:7:"version";s:2:"$1";}i:190;a:4:{s:5:"regex";s:24:"Sraf(?:[/ ](\\d+[.\\d]+))?";s:4:"name";s:13:"Seraphic Sraf";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:191;a:4:{s:5:"regex";s:29:"SeewoBrowser/(?:(\\d+[.\\d]+))?";s:4:"name";s:13:"Seewo Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:192;a:3:{s:5:"regex";s:41:"(?:Kode(?:iOS)?/(?:(\\d+[.\\d]+))?|TansoDL)";s:4:"name";s:12:"Kode Browser";s:7:"version";s:2:"$1";}i:193;a:4:{s:5:"regex";s:18:"UR/(?:(\\d+[.\\d]+))";s:4:"name";s:10:"UR Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:194;a:4:{s:5:"regex";s:18:"OceanHero/([.\\d]+)";s:4:"name";s:9:"OceanHero";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:195;a:4:{s:5:"regex";s:36:"Chrome/.+ SLBrowser/(?:(\\d+[.\\d]+))?";s:4:"name";s:20:"Smart Lenovo Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:196;a:3:{s:5:"regex";s:26:"SLBrowser/(?:(\\d+[.\\d]+))?";s:4:"name";s:20:"Smart Lenovo Browser";s:7:"version";s:2:"$1";}i:197;a:3:{s:5:"regex";s:7:"Browzar";s:4:"name";s:7:"Browzar";s:7:"version";s:0:"";}i:198;a:4:{s:5:"regex";s:24:"Stargon/(?:(\\d+[.\\d]+))?";s:4:"name";s:7:"Stargon";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:199;a:4:{s:5:"regex";s:27:"NFSBrowser/(?:(\\d+[.\\d]+))?";s:4:"name";s:11:"NFS Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:200;a:3:{s:5:"regex";s:20:"Borealis/(\\d+[.\\d]+)";s:4:"name";s:18:"Borealis Navigator";s:7:"version";s:2:"$1";}i:201;a:4:{s:5:"regex";s:28:"YoloBrowser/(?:(\\d+[.\\d]+))?";s:4:"name";s:12:"Yolo Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:202;a:3:{s:5:"regex";s:20:"PHX/(?:(\\d+[.\\d]+))?";s:4:"name";s:15:"Phoenix Browser";s:7:"version";s:2:"$1";}i:203;a:3:{s:5:"regex";s:28:"PrivacyWall/(?:(\\d+[.\\d]+))?";s:4:"name";s:11:"PrivacyWall";s:7:"version";s:2:"$1";}i:204;a:3:{s:5:"regex";s:22:"Ghostery:?(\\d+[.\\d]+)?";s:4:"name";s:24:"Ghostery Privacy Browser";s:7:"version";s:2:"$1";}i:205;a:3:{s:5:"regex";s:5:"Cliqz";s:4:"name";s:5:"Cliqz";s:7:"version";s:0:"";}i:206;a:4:{s:5:"regex";s:53:"Firefox/.*(?:Turkcell-)?YaaniBrowser(?:/(\\d+[.\\d]+))?";s:4:"name";s:13:"Yaani Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Gecko";}}i:207;a:4:{s:5:"regex";s:43:"(?:Turkcell-)?YaaniBrowser(?:/(\\d+[.\\d]+))?";s:4:"name";s:13:"Yaani Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:208;a:3:{s:5:"regex";s:20:"SEB/(?:(\\d+[.\\d]+))?";s:4:"name";s:17:"Safe Exam Browser";s:7:"version";s:2:"$1";}i:209;a:3:{s:5:"regex";s:24:"Colibri/(?:(\\d+[.\\d]+))?";s:4:"name";s:7:"Colibri";s:7:"version";s:2:"$1";}i:210;a:3:{s:5:"regex";s:22:"Xvast/(?:(\\d+[.\\d]+))?";s:4:"name";s:5:"Xvast";s:7:"version";s:2:"$1";}i:211;a:3:{s:5:"regex";s:32:"TungstenBrowser/(?:(\\d+[.\\d]+))?";s:4:"name";s:8:"Tungsten";s:7:"version";s:2:"$1";}i:212;a:3:{s:5:"regex";s:31:"Lulumi-browser/(?:(\\d+[.\\d]+))?";s:4:"name";s:6:"Lulumi";s:7:"version";s:2:"$1";}i:213;a:3:{s:5:"regex";s:25:"ybrowser/(?:(\\d+[.\\d]+))?";s:4:"name";s:20:"Yahoo! Japan Browser";s:7:"version";s:2:"$1";}i:214;a:3:{s:5:"regex";s:33:"iLunascapeLite/(?:(\\d+\\.[.\\d]+))?";s:4:"name";s:14:"Lunascape Lite";s:7:"version";s:2:"$1";}i:215;a:4:{s:5:"regex";s:43:"Chrome/.+ i?Lunascape(?:[/ ](\\d+\\.[.\\d]+))?";s:4:"name";s:9:"Lunascape";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:216;a:4:{s:5:"regex";s:33:"i?Lunascape(?:[/ ](\\d+\\.[.\\d]+))?";s:4:"name";s:9:"Lunascape";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:0:"";}}i:217;a:3:{s:5:"regex";s:25:"Polypane/(?:(\\d+[.\\d]+))?";s:4:"name";s:8:"Polypane";s:7:"version";s:2:"$1";}i:218;a:3:{s:5:"regex";s:29:"OhHaiBrowser/(?:(\\d+[.\\d]+))?";s:4:"name";s:13:"OhHai Browser";s:7:"version";s:2:"$1";}i:219;a:3:{s:5:"regex";s:22:"Sizzy/(?:(\\d+[.\\d]+))?";s:4:"name";s:5:"Sizzy";s:7:"version";s:2:"$1";}i:220;a:3:{s:5:"regex";s:29:"GlassBrowser/(?:(\\d+[.\\d]+))?";s:4:"name";s:13:"Glass Browser";s:7:"version";s:2:"$1";}i:221;a:4:{s:5:"regex";s:23:"ToGate/(?:(\\d+[.\\d]+))?";s:4:"name";s:6:"ToGate";s:7:"version";s:2:"$1";s:6:"engine";a:2:{s:7:"default";s:6:"WebKit";s:8:"versions";a:1:{i:28;s:5:"Blink";}}}i:222;a:3:{s:5:"regex";s:55:"(?:AirWatch Browser v|AirWatchBrowser/)(?:(\\d+[.\\d]+))?";s:4:"name";s:15:"VMware AirWatch";s:7:"version";s:2:"$1";}i:223;a:4:{s:5:"regex";s:15:"AOL (\\d+[.\\d]+)";s:4:"name";s:12:"AOL Explorer";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:7:"Trident";}}i:224;a:3:{s:5:"regex";s:20:"ADG/(?:(\\d+[.\\d]+))?";s:4:"name";s:11:"AOL Desktop";s:7:"version";s:2:"$1";}i:225;a:3:{s:5:"regex";s:33:"Elements Browser/(?:(\\d+[.\\d]+))?";s:4:"name";s:16:"Elements Browser";s:7:"version";s:2:"$1";}i:226;a:3:{s:5:"regex";s:17:"Light/(\\d+[.\\d]+)";s:4:"name";s:5:"Light";s:7:"version";s:2:"$1";}i:227;a:3:{s:5:"regex";s:40:"Valve Steam GameOverlay/(?:(\\d+[.\\d]+))?";s:4:"name";s:21:"Steam In-Game Overlay";s:7:"version";s:2:"$1";}i:228;a:3:{s:5:"regex";s:27:"115Browser/(?:(\\d+[.\\d]+))?";s:4:"name";s:11:"115 Browser";s:7:"version";s:2:"$1";}i:229;a:4:{s:5:"regex";s:21:"Atom/(?:(\\d+[.\\d]+))?";s:4:"name";s:4:"Atom";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:230;a:3:{s:5:"regex";s:20:"Wolvic/(\\d+\\.[.\\d]+)";s:4:"name";s:6:"Wolvic";s:7:"version";s:2:"$1";}i:231;a:3:{s:5:"regex";s:18:"Mobile VR.+Firefox";s:4:"name";s:15:"Firefox Reality";s:7:"version";s:0:"";}i:232;a:4:{s:5:"regex";s:20:"AVG(?:/(\\d+[.\\d]+))?";s:4:"name";s:18:"AVG Secure Browser";s:7:"version";s:2:"$1";s:6:"engine";a:2:{s:7:"default";s:6:"WebKit";s:8:"versions";a:1:{i:28;s:5:"Blink";}}}i:233;a:4:{s:5:"regex";s:14:"AT/(\\d+[.\\d]+)";s:4:"name";s:18:"AVG Secure Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:234;a:3:{s:5:"regex";s:22:"Start/(?:(\\d+[.\\d]+))?";s:4:"name";s:22:"START Internet Browser";s:7:"version";s:2:"$1";}i:235;a:3:{s:5:"regex";s:24:"Lovense(?:/(\\d+[.\\d]+))?";s:4:"name";s:15:"Lovense Browser";s:7:"version";s:2:"$1";}i:236;a:4:{s:5:"regex";s:57:"(?:com\\.airfind\\.deltabrowser|AirSearch)(?:/(\\d+[.\\d]+))?";s:4:"name";s:13:"Delta Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:237;a:3:{s:5:"regex";s:41:"(?:Ordissimo|webissimo3)(?:/(\\d+[.\\d]+))?";s:4:"name";s:9:"Ordissimo";s:7:"version";s:2:"$1";}i:238;a:4:{s:5:"regex";s:25:"CCleaner(?:/(\\d+[.\\d]+))?";s:4:"name";s:8:"CCleaner";s:7:"version";s:2:"$1";s:6:"engine";a:2:{s:7:"default";s:6:"WebKit";s:8:"versions";a:1:{i:28;s:5:"Blink";}}}i:239;a:4:{s:5:"regex";s:26:"AlohaLite(?:/(\\d+[.\\d]+))?";s:4:"name";s:18:"Aloha Browser Lite";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:240;a:3:{s:5:"regex";s:27:"TaoBrowser(?:/(\\d+[.\\d]+))?";s:4:"name";s:11:"Tao Browser";s:7:"version";s:2:"$1";}i:241;a:3:{s:5:"regex";s:23:"Falkon(?:/(\\d+[.\\d]+))?";s:4:"name";s:6:"Falkon";s:7:"version";s:2:"$1";}i:242;a:3:{s:5:"regex";s:22:"mCent(?:/(\\d+[.\\d]+))?";s:4:"name";s:5:"mCent";s:7:"version";s:2:"$1";}i:243;a:3:{s:5:"regex";s:25:"SalamWeb(?:/(\\d+[.\\d]+))?";s:4:"name";s:8:"SalamWeb";s:7:"version";s:2:"$1";}i:244;a:3:{s:5:"regex";s:26:"BlackHawk(?:/(\\d+[.\\d]+))?";s:4:"name";s:9:"BlackHawk";s:7:"version";s:2:"$1";}i:245;a:3:{s:5:"regex";s:23:"Minimo(?:/(\\d+[.\\d]+))?";s:4:"name";s:6:"Minimo";s:7:"version";s:2:"$1";}i:246;a:3:{s:5:"regex";s:20:"WIB(?:/(\\d+[.\\d]+))?";s:4:"name";s:21:"Wear Internet Browser";s:7:"version";s:2:"$1";}i:247;a:3:{s:5:"regex";s:18:"Origyn Web Browser";s:4:"name";s:18:"Origyn Web Browser";s:7:"version";s:0:"";}i:248;a:3:{s:5:"regex";s:22:"Kinza(?:/(\\d+[.\\d]+))?";s:4:"name";s:5:"Kinza";s:7:"version";s:2:"$1";}i:249;a:4:{s:5:"regex";s:25:"Beamrise(?:/(\\d+[.\\d]+))?";s:4:"name";s:8:"Beamrise";s:7:"version";s:2:"$1";s:6:"engine";a:2:{s:7:"default";s:6:"WebKit";s:8:"versions";a:1:{i:28;s:5:"Blink";}}}i:250;a:3:{s:5:"regex";s:21:"Faux(?:/(\\d+[.\\d]+))?";s:4:"name";s:12:"Faux Browser";s:7:"version";s:2:"$1";}i:251;a:3:{s:5:"regex";s:31:"splash Version(?:/(\\d+[.\\d]+))?";s:4:"name";s:6:"Splash";s:7:"version";s:2:"$1";}i:252;a:3:{s:5:"regex";s:26:"MZBrowser(?:/(\\d+[.\\d]+))?";s:4:"name";s:13:"Meizu Browser";s:7:"version";s:2:"$1";}i:253;a:3:{s:5:"regex";s:27:"COSBrowser(?:/(\\d+[.\\d]+))?";s:4:"name";s:11:"COS Browser";s:7:"version";s:2:"$1";}i:254;a:3:{s:5:"regex";s:23:"Crusta(?:/(\\d+[.\\d]+))?";s:4:"name";s:6:"Crusta";s:7:"version";s:2:"$1";}i:255;a:4:{s:5:"regex";s:36:"Hawk/TurboBrowser(?:/v?(\\d+[.\\d]+))?";s:4:"name";s:18:"Hawk Turbo Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:256;a:4:{s:5:"regex";s:36:"Hawk/QuickBrowser(?:/v?(\\d+[.\\d]+))?";s:4:"name";s:18:"Hawk Quick Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:257;a:4:{s:5:"regex";s:22:"FreeU(?:/(\\d+[.\\d]+))?";s:4:"name";s:5:"FreeU";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:258;a:4:{s:5:"regex";s:27:"NoxBrowser(?:/(\\d+[.\\d]+))?";s:4:"name";s:11:"Nox Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:259;a:4:{s:5:"regex";s:25:"Basilisk(?:/(\\d+[.\\d]+))?";s:4:"name";s:8:"Basilisk";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"Goanna";}}i:260;a:4:{s:5:"regex";s:31:"SputnikBrowser(?:/(\\d+[.\\d]+))?";s:4:"name";s:15:"Sputnik Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:261;a:4:{s:5:"regex";s:27:"TNSBrowser(?:/(\\d+[.\\d]+))?";s:4:"name";s:9:"K.Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:262;a:4:{s:5:"regex";s:30:"OculusBrowser(?:/(\\d+[.\\d]+))?";s:4:"name";s:14:"Oculus Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:263;a:4:{s:5:"regex";s:44:"Jio(?:Browser|Pages|Sphere)(?:/(\\d+[.\\d]+))?";s:4:"name";s:9:"JioSphere";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:264;a:4:{s:5:"regex";s:22:"SY/(\\d+[.\\d]+) Chrome/";s:4:"name";s:14:"Stampy Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:265;a:4:{s:5:"regex";s:31:"Chrome/.+ Hola(?:/(\\d+[.\\d]+))?";s:4:"name";s:13:"hola! Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:266;a:4:{s:5:"regex";s:24:"SlimBoat/(?:(\\d+[.\\d]+))";s:4:"name";s:8:"SlimBoat";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:267;a:4:{s:5:"regex";s:23:"Slimjet/(?:(\\d+[.\\d]+))";s:4:"name";s:7:"Slimjet";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:268;a:4:{s:5:"regex";s:32:"(?:7Star|Kuaiso)/(?:(\\d+[.\\d]+))";s:4:"name";s:5:"7Star";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:269;a:4:{s:5:"regex";s:23:"MxNitro/(?:(\\d+[.\\d]+))";s:4:"name";s:7:"MxNitro";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:270;a:4:{s:5:"regex";s:30:"HuaweiBrowser(?:/(\\d+[.\\d]+))?";s:4:"name";s:21:"Huawei Browser Mobile";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:271;a:4:{s:5:"regex";s:16:"HBPC/(\\d+[.\\d]+)";s:4:"name";s:14:"Huawei Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:272;a:3:{s:5:"regex";s:13:"ZTE ?Browser/";s:4:"name";s:11:"ZTE Browser";s:7:"version";s:2:"$1";}i:273;a:3:{s:5:"regex";s:28:"VivoBrowser(?:/(\\d+[.\\d]+))?";s:4:"name";s:12:"vivo Browser";s:7:"version";s:2:"$1";}i:274;a:3:{s:5:"regex";s:30:"RealmeBrowser(?:/(\\d+[.\\d]+))?";s:4:"name";s:14:"Realme Browser";s:7:"version";s:2:"$1";}i:275;a:4:{s:5:"regex";s:35:"Beaker ?Browser(?:[/ ](\\d+[.\\d]+))?";s:4:"name";s:14:"Beaker Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:276;a:4:{s:5:"regex";s:20:"QwantiOS/(\\d+[.\\d]+)";s:4:"name";s:12:"Qwant Mobile";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:277;a:4:{s:5:"regex";s:37:"Chrome/.*QwantMobile(?:/(\\d+[.\\d]+))?";s:4:"name";s:12:"Qwant Mobile";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:278;a:4:{s:5:"regex";s:28:"QwantMobile(?:/(\\d+[.\\d]+))?";s:4:"name";s:12:"Qwant Mobile";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:0:"";}}i:279;a:4:{s:5:"regex";s:17:"Qwant/(\\d+[.\\d]+)";s:4:"name";s:12:"Qwant Mobile";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Gecko";}}i:280;a:4:{s:5:"regex";s:27:"TenFourFox(?:/(\\d+[.\\d]+))?";s:4:"name";s:10:"TenFourFox";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Gecko";}}i:281;a:4:{s:5:"regex";s:36:"Chrome/.+ AOLShield(?:/(\\d+[.\\d]+))?";s:4:"name";s:14:"AOL Shield Pro";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:282;a:4:{s:5:"regex";s:26:"AOLShield(?:/(\\d+[.\\d]+))?";s:4:"name";s:10:"AOL Shield";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Gecko";}}i:283;a:4:{s:5:"regex";s:36:"(?<!motorola |; )Edge[ /](\\d+[.\\d]+)";s:4:"name";s:14:"Microsoft Edge";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:4:"Edge";}}i:284;a:4:{s:5:"regex";s:21:"EdgiOS[ /](\\d+[.\\d]+)";s:4:"name";s:14:"Microsoft Edge";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:285;a:4:{s:5:"regex";s:19:"EdgA[ /](\\d+[.\\d]+)";s:4:"name";s:14:"Microsoft Edge";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:286;a:4:{s:5:"regex";s:18:"Edg[ /](\\d+[.\\d]+)";s:4:"name";s:14:"Microsoft Edge";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:287;a:3:{s:5:"regex";s:40:"QIHU 360[ES]E|QihooBrowserHD/(\\d+[.\\d]+)";s:4:"name";s:18:"360 Secure Browser";s:7:"version";s:2:"$1";}i:288;a:3:{s:5:"regex";s:35:"Chrome.+Safari/537\\.36/(\\d+[.\\d]+)$";s:4:"name";s:18:"360 Secure Browser";s:7:"version";s:2:"$1";}i:289;a:3:{s:5:"regex";s:54:"360 Aphone Browser(?:[ /]?\\(?(\\d+[.\\d]+)(?:beta)?\\)?)?";s:4:"name";s:17:"360 Phone Browser";s:7:"version";s:2:"$1";}i:290;a:4:{s:5:"regex";s:32:"SailfishBrowser(?:/(\\d+[.\\d]+))?";s:4:"name";s:16:"Sailfish Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:0:"";}}i:291;a:4:{s:5:"regex";s:23:"IceCat(?:/(\\d+[.\\d]+))?";s:4:"name";s:6:"IceCat";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Gecko";}}i:292;a:4:{s:5:"regex";s:7:"Mobicip";s:4:"name";s:7:"Mobicip";s:7:"version";s:0:"";s:6:"engine";a:1:{s:7:"default";s:5:"Gecko";}}i:293;a:4:{s:5:"regex";s:23:"Camino(?:/(\\d+[.\\d]+))?";s:4:"name";s:6:"Camino";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Gecko";}}i:294;a:4:{s:5:"regex";s:25:"Waterfox(?:/(\\d+[.\\d]+))?";s:4:"name";s:8:"Waterfox";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Gecko";}}i:295;a:4:{s:5:"regex";s:24:"VertexSurf/(\\d+\\.[.\\d]+)";s:4:"name";s:11:"Vertex Surf";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:296;a:4:{s:5:"regex";s:39:"Chrome/.+ AlohaBrowser(?:/(\\d+[.\\d]+))?";s:4:"name";s:13:"Aloha Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:297;a:3:{s:5:"regex";s:37:"AlohaBrowser(?:App)?(?:/(\\d+[.\\d]+))?";s:4:"name";s:13:"Aloha Browser";s:7:"version";s:2:"$1";}i:298;a:3:{s:5:"regex";s:6:"Aloha/";s:4:"name";s:13:"Aloha Browser";s:7:"version";s:0:"";}i:299;a:4:{s:5:"regex";s:62:"Chrome.+(?:Avast(?:SecureBrowser)?|ASW|Safer)(?:/(\\d+[.\\d]+))?";s:4:"name";s:20:"Avast Secure Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:300;a:4:{s:5:"regex";s:54:"(?:Avast(?:SecureBrowser)?|ASW|Safer)(?:/(\\d+[.\\d]+))?";s:4:"name";s:20:"Avast Secure Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:301;a:4:{s:5:"regex";s:20:"Epic(?:/(\\d+[.\\d]+))";s:4:"name";s:4:"Epic";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Gecko";}}i:302;a:4:{s:5:"regex";s:23:"Fennec(?:/(\\d+[.\\d]+))?";s:4:"name";s:6:"Fennec";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Gecko";}}i:303;a:4:{s:5:"regex";s:35:"Firefox.*Tablet browser (\\d+[.\\d]+)";s:4:"name";s:6:"MicroB";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Gecko";}}i:304;a:4:{s:5:"regex";s:30:"Maemo Browser(?: (\\d+[.\\d]+))?";s:4:"name";s:6:"MicroB";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Gecko";}}i:305;a:3:{s:5:"regex";s:29:"Deepnet Explorer (\\d+[.\\d]+)?";s:4:"name";s:16:"Deepnet Explorer";s:7:"version";s:2:"$1";}i:306;a:4:{s:5:"regex";s:14:"Avant ?Browser";s:4:"name";s:13:"Avant Browser";s:7:"version";s:0:"";s:6:"engine";a:1:{s:7:"default";s:0:"";}}i:307;a:3:{s:5:"regex";s:28:"OppoBrowser(?:/(\\d+[.\\d]+))?";s:4:"name";s:12:"Oppo Browser";s:7:"version";s:2:"$1";}i:308;a:4:{s:5:"regex";s:31:"Chrome/(\\d+\\.[.\\d]+) .*MRCHROME";s:4:"name";s:5:"Amigo";s:7:"version";s:2:"$1";s:6:"engine";a:2:{s:7:"default";s:6:"WebKit";s:8:"versions";a:1:{i:28;s:5:"Blink";}}}i:309;a:3:{s:5:"regex";s:30:"AtomicBrowser(?:/(\\d+[.\\d]+))?";s:4:"name";s:18:"Atomic Web Browser";s:7:"version";s:2:"$1";}i:310;a:3:{s:5:"regex";s:26:"Bunjalloo(?:/(\\d+[.\\d]+))?";s:4:"name";s:9:"Bunjalloo";s:7:"version";s:2:"$1";}i:311;a:4:{s:5:"regex";s:28:"Chrome/(\\d+\\.[.\\d]+).*Brave/";s:4:"name";s:5:"Brave";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:312;a:4:{s:5:"regex";s:34:"Brave(?: Chrome)?(?:/(\\d+[.\\d]+))?";s:4:"name";s:5:"Brave";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:313;a:4:{s:5:"regex";s:24:"Iridium(?:/(\\d+[.\\d]+))?";s:4:"name";s:7:"Iridium";s:7:"version";s:2:"$1";s:6:"engine";a:2:{s:7:"default";s:6:"WebKit";s:8:"versions";a:1:{i:28;s:5:"Blink";}}}i:314;a:4:{s:5:"regex";s:26:"Iceweasel(?:/(\\d+[.\\d]+))?";s:4:"name";s:9:"Iceweasel";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Gecko";}}i:315;a:4:{s:5:"regex";s:11:"WebPositive";s:4:"name";s:11:"WebPositive";s:7:"version";s:0:"";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:316;a:4:{s:5:"regex";s:35:".*Goanna.*PaleMoon(?:/(\\d+[.\\d]+))?";s:4:"name";s:9:"Pale Moon";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"Goanna";}}i:317;a:4:{s:5:"regex";s:25:"PaleMoon(?:/(\\d+[.\\d]+))?";s:4:"name";s:9:"Pale Moon";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Gecko";}}i:318;a:4:{s:5:"regex";s:26:"CometBird(?:/(\\d+[.\\d]+))?";s:4:"name";s:9:"CometBird";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Gecko";}}i:319;a:4:{s:5:"regex";s:26:"IceDragon(?:/(\\d+[.\\d]+))?";s:4:"name";s:9:"IceDragon";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Gecko";}}i:320;a:4:{s:5:"regex";s:22:"Flock(?:/(\\d+[.\\d]+))?";s:4:"name";s:5:"Flock";s:7:"version";s:2:"$1";s:6:"engine";a:2:{s:7:"default";s:5:"Gecko";s:8:"versions";a:1:{i:3;s:6:"WebKit";}}}i:321;a:3:{s:5:"regex";s:31:"JigBrowserPlus/(?:(\\d+[.\\d]+))?";s:4:"name";s:16:"Jig Browser Plus";s:7:"version";s:2:"$1";}i:322;a:3:{s:5:"regex";s:45:"jig browser(?: web;|9i?)?(?:[/ ](\\d+[.\\d]+))?";s:4:"name";s:11:"Jig Browser";s:7:"version";s:2:"$1";}i:323;a:4:{s:5:"regex";s:23:"Kapiko(?:/(\\d+[.\\d]+))?";s:4:"name";s:6:"Kapiko";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Gecko";}}i:324;a:4:{s:5:"regex";s:21:"Kylo(?:/(\\d+[.\\d]+))?";s:4:"name";s:4:"Kylo";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Gecko";}}i:325;a:3:{s:5:"regex";s:23:"Origin/(?:(\\d+[.\\d]+))?";s:4:"name";s:22:"Origin In-Game Overlay";s:7:"version";s:2:"$1";}i:326;a:3:{s:5:"regex";s:26:"Cunaguaro(?:/(\\d+[.\\d]+))?";s:4:"name";s:9:"Cunaguaro";s:7:"version";s:2:"$1";}i:327;a:3:{s:5:"regex";s:44:"(?:TO-Browser/TOB|DT-Browser/DTB)(\\d+[.\\d]+)";s:4:"name";s:19:"t-online.de Browser";s:7:"version";s:2:"$1";}i:328;a:4:{s:5:"regex";s:27:"Kazehakase(?:/(\\d+[.\\d]+))?";s:4:"name";s:10:"Kazehakase";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:0:"";}}i:329;a:4:{s:5:"regex";s:26:"ArcticFox(?:/(\\d+[.\\d]+))?";s:4:"name";s:10:"Arctic Fox";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"Goanna";}}i:330;a:4:{s:5:"regex";s:22:"Mypal(?:/(\\d+[.\\d]+))?";s:4:"name";s:5:"Mypal";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"Goanna";}}i:331;a:4:{s:5:"regex";s:25:"Centaury(?:/(\\d+[.\\d]+))?";s:4:"name";s:8:"Centaury";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"Goanna";}}i:332;a:3:{s:5:"regex";s:30:"(?:Focus|Klar)(?:/(\\d+[.\\d]+))";s:4:"name";s:13:"Firefox Focus";s:7:"version";s:2:"$1";}i:333;a:4:{s:5:"regex";s:25:"Cyberfox(?:/(\\d+[.\\d]+))?";s:4:"name";s:8:"Cyberfox";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Gecko";}}i:334;a:4:{s:5:"regex";s:35:"Firefox/(\\d+\\.[.\\d]+).*\\(Swiftfox\\)";s:4:"name";s:8:"Swiftfox";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Gecko";}}i:335;a:3:{s:5:"regex";s:22:"UCBrowserHD/(\\d[\\d.]+)";s:4:"name";s:13:"UC Browser HD";s:7:"version";s:2:"$1";}i:336;a:3:{s:5:"regex";s:27:"UCMini(?:[ /]?(\\d+[.\\d]+))?";s:4:"name";s:15:"UC Browser Mini";s:7:"version";s:2:"$1";}i:337;a:3:{s:5:"regex";s:26:"UC[ ]?Browser.* \\(UCMini\\)";s:4:"name";s:15:"UC Browser Mini";s:7:"version";s:0:"";}i:338;a:4:{s:5:"regex";s:35:"Chrome.+uc mini browser(\\d+[.\\d]+)?";s:4:"name";s:15:"UC Browser Mini";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:339;a:3:{s:5:"regex";s:28:"UCTurbo(?:[ /]?(\\d+[.\\d]+))?";s:4:"name";s:16:"UC Browser Turbo";s:7:"version";s:2:"$1";}i:340;a:3:{s:5:"regex";s:27:"UC[ ]?Browser.* \\(UCTurbo\\)";s:4:"name";s:16:"UC Browser Turbo";s:7:"version";s:0:"";}i:341;a:3:{s:5:"regex";s:34:"UC[ ]?Browser(?:[ /]?(\\d+[.\\d]+))?";s:4:"name";s:10:"UC Browser";s:7:"version";s:2:"$1";}i:342;a:3:{s:5:"regex";s:26:"UCWEB(?:[ /]?(\\d+[.\\d]+))?";s:4:"name";s:10:"UC Browser";s:7:"version";s:2:"$1";}i:343;a:3:{s:5:"regex";s:14:"UC AppleWebKit";s:4:"name";s:10:"UC Browser";s:7:"version";s:0:"";}i:344;a:4:{s:5:"regex";s:59:"UC%20Browser/(\\d+[.\\d]+)? CFNetwork/.+Darwin/.+(?!.*x86_64)";s:4:"name";s:10:"UC Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:345;a:4:{s:5:"regex";s:29:"Chrome.+UC Browser(\\d+[.\\d]+)";s:4:"name";s:10:"UC Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:346;a:4:{s:5:"regex";s:26:"Firefox.+UCKai/(\\d+[.\\d]+)";s:4:"name";s:10:"UC Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Gecko";}}i:347;a:4:{s:5:"regex";s:50:"(?:Mobile|Tablet).*Servo.*Firefox(?:/(\\d+[.\\d]+))?";s:4:"name";s:14:"Firefox Mobile";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Servo";}}i:348;a:4:{s:5:"regex";s:43:"(?:Mobile|Tablet).*Firefox(?:/(\\d+[.\\d]+))?";s:4:"name";s:14:"Firefox Mobile";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Gecko";}}i:349;a:4:{s:5:"regex";s:17:"FxiOS/(\\d+[.\\d]+)";s:4:"name";s:18:"Firefox Mobile iOS";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:350;a:4:{s:5:"regex";s:33:".*Servo.*Firefox(?:/(\\d+[.\\d]+))?";s:4:"name";s:7:"Firefox";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Servo";}}i:351;a:4:{s:5:"regex";s:42:"(?!.*Opera[ /])Firefox(?:[ /](\\d+[.\\d]+))?";s:4:"name";s:7:"Firefox";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Gecko";}}i:352;a:4:{s:5:"regex";s:76:"(?:BonEcho|GranParadiso|Lorentz|Minefield|Namoroka|Shiretoko)[ /](\\d+[.\\d]+)";s:4:"name";s:7:"Firefox";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Gecko";}}i:353;a:3:{s:5:"regex";s:29:"ANTFresco(?:[/ ](\\d+[.\\d]+))?";s:4:"name";s:10:"ANT Fresco";s:7:"version";s:2:"$1";}i:354;a:3:{s:5:"regex";s:25:"ANTGalio(?:/(\\d+[.\\d]+))?";s:4:"name";s:8:"ANTGalio";s:7:"version";s:2:"$1";}i:355;a:3:{s:5:"regex";s:37:"(?:Espial|Escape)(?:[/ ](\\d+[.\\d]+))?";s:4:"name";s:17:"Espial TV Browser";s:7:"version";s:2:"$1";}i:356;a:4:{s:5:"regex";s:25:"RockMelt(?:/(\\d+[.\\d]+))?";s:4:"name";s:8:"RockMelt";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:357;a:3:{s:5:"regex";s:34:"Fireweb Navigator(?:/(\\d+[.\\d]+))?";s:4:"name";s:17:"Fireweb Navigator";s:7:"version";s:2:"$1";}i:358;a:3:{s:5:"regex";s:24:"Fireweb(?:/(\\d+[.\\d]+))?";s:4:"name";s:7:"Fireweb";s:7:"version";s:2:"$1";}i:359;a:4:{s:5:"regex";s:41:"(?:Navigator|Netscape6?)(?:/(\\d+[.\\d]+))?";s:4:"name";s:8:"Netscape";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:0:"";}}i:360;a:3:{s:5:"regex";s:32:"(?:Polarity)(?:[/ ](\\d+[.\\d]+))?";s:4:"name";s:8:"Polarity";s:7:"version";s:2:"$1";}i:361;a:3:{s:5:"regex";s:32:"(?:QupZilla)(?:[/ ](\\d+[.\\d]+))?";s:4:"name";s:8:"QupZilla";s:7:"version";s:2:"$1";}i:362;a:3:{s:5:"regex";s:30:"(?:Dooble)(?:[/ ](\\d+[.\\d]+))?";s:4:"name";s:6:"Dooble";s:7:"version";s:2:"$1";}i:363;a:4:{s:5:"regex";s:17:"Whale/(\\d+[.\\d]+)";s:4:"name";s:13:"Whale Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:364;a:3:{s:5:"regex";s:57:"Obigo[ ]?(?:InternetBrowser|Browser)?(?:[ /]([a-z0-9]*))?";s:4:"name";s:5:"Obigo";s:7:"version";s:2:"$1";}i:365;a:3:{s:5:"regex";s:12:"Obigo|Teleca";s:4:"name";s:5:"Obigo";s:7:"version";s:0:"";}i:366;a:4:{s:5:"regex";s:37:"Chrome/.+ OP(?:RG)?X(?:/(\\d+[.\\d]+))?";s:4:"name";s:8:"Opera GX";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:367;a:4:{s:5:"regex";s:27:"OP(?:RG)?X(?:/(\\d+[.\\d]+))?";s:4:"name";s:8:"Opera GX";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:368;a:4:{s:5:"regex";s:32:"Opera%20GX/.+CFNetwork/.+Darwin/";s:4:"name";s:8:"Opera GX";s:7:"version";s:0:"";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:369;a:4:{s:5:"regex";s:90:"(?:Opera Tablet.*Version|Opera/.+(?<!SymbOS; )Opera Mobi.+Version|Mobile.+OPR)/(\\d+[.\\d]+)";s:4:"name";s:12:"Opera Mobile";s:7:"version";s:2:"$1";s:6:"engine";a:2:{s:7:"default";s:6:"Presto";s:8:"versions";a:1:{i:15;s:5:"Blink";}}}i:370;a:4:{s:5:"regex";s:15:"MMS/(\\d+[.\\d]+)";s:4:"name";s:10:"Opera Neon";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:371;a:4:{s:5:"regex";s:15:"OMI/(\\d+[.\\d]+)";s:4:"name";s:13:"Opera Devices";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:372;a:4:{s:5:"regex";s:60:"Opera%20Touch/(\\d+[.\\d]+)? CFNetwork/.+Darwin/.+(?!.*x86_64)";s:4:"name";s:11:"Opera Touch";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:373;a:4:{s:5:"regex";s:49:"Opera%20Touch/.+CFNetwork/.+Darwin/.+(?!.*x86_64)";s:4:"name";s:11:"Opera Touch";s:7:"version";s:0:"";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:374;a:3:{s:5:"regex";s:15:"OPT/(\\d+[.\\d]+)";s:4:"name";s:11:"Opera Touch";s:7:"version";s:2:"$1";}i:375;a:4:{s:5:"regex";s:45:"Opera/(\\d+\\.[.\\d]+) .*(?<!SymbOS; )Opera Mobi";s:4:"name";s:12:"Opera Mobile";s:7:"version";s:2:"$1";s:6:"engine";a:2:{s:7:"default";s:6:"Presto";s:8:"versions";a:1:{i:15;s:5:"Blink";}}}i:376;a:4:{s:5:"regex";s:32:"Opera ?Mini/(?:att/)?(\\d+[.\\d]+)";s:4:"name";s:10:"Opera Mini";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"Presto";}}i:377;a:4:{s:5:"regex";s:32:"Opera ?Mini.+Version/(\\d+[.\\d]+)";s:4:"name";s:10:"Opera Mini";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"Presto";}}i:378;a:4:{s:5:"regex";s:17:"OPiOS/(\\d+[.\\d]+)";s:4:"name";s:14:"Opera Mini iOS";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:379;a:4:{s:5:"regex";s:34:"Opera%20Mini/(\\d+[.\\d]+) CFNetwork";s:4:"name";s:14:"Opera Mini iOS";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:380;a:4:{s:5:"regex";s:40:"Opera.+Edition Next.+Version/(\\d+[.\\d]+)";s:4:"name";s:10:"Opera Next";s:7:"version";s:2:"$1";s:6:"engine";a:2:{s:7:"default";s:6:"Presto";s:8:"versions";a:1:{i:15;s:5:"Blink";}}}i:381;a:4:{s:5:"regex";s:65:"(?:Opera|OPR)[/ ](?:9\\.80.*Version/)?(\\d+\\.[.\\d]+) .*Edition Next";s:4:"name";s:10:"Opera Next";s:7:"version";s:2:"$1";s:6:"engine";a:2:{s:7:"default";s:6:"Presto";s:8:"versions";a:1:{i:15;s:5:"Blink";}}}i:382;a:4:{s:5:"regex";s:53:"(?:Opera[/ ]?|OPR[/ ])(?:9\\.80.*Version/)?(\\d+[.\\d]+)";s:4:"name";s:5:"Opera";s:7:"version";s:2:"$1";s:6:"engine";a:2:{s:7:"default";s:0:"";s:8:"versions";a:3:{s:3:"3.5";s:7:"Elektra";i:7;s:6:"Presto";i:15;s:5:"Blink";}}}i:383;a:4:{s:5:"regex";s:17:"Opera/.+CFNetwork";s:4:"name";s:5:"Opera";s:7:"version";s:0:"";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:384;a:4:{s:5:"regex";s:14:"Chrome.+Opera/";s:4:"name";s:5:"Opera";s:7:"version";s:0:"";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:385;a:4:{s:5:"regex";s:23:"rekonq(?:/(\\d+[.\\d]+))?";s:4:"name";s:6:"Rekonq";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:386;a:4:{s:5:"regex";s:25:"CoolNovo(?:/(\\d+[.\\d]+))?";s:4:"name";s:8:"CoolNovo";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:0:"";}}i:387;a:4:{s:5:"regex";s:33:"(?:Comodo[ _])?Dragon/(\\d+[.\\d]+)";s:4:"name";s:13:"Comodo Dragon";s:7:"version";s:2:"$1";s:6:"engine";a:2:{s:7:"default";s:6:"WebKit";s:8:"versions";a:1:{i:28;s:5:"Blink";}}}i:388;a:4:{s:5:"regex";s:27:"ChromePlus(?:/(\\d+[.\\d]+))?";s:4:"name";s:10:"ChromePlus";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:0:"";}}i:389;a:4:{s:5:"regex";s:25:"Conkeror(?:/(\\d+[.\\d]+))?";s:4:"name";s:8:"Conkeror";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Gecko";}}i:390;a:4:{s:5:"regex";s:26:"Konqueror(?:/(\\d+[.\\d]+))?";s:4:"name";s:9:"Konqueror";s:7:"version";s:2:"$1";s:6:"engine";a:2:{s:7:"default";s:5:"KHTML";s:8:"versions";a:1:{i:4;s:0:"";}}}i:391;a:3:{s:5:"regex";s:26:"bdhonorbrowser/(\\d+[.\\d]+)";s:4:"name";s:13:"HONOR Browser";s:7:"version";s:2:"$1";}i:392;a:3:{s:5:"regex";s:79:"(?:baidubrowser|bdbrowser(?:(?:hd)?_i18n)?|FlyFlow|BaiduHD)(?:[/ ](\\d+[.\\d]*))?";s:4:"name";s:13:"Baidu Browser";s:7:"version";s:2:"$1";}i:393;a:3:{s:5:"regex";s:52:"(?:(?:BD)?Spark(?:Safe)?|BIDUBrowser)[/ ](\\d+[.\\d]*)";s:4:"name";s:11:"Baidu Spark";s:7:"version";s:2:"$1";}i:394;a:4:{s:5:"regex";s:35:"YaBrowser(?:/(\\d+[.\\d]*)) YaApp_iOS";s:4:"name";s:14:"Yandex Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:395;a:4:{s:5:"regex";s:40:"iP(?:hone|ad).*YaBrowser(?:/(\\d+[.\\d]*))";s:4:"name";s:14:"Yandex Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:396;a:4:{s:5:"regex";s:35:"YaBrowser(?:/(\\d+[.\\d]*)) \\(lite\\)?";s:4:"name";s:19:"Yandex Browser Lite";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:397;a:4:{s:5:"regex";s:27:"YaBrowser/(\\d+[.\\d]*).*corp";s:4:"name";s:19:"Yandex Browser Corp";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:398;a:4:{s:5:"regex";s:47:"YaBrowser(?:/(\\d+[.\\d]*))(?: \\((alpha|beta)\\))?";s:4:"name";s:14:"Yandex Browser";s:7:"version";s:5:"$1 $2";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:399;a:4:{s:5:"regex";s:40:"Ya(?:ndex)?SearchBrowser(?:/(\\d+[.\\d]*))";s:4:"name";s:14:"Yandex Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:400;a:4:{s:5:"regex";s:24:"Viv(?:aldi)?/(\\d+[.\\d]+)";s:4:"name";s:7:"Vivaldi";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:401;a:4:{s:5:"regex";s:27:"TweakStyle(?:/(\\d+[.\\d]+))?";s:4:"name";s:10:"TweakStyle";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:402;a:4:{s:5:"regex";s:34:"Chrome.+Midori Browser/(\\d+[.\\d]+)";s:4:"name";s:6:"Midori";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:403;a:4:{s:5:"regex";s:26:"Midori(?:[ /](\\d+[.\\d]+))?";s:4:"name";s:6:"Midori";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:404;a:3:{s:5:"regex";s:24:"Mercury/(?:(\\d+[.\\d]+))?";s:4:"name";s:7:"Mercury";s:7:"version";s:2:"$1";}i:405;a:4:{s:5:"regex";s:21:"Chrome.+Maxthon/\\d{4}";s:4:"name";s:7:"Maxthon";s:7:"version";s:0:"";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:406;a:4:{s:5:"regex";s:62:"Chrome.+(?:MxBrowser|Maxthon)(?:.+\\(portable\\))?/(\\d+\\.[.\\d]+)";s:4:"name";s:7:"Maxthon";s:7:"version";s:2:"$1";s:6:"engine";a:2:{s:7:"default";s:6:"WebKit";s:8:"versions";a:1:{s:3:"4.2";s:5:"Blink";}}}i:407;a:4:{s:5:"regex";s:79:"(?:Maxthon(?:%20Browser)?|MxBrowser(?:-inhouse|-iPhone)?|MXiOS)[ /](\\d+[.\\d]+)?";s:4:"name";s:7:"Maxthon";s:7:"version";s:2:"$1";s:6:"engine";a:2:{s:7:"default";s:0:"";s:8:"versions";a:1:{i:3;s:6:"WebKit";}}}i:408;a:4:{s:5:"regex";s:17:"(?:Maxthon|MyIE2)";s:4:"name";s:7:"Maxthon";s:7:"version";s:0:"";s:6:"engine";a:1:{s:7:"default";s:0:"";}}i:409;a:3:{s:5:"regex";s:20:"Puffin/(\\d+[.\\d]+)FP";s:4:"name";s:20:"Puffin Cloud Browser";s:7:"version";s:2:"$1";}i:410;a:3:{s:5:"regex";s:28:"Puffin/(\\d+[.\\d]+)(?:[LMW]D)";s:4:"name";s:21:"Puffin Secure Browser";s:7:"version";s:2:"$1";}i:411;a:3:{s:5:"regex";s:35:"Puffin/(\\d+[.\\d]+)(?:[AILW][PT]|M)?";s:4:"name";s:18:"Puffin Web Browser";s:7:"version";s:2:"$1";}i:412;a:4:{s:5:"regex";s:27:"MobileIron(?:/(\\d+[.\\d]+))?";s:4:"name";s:11:"Iron Mobile";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:413;a:4:{s:5:"regex";s:32:"Chrome(?:/(\\d+\\.[.\\d]+) )?.*Iron";s:4:"name";s:4:"Iron";s:7:"version";s:2:"$1";s:6:"engine";a:2:{s:7:"default";s:6:"WebKit";s:8:"versions";a:1:{i:28;s:5:"Blink";}}}i:414;a:4:{s:5:"regex";s:16:"Iron/(\\d+[.\\d]+)";s:4:"name";s:4:"Iron";s:7:"version";s:2:"$1";s:6:"engine";a:2:{s:7:"default";s:6:"WebKit";s:8:"versions";a:1:{i:28;s:5:"Blink";}}}i:415;a:4:{s:5:"regex";s:25:"Epiphany(?:/(\\d+[.\\d]+))?";s:4:"name";s:9:"GNOME Web";s:7:"version";s:2:"$1";s:6:"engine";a:2:{s:7:"default";s:5:"Gecko";s:8:"versions";a:2:{s:6:"2.9.16";s:0:"";s:4:"2.28";s:6:"WebKit";}}}i:416;a:3:{s:5:"regex";s:30:"LieBaoFast(?:[ /](\\d+[.\\d]+))?";s:4:"name";s:10:"LieBaoFast";s:7:"version";s:2:"$1";}i:417;a:3:{s:5:"regex";s:29:"LBBrowser(?:[ /](\\d+[.\\d]+))?";s:4:"name";s:15:"Cheetah Browser";s:7:"version";s:2:"$1";}i:418;a:3:{s:5:"regex";s:14:"SE (\\d+[.\\d]+)";s:4:"name";s:14:"Sogou Explorer";s:7:"version";s:2:"$1";}i:419;a:3:{s:5:"regex";s:22:"QQBrowserLite/([\\d.]+)";s:4:"name";s:15:"QQ Browser Lite";s:7:"version";s:2:"$1";}i:420;a:4:{s:5:"regex";s:25:"M?QQBrowser/Mini([.\\d]+)?";s:4:"name";s:15:"QQ Browser Mini";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:0:"";}}i:421;a:4:{s:5:"regex";s:39:"M?QQ(?:Browser|浏览器)(?:/([.\\d]+))?";s:4:"name";s:10:"QQ Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:0:"";}}i:422;a:4:{s:5:"regex";s:44:"(?:MIUIBrowser|MiuiBrowser)(?:/(\\d+[.\\d]+))?";s:4:"name";s:10:"Mi Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:0:"";}}i:423;a:4:{s:5:"regex";s:57:"(?:coc_coc_browser|coccocbrowser|CocCoc)(?:/(\\d+[.\\d]+))?";s:4:"name";s:7:"Coc Coc";s:7:"version";s:2:"$1";s:6:"engine";a:2:{s:7:"default";s:6:"WebKit";s:8:"versions";a:1:{i:28;s:5:"Blink";}}}i:424;a:3:{s:5:"regex";s:30:"(?:DuckDuckGo|Ddg)/(\\d+[.\\d]*)";s:4:"name";s:26:"DuckDuckGo Privacy Browser";s:7:"version";s:2:"$1";}i:425;a:4:{s:5:"regex";s:40:"(?:DDG-Android-|ddg_android/)(\\d+[.\\d]*)";s:4:"name";s:26:"DuckDuckGo Privacy Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:426;a:3:{s:5:"regex";s:36:"Samsung ?Browser(?:[/ ](\\d+[.\\d]+))?";s:4:"name";s:15:"Samsung Browser";s:7:"version";s:2:"$1";}i:427;a:3:{s:5:"regex";s:30:"(?:SFB(?:rowser)?)/(\\d+[.\\d]+)";s:4:"name";s:18:"Super Fast Browser";s:7:"version";s:2:"$1";}i:428;a:3:{s:5:"regex";s:38:"com\\.browser\\.tssomas(?:/(\\d+[.\\d]+))?";s:4:"name";s:18:"Super Fast Browser";s:7:"version";s:2:"$1";}i:429;a:3:{s:5:"regex";s:28:"EUI Browser(?:/(\\d+[.\\d]+))?";s:4:"name";s:11:"EUI Browser";s:7:"version";s:2:"$1";}i:430;a:4:{s:5:"regex";s:25:"UBrowser(?:/(\\d+[.\\d]+))?";s:4:"name";s:8:"UBrowser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:431;a:4:{s:5:"regex";s:24:"Streamy(?:/(\\d+[.\\d]+))?";s:4:"name";s:7:"Streamy";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:0:"";}}i:432;a:4:{s:5:"regex";s:7:"isivioo";s:4:"name";s:7:"Isivioo";s:7:"version";s:0:"";s:6:"engine";a:1:{s:7:"default";s:0:"";}}i:433;a:4:{s:5:"regex";s:26:"Chrome/.+Tenta/(\\d+[.\\d]+)";s:4:"name";s:13:"Tenta Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:434;a:4:{s:5:"regex";s:17:"Tenta/(\\d+[.\\d]+)";s:4:"name";s:13:"Tenta Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:435;a:4:{s:5:"regex";s:18:"Rocket/(\\d+[.\\d]+)";s:4:"name";s:14:"Firefox Rocket";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:436;a:4:{s:5:"regex";s:35:"Web Explorer/(\\d+\\.[.\\d]+) .*Chrome";s:4:"name";s:12:"Web Explorer";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:437;a:4:{s:5:"regex";s:17:"webexplorer/(\\d+)";s:4:"name";s:12:"Web Explorer";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:0:"";}}i:438;a:4:{s:5:"regex";s:32:"Chrome.+SznProhlizec/(\\d+[.\\d]+)";s:4:"name";s:14:"Seznam Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:439;a:4:{s:5:"regex";s:24:"SznProhlizec/(\\d+[.\\d]+)";s:4:"name";s:14:"Seznam Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:440;a:4:{s:5:"regex";s:30:"SogouMobileBrowser/(\\d+[.\\d]+)";s:4:"name";s:20:"Sogou Mobile Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:0:"";}}i:441;a:4:{s:5:"regex";s:24:"Mint Browser/(\\d+[.\\d]+)";s:4:"name";s:12:"Mint Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:0:"";}}i:442;a:4:{s:5:"regex";s:34:"Ecosia (?:android|ios)@(\\d+[.\\d]+)";s:4:"name";s:6:"Ecosia";s:7:"version";s:2:"$1";s:6:"engine";a:2:{s:7:"default";s:0:"";s:8:"versions";a:1:{i:28;s:5:"Blink";}}}i:443;a:4:{s:5:"regex";s:9:"ACHEETAHI";s:4:"name";s:10:"CM Browser";s:7:"version";s:0:"";s:6:"engine";a:1:{s:7:"default";s:0:"";}}i:444;a:4:{s:5:"regex";s:60:"Chrome/.+ (?:LeBrowser|MobileLenovoBrowser)(?:/(\\d+[.\\d]+))?";s:4:"name";s:14:"Lenovo Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:445;a:4:{s:5:"regex";s:26:"LeBrowser(?:/(\\d+[.\\d]+))?";s:4:"name";s:14:"Lenovo Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:446;a:4:{s:5:"regex";s:11:"Kiwi Chrome";s:4:"name";s:4:"Kiwi";s:7:"version";s:0:"";s:6:"engine";a:1:{s:7:"default";s:0:"";}}i:447;a:4:{s:5:"regex";s:25:"Mb2345Browser/(\\d+[.\\d]+)";s:4:"name";s:12:"2345 Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:0:"";}}i:448;a:4:{s:5:"regex";s:28:"Silk/(\\d+[.\\d]+) like Chrome";s:4:"name";s:11:"Mobile Silk";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:449;a:4:{s:5:"regex";s:21:"Silk(?:/(\\d+[.\\d]+))?";s:4:"name";s:11:"Mobile Silk";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:450;a:3:{s:5:"regex";s:23:"iBrowser/Mini(\\d+\\.\\d+)";s:4:"name";s:13:"iBrowser Mini";s:7:"version";s:2:"$1";}i:451;a:3:{s:5:"regex";s:23:"iBrowser/(\\d+\\.[.\\d]+)?";s:4:"name";s:8:"iBrowser";s:7:"version";s:2:"$1";}i:452;a:3:{s:5:"regex";s:27:"IBrowse(?:[ /](\\d+[.\\d]+))?";s:4:"name";s:7:"IBrowse";s:7:"version";s:2:"$1";}i:453;a:3:{s:5:"regex";s:28:"UP\\.Browser(?:/(\\d+[.\\d]+))?";s:4:"name";s:23:"Openwave Mobile Browser";s:7:"version";s:2:"$1";}i:454;a:3:{s:5:"regex";s:25:"Openwave(?:/(\\d+[.\\d]+))?";s:4:"name";s:23:"Openwave Mobile Browser";s:7:"version";s:2:"$1";}i:455;a:4:{s:5:"regex";s:30:"OneBrowser(?:[ /](\\d+[.\\d]+))?";s:4:"name";s:11:"ONE Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:456;a:3:{s:5:"regex";s:26:"GoBrowser(?:/(\\d+[.\\d]+))?";s:4:"name";s:9:"GoBrowser";s:7:"version";s:2:"$1";}i:457;a:3:{s:5:"regex";s:81:"(?:NokiaBrowser|BrowserNG|WicKed|Nokia-Communicator-WWW-Browser)(?:/(\\d+[.\\d]+))?";s:4:"name";s:13:"Nokia Browser";s:7:"version";s:2:"$1";}i:458;a:3:{s:5:"regex";s:13:"Series60/5\\.0";s:4:"name";s:13:"Nokia Browser";s:7:"version";s:3:"7.0";}i:459;a:3:{s:5:"regex";s:20:"Series60/(\\d+[.\\d]+)";s:4:"name";s:17:"Nokia OSS Browser";s:7:"version";s:2:"$1";}i:460;a:3:{s:5:"regex";s:25:"S40OviBrowser/(\\d+[.\\d]+)";s:4:"name";s:17:"Nokia Ovi Browser";s:7:"version";s:2:"$1";}i:461;a:3:{s:5:"regex";s:20:"^Nokia|Nokia[EN]?\\d+";s:4:"name";s:13:"Nokia Browser";s:7:"version";s:0:"";}i:462;a:4:{s:5:"regex";s:43:"Sleipnir(?:(?:%20Browser)?[ /](\\d+[.\\d]+))?";s:4:"name";s:8:"Sleipnir";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:0:"";}}i:463;a:3:{s:5:"regex";s:29:"NTENTBrowser(?:/(\\d+[.\\d]+))?";s:4:"name";s:13:"NTENT Browser";s:7:"version";s:2:"$1";}i:464;a:3:{s:5:"regex";s:18:"TV Bro/(\\d+[.\\d]+)";s:4:"name";s:6:"TV Bro";s:7:"version";s:2:"$1";}i:465;a:3:{s:5:"regex";s:22:"Quark(?:/(\\d+[.\\d]+))?";s:4:"name";s:5:"Quark";s:7:"version";s:2:"$1";}i:466;a:4:{s:5:"regex";s:32:"MonumentBrowser(?:/(\\d+[.\\d]+))?";s:4:"name";s:16:"Monument Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:467;a:4:{s:5:"regex";s:28:"BlueBrowser(?:/(\\d+[.\\d]+))?";s:4:"name";s:12:"Blue Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:468;a:4:{s:5:"regex";s:30:"JAPAN Browser(?:/(\\d+[.\\d]+))?";s:4:"name";s:13:"Japan Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:469;a:4:{s:5:"regex";s:23:"OpenFin/(?:(\\d+[.\\d]+))";s:4:"name";s:7:"OpenFin";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:470;a:4:{s:5:"regex";s:26:"SuperBird(?:/(\\d+[.\\d]+))?";s:4:"name";s:9:"SuperBird";s:7:"version";s:2:"$1";s:6:"engine";a:2:{s:7:"default";s:6:"WebKit";s:8:"versions";a:1:{i:28;s:5:"Blink";}}}i:471;a:4:{s:5:"regex";s:23:"Soul(?:Browser)?$|Soul/";s:4:"name";s:12:"Soul Browser";s:7:"version";s:0:"";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:472;a:3:{s:5:"regex";s:26:"LG Browser(?:/(\\d+[.\\d]+))";s:4:"name";s:10:"LG Browser";s:7:"version";s:2:"$1";}i:473;a:4:{s:5:"regex";s:23:"QtWebEngine/(\\d+[.\\d]+)";s:4:"name";s:11:"QtWebEngine";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:0:"";}}i:474;a:4:{s:5:"regex";s:44:"(?: wv\\)|Version/).* Chrome(?:/(\\d+[.\\d]+))?";s:4:"name";s:14:"Chrome Webview";s:7:"version";s:2:"$1";s:6:"engine";a:2:{s:7:"default";s:6:"WebKit";s:8:"versions";a:1:{i:28;s:5:"Blink";}}}i:475;a:4:{s:5:"regex";s:21:"CrMo(?:/(\\d+[.\\d]+))?";s:4:"name";s:13:"Chrome Mobile";s:7:"version";s:2:"$1";s:6:"engine";a:2:{s:7:"default";s:6:"WebKit";s:8:"versions";a:1:{i:28;s:5:"Blink";}}}i:476;a:4:{s:5:"regex";s:22:"CriOS(?:/(\\d+[.\\d]+))?";s:4:"name";s:17:"Chrome Mobile iOS";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:477;a:4:{s:5:"regex";s:30:"Chrome(?:/(\\d+[.\\d]+))? Mobile";s:4:"name";s:13:"Chrome Mobile";s:7:"version";s:2:"$1";s:6:"engine";a:2:{s:7:"default";s:6:"WebKit";s:8:"versions";a:1:{i:28;s:5:"Blink";}}}i:478;a:4:{s:5:"regex";s:28:"chromeframe(?:/(\\d+[.\\d]+))?";s:4:"name";s:12:"Chrome Frame";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:479;a:4:{s:5:"regex";s:25:"Chromium(?:/(\\d+[.\\d]+))?";s:4:"name";s:8:"Chromium";s:7:"version";s:2:"$1";s:6:"engine";a:2:{s:7:"default";s:6:"WebKit";s:8:"versions";a:1:{i:28;s:5:"Blink";}}}i:480;a:4:{s:5:"regex";s:27:".*Chromium(?:_(\\d+[.\\d]+))?";s:4:"name";s:8:"Chromium";s:7:"version";s:2:"$1";s:6:"engine";a:2:{s:7:"default";s:6:"WebKit";s:8:"versions";a:1:{i:28;s:5:"Blink";}}}i:481;a:4:{s:5:"regex";s:31:"HeadlessChrome(?:/(\\d+[.\\d]+))?";s:4:"name";s:15:"Headless Chrome";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Blink";}}i:482;a:4:{s:5:"regex";s:31:"Chrome(?!book)(?:/(\\d+[.\\d]+))?";s:4:"name";s:6:"Chrome";s:7:"version";s:2:"$1";s:6:"engine";a:2:{s:7:"default";s:6:"WebKit";s:8:"versions";a:1:{i:28;s:5:"Blink";}}}i:483;a:4:{s:5:"regex";s:11:"PocketBook/";s:4:"name";s:18:"PocketBook Browser";s:7:"version";s:0:"";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:484;a:3:{s:5:"regex";s:39:"(?:Tizen|SLP) ?Browser(?:/(\\d+[.\\d]+))?";s:4:"name";s:13:"Tizen Browser";s:7:"version";s:2:"$1";}i:485;a:4:{s:5:"regex";s:69:"Tizen (?:\\d+\\.[.\\d]+)[^\\.\\d].* Version/(\\d+[.\\d]+) (?:TV|Mobile|like)";s:4:"name";s:13:"Tizen Browser";s:7:"version";s:2:"$1";s:6:"engine";a:2:{s:7:"default";s:6:"WebKit";s:8:"versions";a:1:{i:4;s:5:"Blink";}}}i:486;a:3:{s:5:"regex";s:23:"Blazer(?:/(\\d+[.\\d]+))?";s:4:"name";s:11:"Palm Blazer";s:7:"version";s:2:"$1";}i:487;a:3:{s:5:"regex";s:15:"Pre/(\\d+[.\\d]+)";s:4:"name";s:8:"Palm Pre";s:7:"version";s:2:"$1";}i:488;a:3:{s:5:"regex";s:25:"(?:hpw|web)OS/(\\d+[.\\d]+)";s:4:"name";s:10:"wOSBrowser";s:7:"version";s:2:"$1";}i:489;a:3:{s:5:"regex";s:26:"WebPro(?:[ /](\\d+[.\\d]+))?";s:4:"name";s:11:"Palm WebPro";s:7:"version";s:2:"$1";}i:490;a:3:{s:5:"regex";s:29:"Palmscape(?:[ /](\\d+[.\\d]+))?";s:4:"name";s:9:"Palmscape";s:7:"version";s:2:"$1";}i:491;a:3:{s:5:"regex";s:27:"Jasmine(?:[ /](\\d+[.\\d]+))?";s:4:"name";s:7:"Jasmine";s:7:"version";s:2:"$1";}i:492;a:4:{s:5:"regex";s:21:"Lynx(?:/(\\d+[.\\d]+))?";s:4:"name";s:4:"Lynx";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:10:"Text-based";}}i:493;a:3:{s:5:"regex";s:28:"NCSA_Mosaic(?:/(\\d+[.\\d]+))?";s:4:"name";s:11:"NCSA Mosaic";s:7:"version";s:2:"$1";}i:494;a:3:{s:5:"regex";s:27:"VMS_Mosaic(?:/(\\d+[.\\d]+))?";s:4:"name";s:10:"VMS Mosaic";s:7:"version";s:2:"$1";}i:495;a:3:{s:5:"regex";s:24:"ABrowse(?: (\\d+[.\\d]+))?";s:4:"name";s:7:"ABrowse";s:7:"version";s:2:"$1";}i:496;a:3:{s:5:"regex";s:22:"amaya(?:/(\\d+[.\\d]+))?";s:4:"name";s:5:"Amaya";s:7:"version";s:2:"$1";}i:497;a:3:{s:5:"regex";s:29:"AmigaVoyager(?:/(\\d+[.\\d]+))?";s:4:"name";s:13:"Amiga Voyager";s:7:"version";s:2:"$1";}i:498;a:3:{s:5:"regex";s:27:"Amiga-Aweb(?:/(\\d+[.\\d]+))?";s:4:"name";s:10:"Amiga Aweb";s:7:"version";s:2:"$1";}i:499;a:4:{s:5:"regex";s:22:"Arora(?:/(\\d+[.\\d]+))?";s:4:"name";s:5:"Arora";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:500;a:4:{s:5:"regex";s:23:"Beonex(?:/(\\d+[.\\d]+))?";s:4:"name";s:6:"Beonex";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Gecko";}}i:501;a:4:{s:5:"regex";s:22:"bline(?:/(\\d+[.\\d]+))?";s:4:"name";s:6:"B-Line";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:502;a:3:{s:5:"regex";s:21:"BrowseX \\((\\d+[.\\d]+)";s:4:"name";s:7:"BrowseX";s:7:"version";s:2:"$1";}i:503;a:3:{s:5:"regex";s:26:"Charon(?:[/ ](\\d+[.\\d]+))?";s:4:"name";s:6:"Charon";s:7:"version";s:2:"$1";}i:504;a:3:{s:5:"regex";s:25:"Cheshire(?:/(\\d+[.\\d]+))?";s:4:"name";s:8:"Cheshire";s:7:"version";s:2:"$1";}i:505;a:4:{s:5:"regex";s:8:"dbrowser";s:4:"name";s:8:"dbrowser";s:7:"version";s:0:"";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:506;a:4:{s:5:"regex";s:22:"Dillo(?:/(\\d+[.\\d]+))?";s:4:"name";s:5:"Dillo";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Dillo";}}i:507;a:4:{s:5:"regex";s:31:"Dolfin(?:/(\\d+[.\\d]+))?|dolphin";s:4:"name";s:7:"Dolphin";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:508;a:4:{s:5:"regex";s:26:"Elinks(?:[ /](\\d+[.\\d]+))?";s:4:"name";s:6:"Elinks";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:10:"Text-based";}}i:509;a:3:{s:5:"regex";s:35:"Element Browser(?:[ /](\\d+[.\\d]+))?";s:4:"name";s:15:"Element Browser";s:7:"version";s:2:"$1";}i:510;a:3:{s:5:"regex";s:26:"eZBrowser(?:/(\\d+[.\\d]+))?";s:4:"name";s:10:"eZ Browser";s:7:"version";s:2:"$1";}i:511;a:4:{s:5:"regex";s:35:"Firebird(?! Build)(?:/(\\d+[.\\d]+))?";s:4:"name";s:8:"Firebird";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Gecko";}}i:512;a:4:{s:5:"regex";s:22:"Fluid(?:/(\\d+[.\\d]+))?";s:4:"name";s:5:"Fluid";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:513;a:4:{s:5:"regex";s:23:"Galeon(?:/(\\d+[.\\d]+))?";s:4:"name";s:6:"Galeon";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Gecko";}}i:514;a:4:{s:5:"regex";s:58:"(?:Google Earth Pro|Google%20Earth%20Pro)(?:/(\\d+[.\\d]+))?";s:4:"name";s:16:"Google Earth Pro";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:515;a:4:{s:5:"regex";s:53:"GoogleEarth/(\\d+\\.[.\\d]+)[^\\.\\d].*client:(?:Plus|Pro)";s:4:"name";s:16:"Google Earth Pro";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:516;a:4:{s:5:"regex";s:32:"Google ?Earth(?:/v?(\\d+[.\\d]+))?";s:4:"name";s:12:"Google Earth";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:517;a:3:{s:5:"regex";s:24:"HotJava(?:/(\\d+[.\\d]+))?";s:4:"name";s:7:"HotJava";s:7:"version";s:2:"$1";}i:518;a:4:{s:5:"regex";s:30:"iCabMobile(?:[ /](\\d+[.\\d]+))?";s:4:"name";s:11:"iCab Mobile";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:519;a:4:{s:5:"regex";s:24:"iCab(?:[ /](\\d+[.\\d]+))?";s:4:"name";s:4:"iCab";s:7:"version";s:2:"$1";s:6:"engine";a:2:{s:7:"default";s:4:"iCab";s:8:"versions";a:1:{i:4;s:6:"WebKit";}}}i:520;a:4:{s:5:"regex";s:25:"Crazy Browser (\\d+[.\\d]+)";s:4:"name";s:13:"Crazy Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:7:"Trident";}}i:521;a:4:{s:5:"regex";s:23:"IEMobile[ /](\\d+[.\\d]+)";s:4:"name";s:9:"IE Mobile";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:7:"Trident";}}i:522;a:4:{s:5:"regex";s:33:"MSIE (\\d+\\.[.\\d]+)[^\\.\\d].*XBLWP7";s:4:"name";s:9:"IE Mobile";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:7:"Trident";}}i:523;a:4:{s:5:"regex";s:18:"MSIE.*Trident/4\\.0";s:4:"name";s:17:"Internet Explorer";s:7:"version";s:3:"8.0";s:6:"engine";a:1:{s:7:"default";s:7:"Trident";}}i:524;a:4:{s:5:"regex";s:18:"MSIE.*Trident/5\\.0";s:4:"name";s:17:"Internet Explorer";s:7:"version";s:3:"9.0";s:6:"engine";a:1:{s:7:"default";s:7:"Trident";}}i:525;a:4:{s:5:"regex";s:18:"MSIE.*Trident/6\\.0";s:4:"name";s:17:"Internet Explorer";s:7:"version";s:4:"10.0";s:6:"engine";a:1:{s:7:"default";s:7:"Trident";}}i:526;a:4:{s:5:"regex";s:15:"Trident/[78]\\.0";s:4:"name";s:17:"Internet Explorer";s:7:"version";s:4:"11.0";s:6:"engine";a:1:{s:7:"default";s:7:"Trident";}}i:527;a:4:{s:5:"regex";s:16:"MSIE (\\d+[.\\d]+)";s:4:"name";s:17:"Internet Explorer";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:7:"Trident";}}i:528;a:4:{s:5:"regex";s:17:"IE[ /](\\d+[.\\d]+)";s:4:"name";s:17:"Internet Explorer";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:7:"Trident";}}i:529;a:3:{s:5:"regex";s:18:"Kindle/(\\d+[.\\d]+)";s:4:"name";s:14:"Kindle Browser";s:7:"version";s:2:"$1";}i:530;a:4:{s:5:"regex";s:25:"K-meleon(?:/(\\d+[.\\d]+))?";s:4:"name";s:8:"K-meleon";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Gecko";}}i:531;a:4:{s:5:"regex";s:24:"Links(?: \\((\\d+[.\\d]+))?";s:4:"name";s:5:"Links";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:10:"Text-based";}}i:532;a:3:{s:5:"regex";s:23:"LuaKit(?:/(\\d+[.\\d]+))?";s:4:"name";s:6:"LuaKit";s:7:"version";s:2:"$1";}i:533;a:4:{s:5:"regex";s:28:"OmniWeb(?:/[v]?(\\d+[.\\d]+))?";s:4:"name";s:7:"OmniWeb";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:534;a:3:{s:5:"regex";s:30:"(?<!/)Phoenix(?:/(\\d+[.\\d]+))?";s:4:"name";s:7:"Phoenix";s:7:"version";s:2:"$1";}i:535;a:4:{s:5:"regex";s:36:"NetFrontLifeBrowser(?:/(\\d+[.\\d]+))?";s:4:"name";s:13:"NetFront Life";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:8:"NetFront";}}i:536;a:4:{s:5:"regex";s:43:"Browser/(?:NetFont-|NF|NetFront)(\\d+[.\\d]+)";s:4:"name";s:8:"NetFront";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:8:"NetFront";}}i:537;a:4:{s:5:"regex";s:25:"NetFront(?:/(\\d+[.\\d]+))?";s:4:"name";s:8:"NetFront";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:8:"NetFront";}}i:538;a:3:{s:5:"regex";s:56:"PLAYSTATION|NINTENDO 3|AppleWebKit.+ N[XF]/\\d+\\.\\d+\\.\\d+";s:4:"name";s:8:"NetFront";s:7:"version";s:0:"";}i:539;a:3:{s:5:"regex";s:28:"NetPositive(?:/(\\d+[.\\d]+))?";s:4:"name";s:11:"NetPositive";s:7:"version";s:2:"$1";}i:540;a:3:{s:5:"regex";s:41:"Odyssey Web Browser(?:.*OWB/(\\d+[.\\d]+))?";s:4:"name";s:19:"Odyssey Web Browser";s:7:"version";s:2:"$1";}i:541;a:3:{s:5:"regex";s:8:"OffByOne";s:4:"name";s:10:"Off By One";s:7:"version";s:0:"";}i:542;a:3:{s:5:"regex";s:50:"(?:Oregano|OreganMediaBrowser)(?:[ /](\\d+[.\\d]+))?";s:4:"name";s:7:"Oregano";s:7:"version";s:2:"$1";}i:543;a:3:{s:5:"regex";s:39:"(?:Polaris|Embider)(?:[/ ](\\d+[.\\d]+))?";s:4:"name";s:7:"Polaris";s:7:"version";s:2:"$1";}i:544;a:3:{s:5:"regex";s:32:"SEMC-Browser(?:[/ ](\\d+[.\\d]+))?";s:4:"name";s:12:"SEMC-Browser";s:7:"version";s:2:"$1";}i:545;a:4:{s:5:"regex";s:26:"Shiira(?:[/ ](\\d+[.\\d]+))?";s:4:"name";s:6:"Shiira";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:546;a:3:{s:5:"regex";s:27:"Skyfire(?:[/ ](\\d+[.\\d]+))?";s:4:"name";s:7:"Skyfire";s:7:"version";s:2:"$1";}i:547;a:4:{s:5:"regex";s:25:"Snowshoe(?:/(\\d+[.\\d]+))?";s:4:"name";s:8:"Snowshoe";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:548;a:3:{s:5:"regex";s:36:"Sunrise(?:Browser)?(?:/(\\d+[.\\d]+))?";s:4:"name";s:7:"Sunrise";s:7:"version";s:2:"$1";}i:549;a:3:{s:5:"regex";s:13:"WeTab-Browser";s:4:"name";s:13:"WeTab Browser";s:7:"version";s:0:"";}i:550;a:3:{s:5:"regex";s:22:"Xiino(?:/(\\d+[.\\d]+))?";s:4:"name";s:5:"Xiino";s:7:"version";s:2:"$1";}i:551;a:3:{s:5:"regex";s:24:"BlackBerry|PlayBook|BB10";s:4:"name";s:18:"BlackBerry Browser";s:7:"version";s:0:"";}i:552;a:3:{s:5:"regex";s:20:"Browlser/(\\d+[.\\d]+)";s:4:"name";s:8:"Browlser";s:7:"version";s:2:"$1";}i:553;a:4:{s:5:"regex";s:24:"(?<! like )Android(?!\\.)";s:4:"name";s:15:"Android Browser";s:7:"version";s:0:"";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:554;a:3:{s:5:"regex";s:22:"Coast(?:/(\\d+[.\\d]+))?";s:4:"name";s:5:"Coast";s:7:"version";s:2:"$1";}i:555;a:4:{s:5:"regex";s:60:"Opera%20Coast/(\\d+[.\\d]+)? CFNetwork/.+Darwin/.+(?!.*x86_64)";s:4:"name";s:5:"Coast";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:556;a:4:{s:5:"regex";s:21:"Surf(?:/(\\d+[.\\d]+))?";s:4:"name";s:4:"surf";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:557;a:4:{s:5:"regex";s:41:"Safari%20Technology%20Preview/(\\d+[.\\d]+)";s:4:"name";s:25:"Safari Technology Preview";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:558;a:4:{s:5:"regex";s:58:"(?:(?:iPod|iPad|iPhone).+Version|MobileSafari)/(\\d+[.\\d]+)";s:4:"name";s:13:"Mobile Safari";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:559;a:4:{s:5:"regex";s:49:"NetworkingExtension/.+ Network/.+ iOS/(\\d+[.\\d]+)";s:4:"name";s:13:"Mobile Safari";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:560;a:4:{s:5:"regex";s:44:"(?:Version/(\\d+\\.[.\\d]+) .*)?Mobile.*Safari/";s:4:"name";s:13:"Mobile Safari";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:561;a:4:{s:5:"regex";s:69:"(?!^AppleCoreMedia/1\\.0\\.0)(?:iPod|(?<!Apple TV; U; CPU )iPhone|iPad)";s:4:"name";s:13:"Mobile Safari";s:7:"version";s:0:"";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:562;a:4:{s:5:"regex";s:89:"Version/(\\d+\\.[.\\d]+) .*Safari/|(?:Safari|Safari(?:%20)?%E6%B5%8F%E8%A7%88%E5%99%A8)/?\\d+";s:4:"name";s:6:"Safari";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:563;a:4:{s:5:"regex";s:43:"NetworkingExtension/(\\d+[.\\d]+).+ CFNetwork";s:4:"name";s:6:"Safari";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:564;a:3:{s:5:"regex";s:60:"(?:\\w{1,5}[_ ])?Dorado(?: WAP-Browser)?(?:[/ ]?(\\d+[.\\d]+))?";s:4:"name";s:6:"Dorado";s:7:"version";s:2:"$1";}i:565;a:4:{s:5:"regex";s:24:"NetSurf(?:/(\\d+[.\\d]+))?";s:4:"name";s:7:"NetSurf";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:7:"NetSurf";}}i:566;a:3:{s:5:"regex";s:4:"Uzbl";s:4:"name";s:4:"Uzbl";s:7:"version";s:0:"";}i:567;a:3:{s:5:"regex";s:13:"SimpleBrowser";s:4:"name";s:13:"SimpleBrowser";s:7:"version";s:0:"";}i:568;a:4:{s:5:"regex";s:20:"Zvu(?:/(\\d+[.\\d]+))?";s:4:"name";s:3:"Zvu";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:5:"Gecko";}}i:569;a:3:{s:5:"regex";s:28:"GOGGalaxyClient/(\\d+[.\\d]+)?";s:4:"name";s:10:"GOG Galaxy";s:7:"version";s:2:"$1";}i:570;a:3:{s:5:"regex";s:56:"WAP Browser/MAUI|(?:\\w*)Maui Wap Browser|MAUI[- ]Browser";s:4:"name";s:16:"MAUI WAP Browser";s:7:"version";s:0:"";}i:571;a:4:{s:5:"regex";s:24:"SP%20Browser/(\\d+[.\\d]+)";s:4:"name";s:10:"SP Browser";s:7:"version";s:2:"$1";s:6:"engine";a:1:{s:7:"default";s:6:"WebKit";}}i:572;a:4:{s:5:"regex";s:32:"(?<!like )Gecko(?!/\\d+ SlimerJS)";s:4:"name";s:7:"Firefox";s:7:"version";s:0:"";s:6:"engine";a:1:{s:7:"default";s:5:"Gecko";}}}}', ['allowed_classes' => false]);