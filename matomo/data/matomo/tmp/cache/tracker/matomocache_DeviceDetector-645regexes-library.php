<?php return unserialize('a:2:{s:8:"lifetime";i:1753701387;s:4:"data";a:136:{i:0;a:4:{s:5:"regex";s:26:"PhantomJS(?:/(\\d+[.\\d]+))?";s:4:"name";s:9:"PhantomJS";s:7:"version";s:2:"$1";s:3:"url";s:22:"https://phantomjs.org/";}i:1;a:4:{s:5:"regex";s:27:"IPinfoClient/.*/(\\d+[.\\d]+)";s:4:"name";s:6:"IPinfo";s:7:"version";s:2:"$1";s:3:"url";s:25:"https://github.com/ipinfo";}i:2;a:4:{s:5:"regex";s:21:"kiwi-tcms/(\\d+[.\\d]+)";s:4:"name";s:9:"Kiwi TCMS";s:7:"version";s:2:"$1";s:3:"url";s:20:"https://kiwitcms.org";}i:3;a:4:{s:5:"regex";s:20:"tcms-api/(\\d+[.\\d]+)";s:4:"name";s:13:"Kiwi TCMS API";s:7:"version";s:2:"$1";s:3:"url";s:20:"https://kiwitcms.org";}i:4;a:4:{s:5:"regex";s:31:"Fuzz Faster U Fool v(\\d+[.\\d]+)";s:4:"name";s:4:"FFUF";s:7:"version";s:2:"$1";s:3:"url";s:28:"https://github.com/ffuf/ffuf";}i:5;a:4:{s:5:"regex";s:14:"Slim Framework";s:4:"name";s:14:"Slim Framework";s:7:"version";s:0:"";s:3:"url";s:30:"https://www.slimframework.com/";}i:6;a:4:{s:5:"regex";s:10:"msray-plus";s:4:"name";s:10:"Msray-Plus";s:7:"version";s:0:"";s:3:"url";s:32:"https://github.com/super-l/msray";}i:7;a:4:{s:5:"regex";s:27:"HTMLParser(?:/(\\d+[.\\d]+))?";s:4:"name";s:11:"HTML Parser";s:7:"version";s:2:"$1";s:3:"url";s:35:"https://htmlparser.sourceforge.net/";}i:8;a:4:{s:5:"regex";s:26:"^got(?:/(\\d+\\.[.\\d]+))? \\(";s:4:"name";s:3:"got";s:7:"version";s:2:"$1";s:3:"url";s:35:"https://github.com/sindresorhus/got";}i:9;a:4:{s:5:"regex";s:8:"Typhoeus";s:4:"name";s:8:"Typhoeus";s:7:"version";s:0:"";s:3:"url";s:36:"https://github.com/typhoeus/typhoeus";}i:10;a:4:{s:5:"regex";s:13:"req/v([.\\d]+)";s:4:"name";s:3:"req";s:7:"version";s:2:"$1";s:3:"url";s:28:"https://github.com/imroc/req";}i:11;a:4:{s:5:"regex";s:17:"quic-go[ -]HTTP/3";s:4:"name";s:7:"quic-go";s:7:"version";s:0:"";s:3:"url";s:41:"https://github.com/lucas-clemente/quic-go";}i:12;a:4:{s:5:"regex";s:35:"azure-data-factory(?:/(\\d+[.\\d]+))?";s:4:"name";s:18:"Azure Data Factory";s:7:"version";s:2:"$1";s:3:"url";s:56:"https://azure.microsoft.com/en-us/products/data-factory/";}i:13;a:4:{s:5:"regex";s:16:"Dart/(\\d+[.\\d]+)";s:4:"name";s:4:"Dart";s:7:"version";s:2:"$1";s:3:"url";s:17:"https://dart.dev/";}i:14;a:4:{s:5:"regex";s:23:"r-curl(?:/(\\d+[.\\d]+))?";s:4:"name";s:6:"r-curl";s:7:"version";s:2:"$1";s:3:"url";s:30:"https://github.com/jeroen/curl";}i:15;a:4:{s:5:"regex";s:29:"python-httpx(?:/(\\d+[.\\d]+))?";s:4:"name";s:5:"HTTPX";s:7:"version";s:2:"$1";s:3:"url";s:29:"https://www.python-httpx.org/";}i:16;a:4:{s:5:"regex";s:25:"fasthttp(?:/(\\d+[.\\d]+))?";s:4:"name";s:8:"fasthttp";s:7:"version";s:2:"$1";s:3:"url";s:35:"https://github.com/valyala/fasthttp";}i:17;a:4:{s:5:"regex";s:28:"geoipupdate(?:/(\\d+[.\\d]+))?";s:4:"name";s:12:"GeoIP Update";s:7:"version";s:2:"$1";s:3:"url";s:38:"https://github.com/maxmind/geoipupdate";}i:18;a:4:{s:5:"regex";s:31:"PHP-Curl-Class(?:/(\\d+[.\\d]+))?";s:4:"name";s:14:"PHP cURL Class";s:7:"version";s:2:"$1";s:3:"url";s:48:"https://github.com/php-curl-class/php-curl-class";}i:19;a:4:{s:5:"regex";s:35:"Cpanel-HTTP-Client(?:/(\\d+[.\\d]+))?";s:4:"name";s:18:"cPanel HTTP Client";s:7:"version";s:2:"$1";s:3:"url";s:23:"https://www.cpanel.net/";}i:20;a:4:{s:5:"regex";s:30:"AnyEvent-HTTP(?:/(\\d+[.\\d]+))?";s:4:"name";s:13:"AnyEvent HTTP";s:7:"version";s:2:"$1";s:3:"url";s:39:"http://software.schmorp.de/pkg/AnyEvent";}i:21;a:4:{s:5:"regex";s:20:"SlimerJS/(\\d+[.\\d]+)";s:4:"name";s:8:"SlimerJS";s:7:"version";s:2:"$1";s:3:"url";s:25:"https://www.slimerjs.org/";}i:22;a:4:{s:5:"regex";s:17:"Jaunt/(\\d+[.\\d]+)";s:4:"name";s:5:"Jaunt";s:7:"version";s:2:"$1";s:3:"url";s:22:"https://jaunt-api.com/";}i:23;a:4:{s:5:"regex";s:19:"Cypress/(\\d+[.\\d]+)";s:4:"name";s:7:"Cypress";s:7:"version";s:2:"$1";s:3:"url";s:37:"https://github.com/cypress-io/cypress";}i:24;a:3:{s:5:"regex";s:21:"Wget(?:/(\\d+[.\\d]+))?";s:4:"name";s:4:"Wget";s:7:"version";s:2:"$1";}i:25;a:3:{s:5:"regex";s:32:"Guzzle(?:Http)?(?:/(\\d+[.\\d]+))?";s:4:"name";s:24:"Guzzle (PHP HTTP Client)";s:7:"version";s:2:"$1";}i:26;a:3:{s:5:"regex";s:20:"^Symfony HttpClient/";s:4:"name";s:7:"Symfony";s:7:"version";s:2:"$1";}i:27;a:3:{s:5:"regex";s:29:"(?:lib)?curl(?:/(\\d+[.\\d]+))?";s:4:"name";s:4:"curl";s:7:"version";s:2:"$1";}i:28;a:3:{s:5:"regex";s:32:"python-requests(?:/(\\d+[.\\d]+))?";s:4:"name";s:15:"Python Requests";s:7:"version";s:2:"$1";}i:29;a:4:{s:5:"regex";s:32:"Python-httplib2(?:/(\\d+[.\\d]+))?";s:4:"name";s:8:"httplib2";s:7:"version";s:2:"$1";s:3:"url";s:34:"https://pypi.org/project/httplib2/";}i:30;a:3:{s:5:"regex";s:33:"Python-urllib3?(?:/?(\\d+[.\\d]+))?";s:4:"name";s:13:"Python urllib";s:7:"version";s:2:"$1";}i:31;a:3:{s:5:"regex";s:35:"Apache-HttpClient(?:/?(\\d+[.\\d]+))?";s:4:"name";s:18:"Apache HTTP Client";s:7:"version";s:2:"$1";}i:32;a:3:{s:5:"regex";s:34:"Java-http-client(?:/?(\\d+[.\\d]+))?";s:4:"name";s:16:"Java HTTP Client";s:7:"version";s:2:"$1";}i:33;a:3:{s:5:"regex";s:17:"Java/?(\\d+[.\\d]+)";s:4:"name";s:4:"Java";s:7:"version";s:2:"$1";}i:34;a:3:{s:5:"regex";s:44:"(?:perlclient|libwww-perl)(?:/?(\\d+[.\\d]+))?";s:4:"name";s:4:"Perl";s:7:"version";s:2:"$1";}i:35;a:4:{s:5:"regex";s:25:"grpc-java-okhttp/([\\d.]+)";s:4:"name";s:9:"gRPC-Java";s:7:"version";s:2:"$1";s:3:"url";s:33:"https://github.com/grpc/grpc-java";}i:36;a:3:{s:5:"regex";s:35:"(?:okhttp|network-okhttp3)/([\\d.]+)";s:4:"name";s:6:"OkHttp";s:7:"version";s:2:"$1";}i:37;a:3:{s:5:"regex";s:16:"okhttp3-([\\d.]+)";s:4:"name";s:6:"OkHttp";s:7:"version";s:2:"$1";}i:38;a:4:{s:5:"regex";s:30:"HTTP_Request2(?:/(\\d+[.\\d]+))?";s:4:"name";s:13:"HTTP_Request2";s:7:"version";s:2:"$1";s:3:"url";s:42:"https://pear.php.net/package/http_request2";}i:39;a:4:{s:5:"regex";s:26:"Mechanize(?:/(\\d+[.\\d]+))?";s:4:"name";s:9:"Mechanize";s:7:"version";s:2:"$1";s:3:"url";s:42:"https://github.com/sparklemotion/mechanize";}i:40;a:3:{s:5:"regex";s:24:"aiohttp(?:/(\\d+[.\\d]+))?";s:4:"name";s:7:"aiohttp";s:7:"version";s:2:"$1";}i:41;a:3:{s:5:"regex";s:42:"Google-HTTP-Java-Client(?:/(\\d+[\\.\\w-]+))?";s:4:"name";s:23:"Google HTTP Java Client";s:7:"version";s:2:"$1";}i:42;a:3:{s:5:"regex";s:30:"WWW-Mechanize(?:/(\\d+[.\\d]+))?";s:4:"name";s:13:"WWW-Mechanize";s:7:"version";s:2:"$1";}i:43;a:4:{s:5:"regex";s:25:"Faraday(?: v(\\d+[.\\d]+))?";s:4:"name";s:7:"Faraday";s:7:"version";s:2:"$1";s:3:"url";s:37:"https://github.com/lostisland/faraday";}i:44;a:3:{s:5:"regex";s:59:"(?:Go-http-client|^Go )/?(?:(\\d+[.\\d]+))?(?: package http)?";s:4:"name";s:14:"Go-http-client";s:7:"version";s:2:"$1";}i:45;a:3:{s:5:"regex";s:27:"urlgrabber(?:/(\\d+[.\\d]+))?";s:4:"name";s:16:"urlgrabber (yum)";s:7:"version";s:2:"$1";}i:46;a:3:{s:5:"regex";s:23:"libdnf(?:/(\\d+[.\\d]+))?";s:4:"name";s:6:"libdnf";s:7:"version";s:2:"$1";}i:47;a:3:{s:5:"regex";s:23:"HTTPie(?:/(\\d+[.\\d]+))?";s:4:"name";s:6:"HTTPie";s:7:"version";s:2:"$1";}i:48;a:3:{s:5:"regex";s:32:"rest-client/(\\d+\\.[.\\d]+) .*ruby";s:4:"name";s:20:"REST Client for Ruby";s:7:"version";s:2:"$1";}i:49;a:4:{s:5:"regex";s:21:"RestSharp/(\\d+[.\\d]+)";s:4:"name";s:9:"RestSharp";s:7:"version";s:2:"$1";s:3:"url";s:38:"https://github.com/restsharp/RestSharp";}i:50;a:4:{s:5:"regex";s:23:"scalaj-http/(\\d+[.\\d]+)";s:4:"name";s:11:"ScalaJ HTTP";s:7:"version";s:2:"$1";s:3:"url";s:37:"https://github.com/scalaj/scalaj-http";}i:51;a:4:{s:5:"regex";s:18:"REST::Client/(\\d+)";s:4:"name";s:17:"Perl REST::Client";s:7:"version";s:2:"$1";s:3:"url";s:37:"https://metacpan.org/pod/REST::Client";}i:52;a:4:{s:5:"regex";s:24:"node-fetch/?(\\d+[.\\d]+)?";s:4:"name";s:10:"Node Fetch";s:7:"version";s:2:"$1";s:3:"url";s:40:"https://github.com/node-fetch/node-fetch";}i:53;a:4:{s:5:"regex";s:28:"electron-fetch/?(\\d+[.\\d]+)?";s:4:"name";s:14:"Electron Fetch";s:7:"version";s:2:"$1";s:3:"url";s:44:"https://github.com/arantes555/electron-fetch";}i:54;a:4:{s:5:"regex";s:24:"ReactorNetty/(\\d+[.\\d]+)";s:4:"name";s:12:"ReactorNetty";s:7:"version";s:2:"$1";s:3:"url";s:40:"https://github.com/reactor/reactor-netty";}i:55;a:4:{s:5:"regex";s:31:"PostmanRuntime(?:/(\\d+[.\\d]+))?";s:4:"name";s:15:"Postman Desktop";s:7:"version";s:2:"$1";s:3:"url";s:46:"https://github.com/postmanlabs/postman-runtime";}i:56;a:4:{s:5:"regex";s:25:"insomnia(?:/(\\d+[.\\d]+))?";s:4:"name";s:20:"Insomnia REST Client";s:7:"version";s:2:"$1";s:3:"url";s:21:"https://insomnia.rest";}i:57;a:4:{s:5:"regex";s:35:"Jakarta Commons-HttpClient/([.\\d]+)";s:4:"name";s:26:"Jakarta Commons HttpClient";s:7:"version";s:2:"$1";s:3:"url";s:36:"https://hc.apache.org/httpclient-3.x";}i:58;a:3:{s:5:"regex";s:33:"WinHttp\\.WinHttpRequest.+([.\\d]+)";s:4:"name";s:22:"WinHttp WinHttpRequest";s:7:"version";s:2:"$1";}i:59;a:3:{s:5:"regex";s:7:"WinHTTP";s:4:"name";s:12:"Windows HTTP";s:7:"version";s:0:"";}i:60;a:3:{s:5:"regex";s:31:"Embarcadero URI Client/([.\\d]+)";s:4:"name";s:22:"Embarcadero URI Client";s:7:"version";s:2:"$1";}i:61;a:3:{s:5:"regex";s:17:"Mikrotik/([.\\d]+)";s:4:"name";s:14:"Mikrotik Fetch";s:7:"version";s:2:"$1";}i:62;a:3:{s:5:"regex";s:26:"GRequests(?:/(\\d+[.\\d]+))?";s:4:"name";s:9:"GRequests";s:7:"version";s:2:"$1";}i:63;a:3:{s:5:"regex";s:18:"akka-http/([.\\d]+)";s:4:"name";s:9:"Akka HTTP";s:7:"version";s:2:"$1";}i:64;a:3:{s:5:"regex";s:22:"aria2(?:/(\\d+[.\\d]+))?";s:4:"name";s:5:"Aria2";s:7:"version";s:2:"$1";}i:65;a:3:{s:5:"regex";s:27:"(?:BTWebClient/|^uTorrent/)";s:4:"name";s:8:"uTorrent";s:7:"version";s:0:"";}i:66;a:3:{s:5:"regex";s:21:"gvfs/(?:(\\d+[.\\d]+))?";s:4:"name";s:4:"gvfs";s:7:"version";s:2:"$1";}i:67;a:3:{s:5:"regex";s:13:"uclient-fetch";s:4:"name";s:13:"uclient-fetch";s:7:"version";s:0:"";}i:68;a:3:{s:5:"regex";s:19:"cpprestsdk/([.\\d]+)";s:4:"name";s:12:"C++ REST SDK";s:7:"version";s:2:"$1";}i:69;a:3:{s:5:"regex";s:29:"lua-resty-http/([.\\d]+).+ngx_";s:4:"name";s:19:"LUA OpenResty NGINX";s:7:"version";s:2:"$1";}i:70;a:3:{s:5:"regex";s:21:"unirest-java/([.\\d]+)";s:4:"name";s:16:"Unirest for Java";s:7:"version";s:2:"$1";}i:71;a:3:{s:5:"regex";s:14:"jsdom/([.\\d]+)";s:4:"name";s:5:"jsdom";s:7:"version";s:2:"$1";}i:72;a:3:{s:5:"regex";s:16:"hackney/([.\\d]+)";s:4:"name";s:7:"hackney";s:7:"version";s:2:"$1";}i:73;a:3:{s:5:"regex";s:17:"go-resty/([.\\d]+)";s:4:"name";s:5:"Resty";s:7:"version";s:2:"$1";}i:74;a:3:{s:5:"regex";s:14:"pa11y/([.\\d]+)";s:4:"name";s:5:"Pa11y";s:7:"version";s:2:"$1";}i:75;a:3:{s:5:"regex";s:32:"ultimate_sitemap_parser/([.\\d]+)";s:4:"name";s:23:"Ultimate Sitemap Parser";s:7:"version";s:2:"$1";}i:76;a:3:{s:5:"regex";s:20:"Artifactory/([.\\d]+)";s:4:"name";s:11:"Artifactory";s:7:"version";s:2:"$1";}i:77;a:3:{s:5:"regex";s:14:"BSRPC ([.\\d]+)";s:4:"name";s:18:"Open Build Service";s:7:"version";s:2:"$1";}i:78;a:3:{s:5:"regex";s:16:"Buildah/([.\\d]+)";s:4:"name";s:7:"Buildah";s:7:"version";s:2:"$1";}i:79;a:3:{s:5:"regex";s:19:"buildkit/v?([.\\d]+)";s:4:"name";s:8:"BuildKit";s:7:"version";s:2:"$1";}i:80;a:3:{s:5:"regex";s:21:"containerd/v?([.\\d]+)";s:4:"name";s:10:"Containerd";s:7:"version";s:2:"$1";}i:81;a:3:{s:5:"regex";s:19:"containers/([.\\d]+)";s:4:"name";s:10:"containers";s:7:"version";s:2:"$1";}i:82;a:3:{s:5:"regex";s:14:"cri-o/([.\\d]+)";s:4:"name";s:5:"cri-o";s:7:"version";s:2:"$1";}i:83;a:3:{s:5:"regex";s:15:"docker/([.\\d]+)";s:4:"name";s:6:"docker";s:7:"version";s:2:"$1";}i:84;a:3:{s:5:"regex";s:30:"go-containerregistry/v([.\\d]+)";s:4:"name";s:21:"go-container registry";s:7:"version";s:2:"$1";}i:85;a:3:{s:5:"regex";s:15:"libpod/([.\\d]+)";s:4:"name";s:6:"libpod";s:7:"version";s:2:"$1";}i:86;a:3:{s:5:"regex";s:15:"skopeo/([.\\d]+)";s:4:"name";s:6:"Skopeo";s:7:"version";s:2:"$1";}i:87;a:3:{s:5:"regex";s:13:"Helm/([.\\d]+)";s:4:"name";s:4:"Helm";s:7:"version";s:2:"$1";}i:88;a:3:{s:5:"regex";s:22:"harbor-registry-client";s:4:"name";s:22:"Harbor registry client";s:7:"version";s:0:"";}i:89;a:3:{s:5:"regex";s:23:"axios(?:/?(\\d+[.\\d]+))?";s:4:"name";s:5:"Axios";s:7:"version";s:2:"$1";}i:90;a:3:{s:5:"regex";s:26:"^CarrierWave/(\\d+\\.[.\\d]+)";s:4:"name";s:11:"CarrierWave";s:7:"version";s:2:"$1";}i:91;a:3:{s:5:"regex";s:19:"^Deno/(\\d+\\.[.\\d]+)";s:4:"name";s:4:"Deno";s:7:"version";s:2:"$1";}i:92;a:3:{s:5:"regex";s:19:"^Down/(\\d+\\.[.\\d]+)";s:4:"name";s:4:"Down";s:7:"version";s:2:"$1";}i:93;a:3:{s:5:"regex";s:6:"^Lavf/";s:4:"name";s:6:"ffmpeg";s:7:"version";s:2:"$1";}i:94;a:3:{s:5:"regex";s:29:"^FileDownloader/(\\d+\\.[.\\d]+)";s:4:"name";s:14:"FileDownloader";s:7:"version";s:2:"$1";}i:95;a:3:{s:5:"regex";s:24:"^git-annex/(\\d+\\.[.\\d]+)";s:4:"name";s:9:"git-annex";s:7:"version";s:2:"$1";}i:96;a:3:{s:5:"regex";s:44:"^GStreamer(?: souphttpsrc)[ /](\\d+\\.[.\\d]+)?";s:4:"name";s:9:"GStreamer";s:7:"version";s:2:"$1";}i:97;a:3:{s:5:"regex";s:24:"^HTTP-Tiny/(\\d+\\.[.\\d]+)";s:4:"name";s:9:"HTTP:Tiny";s:7:"version";s:2:"$1";}i:98;a:3:{s:5:"regex";s:16:"KaiOS Downloader";s:4:"name";s:16:"KaiOS Downloader";s:7:"version";s:0:"";}i:99;a:3:{s:5:"regex";s:22:"^libsoup/(\\d+\\.[.\\d]+)";s:4:"name";s:7:"libsoup";s:7:"version";s:2:"$1";}i:100;a:3:{s:5:"regex";s:16:"^Android\\.LVLDM$";s:4:"name";s:36:"Android License Verification Library";s:7:"version";s:2:"$1";}i:101;a:3:{s:5:"regex";s:14:"^PRDownloader$";s:4:"name";s:12:"PRDownloader";s:7:"version";s:0:"";}i:102;a:3:{s:5:"regex";s:22:"^reqwest/(\\d+\\.[.\\d]+)";s:4:"name";s:7:"reqwest";s:7:"version";s:2:"$1";}i:103;a:3:{s:5:"regex";s:15:"^resty-requests";s:4:"name";s:14:"resty-requests";s:7:"version";s:0:"";}i:104;a:3:{s:5:"regex";s:5:"^Ruby";s:4:"name";s:4:"ruby";s:7:"version";s:0:"";}i:105;a:3:{s:5:"regex";s:32:"^SafariViewService/(\\d+\\.[.\\d]+)";s:4:"name";s:19:"Safari View Service";s:7:"version";s:2:"$1";}i:106;a:3:{s:5:"regex";s:8:"^undici$";s:4:"name";s:6:"undici";s:7:"version";s:0:"";}i:107;a:3:{s:5:"regex";s:30:"^URL/Emacs Emacs/(\\d+\\.[.\\d]+)";s:4:"name";s:5:"Emacs";s:7:"version";s:2:"$1";}i:108;a:3:{s:5:"regex";s:16:"^FDM[ /]([\\d.]+)";s:4:"name";s:21:"Free Download Manager";s:7:"version";s:2:"$1";}i:109;a:3:{s:5:"regex";s:19:"OkDownload/([\\d.]+)";s:4:"name";s:18:"OKDownload Library";s:7:"version";s:2:"$1";}i:110;a:3:{s:5:"regex";s:28:"^Libsyn4-?(?:peek|download)$";s:4:"name";s:6:"Libsyn";s:7:"version";s:0:"";}i:111;a:3:{s:5:"regex";s:22:"AppleCoreMedia/1\\.0\\.0";s:4:"name";s:15:"iOS Application";s:7:"version";s:0:"";}i:112;a:4:{s:5:"regex";s:28:"cpp-httplib(?:/(\\d+[.\\d]+))?";s:4:"name";s:11:"cpp-httplib";s:7:"version";s:2:"$1";s:3:"url";s:38:"https://github.com/yhirose/cpp-httplib";}i:113;a:4:{s:5:"regex";s:23:"Definitely-Not-Requests";s:4:"name";s:8:"Requests";s:7:"version";s:0:"";s:3:"url";s:31:"https://github.com/psf/requests";}i:114;a:4:{s:5:"regex";s:16:"Stealer ([\\d.]+)";s:4:"name";s:7:"Stealer";s:7:"version";s:2:"$1";s:3:"url";s:35:"https://github.com/hotrush/stealer/";}i:115;a:4:{s:5:"regex";s:29:"Mandrill-PHP(?:/(\\d+[.\\d]+))?";s:4:"name";s:12:"Mandrill PHP";s:7:"version";s:2:"$1";s:3:"url";s:60:"https://bitbucket.org/mailchimp/mandrill-api-php/src/master/";}i:116;a:4:{s:5:"regex";s:8:"^Podgrab";s:4:"name";s:7:"Podgrab";s:7:"version";s:0:"";s:3:"url";s:35:"https://github.com/akhilrex/podgrab";}i:117;a:4:{s:5:"regex";s:45:"^Podcast Provider.*?Radio Downloader ([\\d.]+)";s:4:"name";s:16:"Radio Downloader";s:7:"version";s:2:"$1";s:3:"url";s:41:"https://nerdoftheherd.com/tools/radiodld/";}i:118;a:4:{s:5:"regex";s:27:"^ESP32 HTTP Client/([\\d.]+)";s:4:"name";s:17:"ESP32 HTTP Client";s:7:"version";s:2:"$1";s:3:"url";s:42:"https://github.com/espressif/arduino-esp32";}i:119;a:4:{s:5:"regex";s:38:"babashka\\.http-client(?:/(\\d+[.\\d]+))?";s:4:"name";s:20:"Babashka HTTP Client";s:7:"version";s:2:"$1";s:3:"url";s:39:"https://github.com/babashka/http-client";}i:120;a:4:{s:5:"regex";s:25:"http\\.rb(?:/(\\d+[.\\d]+))?";s:4:"name";s:7:"http.rb";s:7:"version";s:2:"$1";s:3:"url";s:30:"https://github.com/httprb/http";}i:121;a:4:{s:5:"regex";s:32:"node-superagent(?:/(\\d+[.\\d]+))?";s:4:"name";s:10:"superagent";s:7:"version";s:2:"$1";s:3:"url";s:35:"https://github.com/ladjs/superagent";}i:122;a:4:{s:5:"regex";s:7:"CakePHP";s:4:"name";s:7:"CakePHP";s:7:"version";s:0:"";s:3:"url";s:24:"https://www.cakephp.org/";}i:123;a:4:{s:5:"regex";s:11:"request\\.js";s:4:"name";s:7:"request";s:7:"version";s:0:"";s:3:"url";s:34:"https://github.com/request/request";}i:124;a:4:{s:5:"regex";s:23:"qbhttp(?:/(\\d+[.\\d]+))?";s:4:"name";s:6:"QbHttp";s:7:"version";s:2:"$1";s:3:"url";s:32:"https://github.com/OpenQb/QbHttp";}i:125;a:4:{s:5:"regex";s:23:"httprs(?:/(\\d+[.\\d]+))?";s:4:"name";s:6:"httprs";s:7:"version";s:2:"$1";s:3:"url";s:45:"https://github.com/http-server-rs/http-server";}i:126;a:4:{s:5:"regex";s:22:"Boto3(?:/(\\d+[.\\d]+))?";s:4:"name";s:5:"Boto3";s:7:"version";s:2:"$1";s:3:"url";s:29:"https://github.com/boto/boto3";}i:127;a:4:{s:5:"regex";s:30:"Python-xmlrpc(?:/(\\d+[.\\d]+))?";s:4:"name";s:7:"XML-RPC";s:7:"version";s:2:"$1";s:3:"url";s:45:"https://docs.python.org/3/library/xmlrpc.html";}i:128;a:4:{s:5:"regex";s:36:"ICAP-Client-Library(?:/(\\d+[.\\d]+))?";s:4:"name";s:11:"ICAP Client";s:7:"version";s:2:"$1";s:3:"url";s:43:"https://github.com/Peoplecantfly/icapserver";}i:129;a:4:{s:5:"regex";s:29:"Cygwin-Setup(?:/(\\d+[.\\d]+))?";s:4:"name";s:6:"Cygwin";s:7:"version";s:2:"$1";s:3:"url";s:23:"https://www.cygwin.com/";}i:130;a:4:{s:5:"regex";s:42:"azsdk-python-storage-blob(?:/(\\d+[.\\d]+))?";s:4:"name";s:18:"Azure Blob Storage";s:7:"version";s:2:"$1";s:3:"url";s:85:"https://learn.microsoft.com/en-us/azure/storage/blobs/storage-quickstart-blobs-python";}i:131;a:4:{s:5:"regex";s:28:"trafilatura(?:/(\\d+[.\\d]+))?";s:4:"name";s:11:"trafilatura";s:7:"version";s:2:"$1";s:3:"url";s:36:"https://github.com/adbar/trafilatura";}i:132;a:4:{s:5:"regex";s:23:"sqlmap(?:/(\\d+[.\\d]+))?";s:4:"name";s:6:"sqlmap";s:7:"version";s:2:"$1";s:3:"url";s:19:"https://sqlmap.org/";}i:133;a:4:{s:5:"regex";s:27:"vimeo\\.php(?: (\\d+[.\\d]+))?";s:4:"name";s:9:"vimeo.php";s:7:"version";s:2:"$1";s:3:"url";s:34:"https://github.com/vimeo/vimeo.php";}i:134;a:4:{s:5:"regex";s:17:"^PHP/?(\\d+[.\\d]+)";s:4:"name";s:3:"PHP";s:7:"version";s:2:"$1";s:3:"url";s:0:"";}i:135;a:4:{s:5:"regex";s:23:"go-network-v(\\d+[.\\d]+)";s:4:"name";s:10:"go-network";s:7:"version";s:2:"$1";s:3:"url";s:0:"";}}}', ['allowed_classes' => false]);