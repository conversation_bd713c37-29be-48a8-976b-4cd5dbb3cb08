<?php return unserialize('a:2:{s:8:"lifetime";i:**********;s:4:"data";a:398:{s:21:"PluginCoreVueMetadata";a:7:{s:11:"description";s:25:"CoreVue_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:30:"PluginCorePluginsAdminMetadata";a:7:{s:11:"description";s:34:"CorePluginsAdmin_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:27:"PluginCoreAdminHomeMetadata";a:7:{s:11:"description";s:31:"CoreAdminHome_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:22:"PluginCoreHomeMetadata";a:7:{s:11:"description";s:26:"CoreHome_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:31:"PluginWebsiteMeasurableMetadata";a:8:{s:11:"description";s:61:"Analytics for the web: lets you measure and analyze Websites.";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}s:4:"name";s:17:"WebsiteMeasurable";}s:32:"PluginIntranetMeasurableMetadata";a:8:{s:11:"description";s:70:"Analytics for the web: lets you measure and analyze intranet websites.";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}s:4:"name";s:18:"IntranetMeasurable";}s:25:"PluginDiagnosticsMetadata";a:7:{s:11:"description";s:74:"Performs diagnostics to check that Matomo is installed and runs correctly.";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:32:"PluginCoreVisualizationsMetadata";a:7:{s:11:"description";s:36:"CoreVisualizations_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:19:"PluginProxyMetadata";a:7:{s:11:"description";s:14:"Proxy services";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:17:"PluginAPIMetadata";a:7:{s:11:"description";s:21:"API_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:23:"PluginWidgetizeMetadata";a:7:{s:11:"description";s:27:"Widgetize_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:25:"PluginTransitionsMetadata";a:7:{s:11:"description";s:29:"Transitions_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:30:"PluginLanguagesManagerMetadata";a:7:{s:11:"description";s:34:"LanguagesManager_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:21:"PluginActionsMetadata";a:7:{s:11:"description";s:25:"Actions_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:23:"PluginDashboardMetadata";a:7:{s:11:"description";s:27:"Dashboard_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:24:"PluginMultiSitesMetadata";a:7:{s:11:"description";s:28:"MultiSites_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:23:"PluginReferrersMetadata";a:7:{s:11:"description";s:27:"Referrers_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:26:"PluginUserLanguageMetadata";a:7:{s:11:"description";s:30:"UserLanguage_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:30:"PluginDevicesDetectionMetadata";a:7:{s:11:"description";s:34:"DevicesDetection_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:19:"PluginGoalsMetadata";a:7:{s:11:"description";s:23:"Goals_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:23:"PluginEcommerceMetadata";a:8:{s:11:"description";s:27:"Ecommerce_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}s:4:"name";s:9:"Ecommerce";}s:17:"PluginSEOMetadata";a:7:{s:11:"description";s:21:"SEO_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:20:"PluginEventsMetadata";a:7:{s:11:"description";s:24:"Events_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:25:"PluginUserCountryMetadata";a:7:{s:11:"description";s:29:"UserCountry_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:20:"PluginGeoIp2Metadata";a:7:{s:11:"description";s:24:"GeoIp2_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:27:"PluginVisitsSummaryMetadata";a:7:{s:11:"description";s:31:"VisitsSummary_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:28:"PluginVisitFrequencyMetadata";a:7:{s:11:"description";s:32:"VisitFrequency_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:23:"PluginVisitTimeMetadata";a:7:{s:11:"description";s:27:"VisitTime_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:29:"PluginVisitorInterestMetadata";a:7:{s:11:"description";s:33:"VisitorInterest_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:23:"PluginRssWidgetMetadata";a:9:{s:11:"description";s:93:"Matomo Platform showcase: how to create a new widget that displays a user submitted RSS feed.";s:8:"homepage";s:18:"https://matomo.org";s:7:"authors";a:1:{i:0;a:3:{s:4:"name";s:6:"Matomo";s:5:"email";s:16:"<EMAIL>";s:8:"homepage";s:18:"https://matomo.org";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:3:"1.0";s:5:"theme";b:0;s:7:"require";a:0:{}s:4:"name";s:9:"RssWidget";s:8:"keywords";a:3:{i:0;s:7:"example";i:1;s:4:"feed";i:2;s:6:"widget";}}s:22:"PluginFeedbackMetadata";a:7:{s:11:"description";s:26:"Feedback_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:21:"PluginMonologMetadata";a:7:{s:11:"description";s:36:"Adds logging capabilities to Matomo.";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:19:"PluginLoginMetadata";a:7:{s:11:"description";s:23:"Login_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:27:"PluginTwoFactorAuthMetadata";a:7:{s:11:"description";s:31:"TwoFactorAuth_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:26:"PluginUsersManagerMetadata";a:7:{s:11:"description";s:30:"UsersManager_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:26:"PluginSitesManagerMetadata";a:7:{s:11:"description";s:30:"SitesManager_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:26:"PluginInstallationMetadata";a:7:{s:11:"description";s:30:"Installation_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:25:"PluginCoreUpdaterMetadata";a:7:{s:11:"description";s:29:"CoreUpdater_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:25:"PluginCoreConsoleMetadata";a:7:{s:11:"description";s:29:"CoreConsole_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:30:"PluginScheduledReportsMetadata";a:7:{s:11:"description";s:34:"ScheduledReports_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:28:"PluginUserCountryMapMetadata";a:7:{s:11:"description";s:32:"UserCountryMap_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:18:"PluginLiveMetadata";a:7:{s:11:"description";s:22:"Live_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:28:"PluginPrivacyManagerMetadata";a:7:{s:11:"description";s:32:"PrivacyManager_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:24:"PluginImageGraphMetadata";a:7:{s:11:"description";s:28:"ImageGraph_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:25:"PluginAnnotationsMetadata";a:7:{s:11:"description";s:29:"Annotations_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:29:"PluginMobileMessagingMetadata";a:7:{s:11:"description";s:33:"MobileMessaging_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:21:"PluginOverlayMetadata";a:7:{s:11:"description";s:25:"Overlay_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:27:"PluginSegmentEditorMetadata";a:7:{s:11:"description";s:31:"SegmentEditor_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:22:"PluginInsightsMetadata";a:7:{s:11:"description";s:26:"Insights_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:22:"PluginMorpheusMetadata";a:9:{s:11:"description";s:272:"Morpheus is the default theme of Matomo designed to help you focus on your analytics. In Greek mythology, Morpheus is the God of dreams. In the Matrix movie, Morpheus is the leader of the rebel forces who fight to awaken humans from a dreamlike reality called The Matrix. ";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:1;s:7:"require";a:0:{}s:4:"name";s:8:"Morpheus";s:10:"stylesheet";s:21:"stylesheets/main.less";}s:22:"PluginContentsMetadata";a:7:{s:11:"description";s:26:"Contents_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:26:"PluginBulkTrackingMetadata";a:8:{s:11:"description";s:123:"Provides ability to send several Tracking API requests in one bulk request. Makes importing a lot of data in Matomo faster.";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}s:4:"name";s:12:"BulkTracking";}s:24:"PluginResolutionMetadata";a:7:{s:11:"description";s:28:"Resolution_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:27:"PluginDevicePluginsMetadata";a:7:{s:11:"description";s:31:"DevicePlugins_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:23:"PluginHeartbeatMetadata";a:7:{s:11:"description";s:30:"Handles ping tracker requests.";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:18:"PluginIntlMetadata";a:7:{s:11:"description";s:22:"Intl_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:25:"PluginMarketplaceMetadata";a:7:{s:11:"description";s:29:"Marketplace_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:34:"PluginProfessionalServicesMetadata";a:8:{s:11:"description";s:78:"Provides widgets to learn about Professional services and products for Matomo.";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}s:4:"name";s:20:"ProfessionalServices";}s:20:"PluginUserIdMetadata";a:7:{s:11:"description";s:24:"UserId_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:29:"PluginCustomJsTrackerMetadata";a:7:{s:11:"description";s:33:"CustomJsTracker_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:18:"PluginTourMetadata";a:7:{s:11:"description";s:22:"Tour_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:29:"PluginPagePerformanceMetadata";a:7:{s:11:"description";s:33:"PagePerformance_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:30:"PluginCustomDimensionsMetadata";a:8:{s:11:"description";s:34:"CustomDimensions_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}s:4:"name";s:16:"CustomDimensions";}s:35:"PluginJsTrackerInstallCheckMetadata";a:7:{s:11:"description";s:39:"JsTrackerInstallCheck_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:26:"PluginFeatureFlagsMetadata";a:7:{s:11:"description";s:30:"FeatureFlags_PluginDescription";s:8:"homepage";s:19:"https://matomo.org/";s:7:"authors";a:1:{i:0;a:2:{s:4:"name";s:6:"Matomo";s:8:"homepage";s:19:"https://matomo.org/";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.3.2";s:5:"theme";b:0;s:7:"require";a:0:{}}s:28:"PluginQueuedTrackingMetadata";a:12:{s:11:"description";s:150:"Scale your large traffic Matomo service by queuing tracking requests in Redis or MySQL for better performance and reliability when experiencing peaks.";s:8:"homepage";s:18:"https://matomo.org";s:7:"authors";a:1:{i:0;a:3:{s:4:"name";s:6:"Matomo";s:5:"email";s:16:"<EMAIL>";s:8:"homepage";s:18:"https://matomo.org";}}s:7:"license";s:7:"GPL v3+";s:7:"version";s:5:"5.1.1";s:5:"theme";b:0;s:7:"require";a:1:{s:6:"matomo";s:20:">=5.0.0-b1,<6.0.0-b1";}s:4:"name";s:14:"QueuedTracking";s:8:"keywords";a:4:{i:0;s:7:"tracker";i:1;s:8:"tracking";i:2;s:5:"queue";i:3;s:5:"redis";}s:7:"support";a:5:{s:5:"email";s:16:"<EMAIL>";s:6:"issues";s:58:"https://github.com/matomo-org/plugin-QueuedTracking/issues";s:5:"forum";s:24:"https://forum.matomo.org";s:6:"source";s:51:"https://github.com/matomo-org/plugin-QueuedTracking";s:4:"wiki";s:56:"https://github.com/matomo-org/plugin-QueuedTracking/wiki";}s:8:"category";s:11:"development";s:12:"license_file";s:44:"/var/www/html/plugins/QueuedTracking/LICENSE";}s:58:"PluginCoreVueColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:67:"PluginCorePluginsAdminColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:64:"PluginCoreAdminHomeColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:59:"PluginCoreHomeColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:29:{s:49:"/var/www/html/plugins/CoreHome/Columns/IdSite.php";s:37:"Piwik\\Plugins\\CoreHome\\Columns\\IdSite";s:53:"/var/www/html/plugins/CoreHome/Columns/Profilable.php";s:41:"Piwik\\Plugins\\CoreHome\\Columns\\Profilable";s:49:"/var/www/html/plugins/CoreHome/Columns/UserId.php";s:37:"Piwik\\Plugins\\CoreHome\\Columns\\UserId";s:65:"/var/www/html/plugins/CoreHome/Columns/VisitFirstActionMinute.php";s:53:"Piwik\\Plugins\\CoreHome\\Columns\\VisitFirstActionMinute";s:63:"/var/www/html/plugins/CoreHome/Columns/VisitFirstActionTime.php";s:51:"Piwik\\Plugins\\CoreHome\\Columns\\VisitFirstActionTime";s:57:"/var/www/html/plugins/CoreHome/Columns/VisitGoalBuyer.php";s:45:"Piwik\\Plugins\\CoreHome\\Columns\\VisitGoalBuyer";s:61:"/var/www/html/plugins/CoreHome/Columns/VisitGoalConverted.php";s:49:"Piwik\\Plugins\\CoreHome\\Columns\\VisitGoalConverted";s:50:"/var/www/html/plugins/CoreHome/Columns/VisitId.php";s:38:"Piwik\\Plugins\\CoreHome\\Columns\\VisitId";s:50:"/var/www/html/plugins/CoreHome/Columns/VisitIp.php";s:38:"Piwik\\Plugins\\CoreHome\\Columns\\VisitIp";s:62:"/var/www/html/plugins/CoreHome/Columns/VisitLastActionDate.php";s:50:"Piwik\\Plugins\\CoreHome\\Columns\\VisitLastActionDate";s:68:"/var/www/html/plugins/CoreHome/Columns/VisitLastActionDayOfMonth.php";s:56:"Piwik\\Plugins\\CoreHome\\Columns\\VisitLastActionDayOfMonth";s:67:"/var/www/html/plugins/CoreHome/Columns/VisitLastActionDayOfWeek.php";s:55:"Piwik\\Plugins\\CoreHome\\Columns\\VisitLastActionDayOfWeek";s:67:"/var/www/html/plugins/CoreHome/Columns/VisitLastActionDayOfYear.php";s:55:"Piwik\\Plugins\\CoreHome\\Columns\\VisitLastActionDayOfYear";s:64:"/var/www/html/plugins/CoreHome/Columns/VisitLastActionMinute.php";s:52:"Piwik\\Plugins\\CoreHome\\Columns\\VisitLastActionMinute";s:63:"/var/www/html/plugins/CoreHome/Columns/VisitLastActionMonth.php";s:51:"Piwik\\Plugins\\CoreHome\\Columns\\VisitLastActionMonth";s:65:"/var/www/html/plugins/CoreHome/Columns/VisitLastActionQuarter.php";s:53:"Piwik\\Plugins\\CoreHome\\Columns\\VisitLastActionQuarter";s:64:"/var/www/html/plugins/CoreHome/Columns/VisitLastActionSecond.php";s:52:"Piwik\\Plugins\\CoreHome\\Columns\\VisitLastActionSecond";s:62:"/var/www/html/plugins/CoreHome/Columns/VisitLastActionTime.php";s:50:"Piwik\\Plugins\\CoreHome\\Columns\\VisitLastActionTime";s:68:"/var/www/html/plugins/CoreHome/Columns/VisitLastActionWeekOfYear.php";s:56:"Piwik\\Plugins\\CoreHome\\Columns\\VisitLastActionWeekOfYear";s:62:"/var/www/html/plugins/CoreHome/Columns/VisitLastActionYear.php";s:50:"Piwik\\Plugins\\CoreHome\\Columns\\VisitLastActionYear";s:57:"/var/www/html/plugins/CoreHome/Columns/VisitTotalTime.php";s:45:"Piwik\\Plugins\\CoreHome\\Columns\\VisitTotalTime";s:64:"/var/www/html/plugins/CoreHome/Columns/VisitorDaysSinceFirst.php";s:52:"Piwik\\Plugins\\CoreHome\\Columns\\VisitorDaysSinceFirst";s:64:"/var/www/html/plugins/CoreHome/Columns/VisitorDaysSinceOrder.php";s:52:"Piwik\\Plugins\\CoreHome\\Columns\\VisitorDaysSinceOrder";s:61:"/var/www/html/plugins/CoreHome/Columns/VisitorFingerprint.php";s:49:"Piwik\\Plugins\\CoreHome\\Columns\\VisitorFingerprint";s:52:"/var/www/html/plugins/CoreHome/Columns/VisitorId.php";s:40:"Piwik\\Plugins\\CoreHome\\Columns\\VisitorId";s:59:"/var/www/html/plugins/CoreHome/Columns/VisitorReturning.php";s:47:"Piwik\\Plugins\\CoreHome\\Columns\\VisitorReturning";s:67:"/var/www/html/plugins/CoreHome/Columns/VisitorSecondsSinceFirst.php";s:55:"Piwik\\Plugins\\CoreHome\\Columns\\VisitorSecondsSinceFirst";s:67:"/var/www/html/plugins/CoreHome/Columns/VisitorSecondsSinceOrder.php";s:55:"Piwik\\Plugins\\CoreHome\\Columns\\VisitorSecondsSinceOrder";s:54:"/var/www/html/plugins/CoreHome/Columns/VisitsCount.php";s:42:"Piwik\\Plugins\\CoreHome\\Columns\\VisitsCount";}s:68:"PluginWebsiteMeasurableColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:69:"PluginIntranetMeasurableColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:62:"PluginDiagnosticsColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:69:"PluginCoreVisualizationsColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:56:"PluginProxyColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:54:"PluginAPIColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:60:"PluginWidgetizeColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:62:"PluginTransitionsColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:67:"PluginLanguagesManagerColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:58:"PluginActionsColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:7:{s:56:"/var/www/html/plugins/Actions/Columns/EntryPageTitle.php";s:44:"Piwik\\Plugins\\Actions\\Columns\\EntryPageTitle";s:54:"/var/www/html/plugins/Actions/Columns/EntryPageUrl.php";s:42:"Piwik\\Plugins\\Actions\\Columns\\EntryPageUrl";s:55:"/var/www/html/plugins/Actions/Columns/ExitPageTitle.php";s:43:"Piwik\\Plugins\\Actions\\Columns\\ExitPageTitle";s:53:"/var/www/html/plugins/Actions/Columns/ExitPageUrl.php";s:41:"Piwik\\Plugins\\Actions\\Columns\\ExitPageUrl";s:59:"/var/www/html/plugins/Actions/Columns/VisitTotalActions.php";s:47:"Piwik\\Plugins\\Actions\\Columns\\VisitTotalActions";s:64:"/var/www/html/plugins/Actions/Columns/VisitTotalInteractions.php";s:52:"Piwik\\Plugins\\Actions\\Columns\\VisitTotalInteractions";s:60:"/var/www/html/plugins/Actions/Columns/VisitTotalSearches.php";s:48:"Piwik\\Plugins\\Actions\\Columns\\VisitTotalSearches";}s:60:"PluginDashboardColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:61:"PluginMultiSitesColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:60:"PluginReferrersColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:6:{s:52:"/var/www/html/plugins/Referrers/Columns/Campaign.php";s:40:"Piwik\\Plugins\\Referrers\\Columns\\Campaign";s:51:"/var/www/html/plugins/Referrers/Columns/Keyword.php";s:39:"Piwik\\Plugins\\Referrers\\Columns\\Keyword";s:56:"/var/www/html/plugins/Referrers/Columns/ReferrerName.php";s:44:"Piwik\\Plugins\\Referrers\\Columns\\ReferrerName";s:56:"/var/www/html/plugins/Referrers/Columns/ReferrerType.php";s:44:"Piwik\\Plugins\\Referrers\\Columns\\ReferrerType";s:55:"/var/www/html/plugins/Referrers/Columns/ReferrerUrl.php";s:43:"Piwik\\Plugins\\Referrers\\Columns\\ReferrerUrl";s:51:"/var/www/html/plugins/Referrers/Columns/Website.php";s:39:"Piwik\\Plugins\\Referrers\\Columns\\Website";}s:63:"PluginUserLanguageColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:1:{s:55:"/var/www/html/plugins/UserLanguage/Columns/Language.php";s:43:"Piwik\\Plugins\\UserLanguage\\Columns\\Language";}s:67:"PluginDevicesDetectionColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:9:{s:64:"/var/www/html/plugins/DevicesDetection/Columns/BrowserEngine.php";s:52:"Piwik\\Plugins\\DevicesDetection\\Columns\\BrowserEngine";s:62:"/var/www/html/plugins/DevicesDetection/Columns/BrowserName.php";s:50:"Piwik\\Plugins\\DevicesDetection\\Columns\\BrowserName";s:65:"/var/www/html/plugins/DevicesDetection/Columns/BrowserVersion.php";s:53:"Piwik\\Plugins\\DevicesDetection\\Columns\\BrowserVersion";s:61:"/var/www/html/plugins/DevicesDetection/Columns/ClientType.php";s:49:"Piwik\\Plugins\\DevicesDetection\\Columns\\ClientType";s:62:"/var/www/html/plugins/DevicesDetection/Columns/DeviceBrand.php";s:50:"Piwik\\Plugins\\DevicesDetection\\Columns\\DeviceBrand";s:62:"/var/www/html/plugins/DevicesDetection/Columns/DeviceModel.php";s:50:"Piwik\\Plugins\\DevicesDetection\\Columns\\DeviceModel";s:61:"/var/www/html/plugins/DevicesDetection/Columns/DeviceType.php";s:49:"Piwik\\Plugins\\DevicesDetection\\Columns\\DeviceType";s:53:"/var/www/html/plugins/DevicesDetection/Columns/Os.php";s:41:"Piwik\\Plugins\\DevicesDetection\\Columns\\Os";s:60:"/var/www/html/plugins/DevicesDetection/Columns/OsVersion.php";s:48:"Piwik\\Plugins\\DevicesDetection\\Columns\\OsVersion";}s:56:"PluginGoalsColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:60:"PluginEcommerceColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:54:"PluginSEOColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:57:"PluginEventsColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:1:{s:52:"/var/www/html/plugins/Events/Columns/TotalEvents.php";s:40:"Piwik\\Plugins\\Events\\Columns\\TotalEvents";}s:62:"PluginUserCountryColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:5:{s:50:"/var/www/html/plugins/UserCountry/Columns/City.php";s:38:"Piwik\\Plugins\\UserCountry\\Columns\\City";s:53:"/var/www/html/plugins/UserCountry/Columns/Country.php";s:41:"Piwik\\Plugins\\UserCountry\\Columns\\Country";s:54:"/var/www/html/plugins/UserCountry/Columns/Latitude.php";s:42:"Piwik\\Plugins\\UserCountry\\Columns\\Latitude";s:55:"/var/www/html/plugins/UserCountry/Columns/Longitude.php";s:43:"Piwik\\Plugins\\UserCountry\\Columns\\Longitude";s:52:"/var/www/html/plugins/UserCountry/Columns/Region.php";s:40:"Piwik\\Plugins\\UserCountry\\Columns\\Region";}s:57:"PluginGeoIp2Columns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:64:"PluginVisitsSummaryColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:65:"PluginVisitFrequencyColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:60:"PluginVisitTimeColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:2:{s:55:"/var/www/html/plugins/VisitTime/Columns/LocalMinute.php";s:43:"Piwik\\Plugins\\VisitTime\\Columns\\LocalMinute";s:53:"/var/www/html/plugins/VisitTime/Columns/LocalTime.php";s:41:"Piwik\\Plugins\\VisitTime\\Columns\\LocalTime";}s:66:"PluginVisitorInterestColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:2:{s:70:"/var/www/html/plugins/VisitorInterest/Columns/VisitorDaysSinceLast.php";s:58:"Piwik\\Plugins\\VisitorInterest\\Columns\\VisitorDaysSinceLast";s:73:"/var/www/html/plugins/VisitorInterest/Columns/VisitorSecondsSinceLast.php";s:61:"Piwik\\Plugins\\VisitorInterest\\Columns\\VisitorSecondsSinceLast";}s:60:"PluginRssWidgetColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:59:"PluginFeedbackColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:58:"PluginMonologColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:56:"PluginLoginColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:64:"PluginTwoFactorAuthColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:63:"PluginUsersManagerColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:63:"PluginSitesManagerColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:63:"PluginInstallationColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:62:"PluginCoreUpdaterColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:62:"PluginCoreConsoleColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:67:"PluginScheduledReportsColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:65:"PluginUserCountryMapColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:55:"PluginLiveColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:65:"PluginPrivacyManagerColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:61:"PluginImageGraphColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:62:"PluginAnnotationsColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:66:"PluginMobileMessagingColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:58:"PluginOverlayColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:64:"PluginSegmentEditorColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:59:"PluginInsightsColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:59:"PluginMorpheusColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:59:"PluginContentsColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:63:"PluginBulkTrackingColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:61:"PluginResolutionColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:1:{s:55:"/var/www/html/plugins/Resolution/Columns/Resolution.php";s:43:"Piwik\\Plugins\\Resolution\\Columns\\Resolution";}s:64:"PluginDevicePluginsColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:8:{s:60:"/var/www/html/plugins/DevicePlugins/Columns/PluginCookie.php";s:48:"Piwik\\Plugins\\DevicePlugins\\Columns\\PluginCookie";s:59:"/var/www/html/plugins/DevicePlugins/Columns/PluginFlash.php";s:47:"Piwik\\Plugins\\DevicePlugins\\Columns\\PluginFlash";s:58:"/var/www/html/plugins/DevicePlugins/Columns/PluginJava.php";s:46:"Piwik\\Plugins\\DevicePlugins\\Columns\\PluginJava";s:57:"/var/www/html/plugins/DevicePlugins/Columns/PluginPdf.php";s:45:"Piwik\\Plugins\\DevicePlugins\\Columns\\PluginPdf";s:63:"/var/www/html/plugins/DevicePlugins/Columns/PluginQuickTime.php";s:51:"Piwik\\Plugins\\DevicePlugins\\Columns\\PluginQuickTime";s:64:"/var/www/html/plugins/DevicePlugins/Columns/PluginRealPlayer.php";s:52:"Piwik\\Plugins\\DevicePlugins\\Columns\\PluginRealPlayer";s:65:"/var/www/html/plugins/DevicePlugins/Columns/PluginSilverlight.php";s:53:"Piwik\\Plugins\\DevicePlugins\\Columns\\PluginSilverlight";s:66:"/var/www/html/plugins/DevicePlugins/Columns/PluginWindowsMedia.php";s:54:"Piwik\\Plugins\\DevicePlugins\\Columns\\PluginWindowsMedia";}s:60:"PluginHeartbeatColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:55:"PluginIntlColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:62:"PluginMarketplaceColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:71:"PluginProfessionalServicesColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:57:"PluginUserIdColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:1:{s:47:"/var/www/html/plugins/UserId/Columns/UserId.php";s:35:"Piwik\\Plugins\\UserId\\Columns\\UserId";}s:66:"PluginCustomJsTrackerColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:55:"PluginTourColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:66:"PluginPagePerformanceColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:67:"PluginCustomDimensionsColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:72:"PluginJsTrackerInstallCheckColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:63:"PluginFeatureFlagsColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:65:"PluginQueuedTrackingColumns\\Piwik\\Plugin\\Dimension\\VisitDimension";a:0:{}s:59:"PluginCoreVueColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:68:"PluginCorePluginsAdminColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:65:"PluginCoreAdminHomeColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:60:"PluginCoreHomeColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:4:{s:60:"/var/www/html/plugins/CoreHome/Columns/LinkVisitActionId.php";s:48:"Piwik\\Plugins\\CoreHome\\Columns\\LinkVisitActionId";s:65:"/var/www/html/plugins/CoreHome/Columns/LinkVisitActionIdPages.php";s:53:"Piwik\\Plugins\\CoreHome\\Columns\\LinkVisitActionIdPages";s:55:"/var/www/html/plugins/CoreHome/Columns/ServerMinute.php";s:43:"Piwik\\Plugins\\CoreHome\\Columns\\ServerMinute";s:53:"/var/www/html/plugins/CoreHome/Columns/ServerTime.php";s:41:"Piwik\\Plugins\\CoreHome\\Columns\\ServerTime";}s:69:"PluginWebsiteMeasurableColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:70:"PluginIntranetMeasurableColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:63:"PluginDiagnosticsColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:70:"PluginCoreVisualizationsColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:57:"PluginProxyColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:55:"PluginAPIColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:61:"PluginWidgetizeColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:63:"PluginTransitionsColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:68:"PluginLanguagesManagerColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:59:"PluginActionsColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:13:{s:52:"/var/www/html/plugins/Actions/Columns/ActionType.php";s:40:"Piwik\\Plugins\\Actions\\Columns\\ActionType";s:51:"/var/www/html/plugins/Actions/Columns/ActionUrl.php";s:39:"Piwik\\Plugins\\Actions\\Columns\\ActionUrl";s:52:"/var/www/html/plugins/Actions/Columns/ClickedUrl.php";s:40:"Piwik\\Plugins\\Actions\\Columns\\ClickedUrl";s:53:"/var/www/html/plugins/Actions/Columns/DownloadUrl.php";s:41:"Piwik\\Plugins\\Actions\\Columns\\DownloadUrl";s:52:"/var/www/html/plugins/Actions/Columns/IdPageview.php";s:40:"Piwik\\Plugins\\Actions\\Columns\\IdPageview";s:60:"/var/www/html/plugins/Actions/Columns/PageGenerationTime.php";s:48:"Piwik\\Plugins\\Actions\\Columns\\PageGenerationTime";s:51:"/var/www/html/plugins/Actions/Columns/PageTitle.php";s:39:"Piwik\\Plugins\\Actions\\Columns\\PageTitle";s:49:"/var/www/html/plugins/Actions/Columns/PageUrl.php";s:37:"Piwik\\Plugins\\Actions\\Columns\\PageUrl";s:58:"/var/www/html/plugins/Actions/Columns/PageViewPosition.php";s:46:"Piwik\\Plugins\\Actions\\Columns\\PageViewPosition";s:56:"/var/www/html/plugins/Actions/Columns/SearchCategory.php";s:44:"Piwik\\Plugins\\Actions\\Columns\\SearchCategory";s:53:"/var/www/html/plugins/Actions/Columns/SearchCount.php";s:41:"Piwik\\Plugins\\Actions\\Columns\\SearchCount";s:55:"/var/www/html/plugins/Actions/Columns/SearchKeyword.php";s:43:"Piwik\\Plugins\\Actions\\Columns\\SearchKeyword";s:60:"/var/www/html/plugins/Actions/Columns/TimeSpentRefAction.php";s:48:"Piwik\\Plugins\\Actions\\Columns\\TimeSpentRefAction";}s:61:"PluginDashboardColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:62:"PluginMultiSitesColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:61:"PluginReferrersColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:64:"PluginUserLanguageColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:68:"PluginDevicesDetectionColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:57:"PluginGoalsColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:61:"PluginEcommerceColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:8:{s:63:"/var/www/html/plugins/Ecommerce/Columns/ProductViewCategory.php";s:51:"Piwik\\Plugins\\Ecommerce\\Columns\\ProductViewCategory";s:64:"/var/www/html/plugins/Ecommerce/Columns/ProductViewCategory2.php";s:52:"Piwik\\Plugins\\Ecommerce\\Columns\\ProductViewCategory2";s:64:"/var/www/html/plugins/Ecommerce/Columns/ProductViewCategory3.php";s:52:"Piwik\\Plugins\\Ecommerce\\Columns\\ProductViewCategory3";s:64:"/var/www/html/plugins/Ecommerce/Columns/ProductViewCategory4.php";s:52:"Piwik\\Plugins\\Ecommerce\\Columns\\ProductViewCategory4";s:64:"/var/www/html/plugins/Ecommerce/Columns/ProductViewCategory5.php";s:52:"Piwik\\Plugins\\Ecommerce\\Columns\\ProductViewCategory5";s:59:"/var/www/html/plugins/Ecommerce/Columns/ProductViewName.php";s:47:"Piwik\\Plugins\\Ecommerce\\Columns\\ProductViewName";s:60:"/var/www/html/plugins/Ecommerce/Columns/ProductViewPrice.php";s:48:"Piwik\\Plugins\\Ecommerce\\Columns\\ProductViewPrice";s:58:"/var/www/html/plugins/Ecommerce/Columns/ProductViewSku.php";s:46:"Piwik\\Plugins\\Ecommerce\\Columns\\ProductViewSku";}s:55:"PluginSEOColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:58:"PluginEventsColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:5:{s:52:"/var/www/html/plugins/Events/Columns/EventAction.php";s:40:"Piwik\\Plugins\\Events\\Columns\\EventAction";s:54:"/var/www/html/plugins/Events/Columns/EventCategory.php";s:42:"Piwik\\Plugins\\Events\\Columns\\EventCategory";s:50:"/var/www/html/plugins/Events/Columns/EventName.php";s:38:"Piwik\\Plugins\\Events\\Columns\\EventName";s:49:"/var/www/html/plugins/Events/Columns/EventUrl.php";s:37:"Piwik\\Plugins\\Events\\Columns\\EventUrl";s:51:"/var/www/html/plugins/Events/Columns/EventValue.php";s:39:"Piwik\\Plugins\\Events\\Columns\\EventValue";}s:63:"PluginUserCountryColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:58:"PluginGeoIp2Columns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:65:"PluginVisitsSummaryColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:66:"PluginVisitFrequencyColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:61:"PluginVisitTimeColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:67:"PluginVisitorInterestColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:61:"PluginRssWidgetColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:60:"PluginFeedbackColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:59:"PluginMonologColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:57:"PluginLoginColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:65:"PluginTwoFactorAuthColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:64:"PluginUsersManagerColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:64:"PluginSitesManagerColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:64:"PluginInstallationColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:63:"PluginCoreUpdaterColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:63:"PluginCoreConsoleColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:68:"PluginScheduledReportsColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:66:"PluginUserCountryMapColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:56:"PluginLiveColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:66:"PluginPrivacyManagerColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:62:"PluginImageGraphColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:63:"PluginAnnotationsColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:67:"PluginMobileMessagingColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:59:"PluginOverlayColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:65:"PluginSegmentEditorColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:60:"PluginInsightsColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:60:"PluginMorpheusColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:60:"PluginContentsColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:4:{s:61:"/var/www/html/plugins/Contents/Columns/ContentInteraction.php";s:49:"Piwik\\Plugins\\Contents\\Columns\\ContentInteraction";s:54:"/var/www/html/plugins/Contents/Columns/ContentName.php";s:42:"Piwik\\Plugins\\Contents\\Columns\\ContentName";s:55:"/var/www/html/plugins/Contents/Columns/ContentPiece.php";s:43:"Piwik\\Plugins\\Contents\\Columns\\ContentPiece";s:56:"/var/www/html/plugins/Contents/Columns/ContentTarget.php";s:44:"Piwik\\Plugins\\Contents\\Columns\\ContentTarget";}s:64:"PluginBulkTrackingColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:62:"PluginResolutionColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:65:"PluginDevicePluginsColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:61:"PluginHeartbeatColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:56:"PluginIntlColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:63:"PluginMarketplaceColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:72:"PluginProfessionalServicesColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:58:"PluginUserIdColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:67:"PluginCustomJsTrackerColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:56:"PluginTourColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:67:"PluginPagePerformanceColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:6:{s:67:"/var/www/html/plugins/PagePerformance/Columns/TimeDomCompletion.php";s:55:"Piwik\\Plugins\\PagePerformance\\Columns\\TimeDomCompletion";s:67:"/var/www/html/plugins/PagePerformance/Columns/TimeDomProcessing.php";s:55:"Piwik\\Plugins\\PagePerformance\\Columns\\TimeDomProcessing";s:61:"/var/www/html/plugins/PagePerformance/Columns/TimeNetwork.php";s:49:"Piwik\\Plugins\\PagePerformance\\Columns\\TimeNetwork";s:60:"/var/www/html/plugins/PagePerformance/Columns/TimeOnLoad.php";s:48:"Piwik\\Plugins\\PagePerformance\\Columns\\TimeOnLoad";s:60:"/var/www/html/plugins/PagePerformance/Columns/TimeServer.php";s:48:"Piwik\\Plugins\\PagePerformance\\Columns\\TimeServer";s:62:"/var/www/html/plugins/PagePerformance/Columns/TimeTransfer.php";s:50:"Piwik\\Plugins\\PagePerformance\\Columns\\TimeTransfer";}s:68:"PluginCustomDimensionsColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:73:"PluginJsTrackerInstallCheckColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:64:"PluginFeatureFlagsColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:66:"PluginQueuedTrackingColumns\\Piwik\\Plugin\\Dimension\\ActionDimension";a:0:{}s:63:"PluginCoreVueColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:72:"PluginCorePluginsAdminColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:69:"PluginCoreAdminHomeColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:64:"PluginCoreHomeColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:73:"PluginWebsiteMeasurableColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:74:"PluginIntranetMeasurableColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:67:"PluginDiagnosticsColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:74:"PluginCoreVisualizationsColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:61:"PluginProxyColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:59:"PluginAPIColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:65:"PluginWidgetizeColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:67:"PluginTransitionsColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:72:"PluginLanguagesManagerColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:63:"PluginActionsColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:65:"PluginDashboardColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:66:"PluginMultiSitesColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:65:"PluginReferrersColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:68:"PluginUserLanguageColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:72:"PluginDevicesDetectionColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:61:"PluginGoalsColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:4:{s:48:"/var/www/html/plugins/Goals/Columns/GoalName.php";s:36:"Piwik\\Plugins\\Goals\\Columns\\GoalName";s:46:"/var/www/html/plugins/Goals/Columns/IdGoal.php";s:34:"Piwik\\Plugins\\Goals\\Columns\\IdGoal";s:55:"/var/www/html/plugins/Goals/Columns/PageviewsBefore.php";s:43:"Piwik\\Plugins\\Goals\\Columns\\PageviewsBefore";s:47:"/var/www/html/plugins/Goals/Columns/Revenue.php";s:35:"Piwik\\Plugins\\Goals\\Columns\\Revenue";}s:65:"PluginEcommerceColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:7:{s:49:"/var/www/html/plugins/Ecommerce/Columns/Items.php";s:37:"Piwik\\Plugins\\Ecommerce\\Columns\\Items";s:49:"/var/www/html/plugins/Ecommerce/Columns/Order.php";s:37:"Piwik\\Plugins\\Ecommerce\\Columns\\Order";s:51:"/var/www/html/plugins/Ecommerce/Columns/Revenue.php";s:39:"Piwik\\Plugins\\Ecommerce\\Columns\\Revenue";s:59:"/var/www/html/plugins/Ecommerce/Columns/RevenueDiscount.php";s:47:"Piwik\\Plugins\\Ecommerce\\Columns\\RevenueDiscount";s:59:"/var/www/html/plugins/Ecommerce/Columns/RevenueShipping.php";s:47:"Piwik\\Plugins\\Ecommerce\\Columns\\RevenueShipping";s:59:"/var/www/html/plugins/Ecommerce/Columns/RevenueSubtotal.php";s:47:"Piwik\\Plugins\\Ecommerce\\Columns\\RevenueSubtotal";s:54:"/var/www/html/plugins/Ecommerce/Columns/RevenueTax.php";s:42:"Piwik\\Plugins\\Ecommerce\\Columns\\RevenueTax";}s:59:"PluginSEOColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:62:"PluginEventsColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:67:"PluginUserCountryColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:62:"PluginGeoIp2Columns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:69:"PluginVisitsSummaryColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:70:"PluginVisitFrequencyColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:65:"PluginVisitTimeColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:71:"PluginVisitorInterestColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:65:"PluginRssWidgetColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:64:"PluginFeedbackColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:63:"PluginMonologColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:61:"PluginLoginColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:69:"PluginTwoFactorAuthColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:68:"PluginUsersManagerColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:68:"PluginSitesManagerColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:68:"PluginInstallationColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:67:"PluginCoreUpdaterColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:67:"PluginCoreConsoleColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:72:"PluginScheduledReportsColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:70:"PluginUserCountryMapColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:60:"PluginLiveColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:70:"PluginPrivacyManagerColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:66:"PluginImageGraphColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:67:"PluginAnnotationsColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:71:"PluginMobileMessagingColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:63:"PluginOverlayColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:69:"PluginSegmentEditorColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:64:"PluginInsightsColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:64:"PluginMorpheusColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:64:"PluginContentsColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:68:"PluginBulkTrackingColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:66:"PluginResolutionColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:69:"PluginDevicePluginsColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:65:"PluginHeartbeatColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:60:"PluginIntlColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:67:"PluginMarketplaceColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:76:"PluginProfessionalServicesColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:62:"PluginUserIdColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:71:"PluginCustomJsTrackerColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:60:"PluginTourColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:71:"PluginPagePerformanceColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:72:"PluginCustomDimensionsColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:77:"PluginJsTrackerInstallCheckColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:68:"PluginFeatureFlagsColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:70:"PluginQueuedTrackingColumns\\Piwik\\Plugin\\Dimension\\ConversionDimension";a:0:{}s:22:"AllDimensionModifyTime";a:198:{s:52:"/var/www/html/plugins/Actions/Columns/ActionType.php";i:1747737682;s:51:"/var/www/html/plugins/Actions/Columns/ActionUrl.php";i:1747737682;s:52:"/var/www/html/plugins/Actions/Columns/ClickedUrl.php";i:1747737682;s:57:"/var/www/html/plugins/Actions/Columns/DestinationPage.php";i:1747737682;s:53:"/var/www/html/plugins/Actions/Columns/DownloadUrl.php";i:1747737682;s:56:"/var/www/html/plugins/Actions/Columns/EntryPageTitle.php";i:1747737682;s:54:"/var/www/html/plugins/Actions/Columns/EntryPageUrl.php";i:1747737682;s:55:"/var/www/html/plugins/Actions/Columns/ExitPageTitle.php";i:1747737682;s:53:"/var/www/html/plugins/Actions/Columns/ExitPageUrl.php";i:1747737682;s:52:"/var/www/html/plugins/Actions/Columns/IdPageview.php";i:1747737682;s:49:"/var/www/html/plugins/Actions/Columns/Keyword.php";i:1747737682;s:67:"/var/www/html/plugins/Actions/Columns/KeywordwithNoSearchResult.php";i:1747737682;s:75:"/var/www/html/plugins/Actions/Columns/Metrics/AveragePageGenerationTime.php";i:1747737682;s:67:"/var/www/html/plugins/Actions/Columns/Metrics/AverageTimeOnPage.php";i:1747737682;s:60:"/var/www/html/plugins/Actions/Columns/Metrics/BounceRate.php";i:1747737682;s:58:"/var/www/html/plugins/Actions/Columns/Metrics/ExitRate.php";i:1747737682;s:60:"/var/www/html/plugins/Actions/Columns/PageGenerationTime.php";i:1747737682;s:51:"/var/www/html/plugins/Actions/Columns/PageTitle.php";i:1747737682;s:49:"/var/www/html/plugins/Actions/Columns/PageUrl.php";i:1747737682;s:58:"/var/www/html/plugins/Actions/Columns/PageViewPosition.php";i:1747737682;s:56:"/var/www/html/plugins/Actions/Columns/SearchCategory.php";i:1747737682;s:53:"/var/www/html/plugins/Actions/Columns/SearchCount.php";i:1747737682;s:63:"/var/www/html/plugins/Actions/Columns/SearchDestinationPage.php";i:1747737682;s:55:"/var/www/html/plugins/Actions/Columns/SearchKeyword.php";i:1747737682;s:63:"/var/www/html/plugins/Actions/Columns/SearchNoResultKeyword.php";i:1747737682;s:60:"/var/www/html/plugins/Actions/Columns/TimeSpentRefAction.php";i:1747737682;s:59:"/var/www/html/plugins/Actions/Columns/VisitTotalActions.php";i:1747737682;s:64:"/var/www/html/plugins/Actions/Columns/VisitTotalInteractions.php";i:1747737682;s:60:"/var/www/html/plugins/Actions/Columns/VisitTotalSearches.php";i:1747737682;s:61:"/var/www/html/plugins/Contents/Columns/ContentInteraction.php";i:1747737682;s:54:"/var/www/html/plugins/Contents/Columns/ContentName.php";i:1747737682;s:55:"/var/www/html/plugins/Contents/Columns/ContentPiece.php";i:1747737682;s:56:"/var/www/html/plugins/Contents/Columns/ContentTarget.php";i:1747737682;s:66:"/var/www/html/plugins/Contents/Columns/Metrics/InteractionRate.php";i:1747737682;s:49:"/var/www/html/plugins/CoreHome/Columns/IdSite.php";i:1747737681;s:60:"/var/www/html/plugins/CoreHome/Columns/LinkVisitActionId.php";i:1747737681;s:65:"/var/www/html/plugins/CoreHome/Columns/LinkVisitActionIdPages.php";i:1747737681;s:66:"/var/www/html/plugins/CoreHome/Columns/Metrics/ActionsPerVisit.php";i:1747737681;s:68:"/var/www/html/plugins/CoreHome/Columns/Metrics/AverageTimeOnSite.php";i:1747737681;s:61:"/var/www/html/plugins/CoreHome/Columns/Metrics/BounceRate.php";i:1747737681;s:74:"/var/www/html/plugins/CoreHome/Columns/Metrics/CallableProcessedMetric.php";i:1747737681;s:65:"/var/www/html/plugins/CoreHome/Columns/Metrics/ConversionRate.php";i:1747737681;s:66:"/var/www/html/plugins/CoreHome/Columns/Metrics/EvolutionMetric.php";i:1747737681;s:64:"/var/www/html/plugins/CoreHome/Columns/Metrics/VisitsPercent.php";i:1747737681;s:53:"/var/www/html/plugins/CoreHome/Columns/Profilable.php";i:1747737681;s:55:"/var/www/html/plugins/CoreHome/Columns/ServerMinute.php";i:1747737681;s:53:"/var/www/html/plugins/CoreHome/Columns/ServerTime.php";i:1747737681;s:49:"/var/www/html/plugins/CoreHome/Columns/UserId.php";i:1747737681;s:65:"/var/www/html/plugins/CoreHome/Columns/VisitFirstActionMinute.php";i:1747737681;s:63:"/var/www/html/plugins/CoreHome/Columns/VisitFirstActionTime.php";i:1747737681;s:57:"/var/www/html/plugins/CoreHome/Columns/VisitGoalBuyer.php";i:1747737681;s:61:"/var/www/html/plugins/CoreHome/Columns/VisitGoalConverted.php";i:1747737681;s:50:"/var/www/html/plugins/CoreHome/Columns/VisitId.php";i:1747737681;s:50:"/var/www/html/plugins/CoreHome/Columns/VisitIp.php";i:1747737681;s:62:"/var/www/html/plugins/CoreHome/Columns/VisitLastActionDate.php";i:1747737681;s:68:"/var/www/html/plugins/CoreHome/Columns/VisitLastActionDayOfMonth.php";i:1747737681;s:67:"/var/www/html/plugins/CoreHome/Columns/VisitLastActionDayOfWeek.php";i:1747737681;s:67:"/var/www/html/plugins/CoreHome/Columns/VisitLastActionDayOfYear.php";i:1747737681;s:64:"/var/www/html/plugins/CoreHome/Columns/VisitLastActionMinute.php";i:1747737681;s:63:"/var/www/html/plugins/CoreHome/Columns/VisitLastActionMonth.php";i:1747737681;s:65:"/var/www/html/plugins/CoreHome/Columns/VisitLastActionQuarter.php";i:1747737681;s:64:"/var/www/html/plugins/CoreHome/Columns/VisitLastActionSecond.php";i:1747737681;s:62:"/var/www/html/plugins/CoreHome/Columns/VisitLastActionTime.php";i:1747737681;s:68:"/var/www/html/plugins/CoreHome/Columns/VisitLastActionWeekOfYear.php";i:1747737681;s:62:"/var/www/html/plugins/CoreHome/Columns/VisitLastActionYear.php";i:1747737681;s:57:"/var/www/html/plugins/CoreHome/Columns/VisitTotalTime.php";i:1747737681;s:64:"/var/www/html/plugins/CoreHome/Columns/VisitorDaysSinceFirst.php";i:1747737681;s:64:"/var/www/html/plugins/CoreHome/Columns/VisitorDaysSinceOrder.php";i:1747737681;s:61:"/var/www/html/plugins/CoreHome/Columns/VisitorFingerprint.php";i:1747737681;s:52:"/var/www/html/plugins/CoreHome/Columns/VisitorId.php";i:1747737681;s:59:"/var/www/html/plugins/CoreHome/Columns/VisitorReturning.php";i:1747737681;s:67:"/var/www/html/plugins/CoreHome/Columns/VisitorSecondsSinceFirst.php";i:1747737681;s:67:"/var/www/html/plugins/CoreHome/Columns/VisitorSecondsSinceOrder.php";i:1747737681;s:54:"/var/www/html/plugins/CoreHome/Columns/VisitsCount.php";i:1747737681;s:81:"/var/www/html/plugins/CustomDimensions/Columns/Metrics/AverageTimeOnDimension.php";i:1747737682;s:66:"/var/www/html/plugins/DevicePlugins/Columns/DevicePluginColumn.php";i:1747737682;s:54:"/var/www/html/plugins/DevicePlugins/Columns/Plugin.php";i:1747737682;s:60:"/var/www/html/plugins/DevicePlugins/Columns/PluginCookie.php";i:1747737682;s:59:"/var/www/html/plugins/DevicePlugins/Columns/PluginFlash.php";i:1747737682;s:58:"/var/www/html/plugins/DevicePlugins/Columns/PluginJava.php";i:1747737682;s:57:"/var/www/html/plugins/DevicePlugins/Columns/PluginPdf.php";i:1747737682;s:63:"/var/www/html/plugins/DevicePlugins/Columns/PluginQuickTime.php";i:1747737682;s:64:"/var/www/html/plugins/DevicePlugins/Columns/PluginRealPlayer.php";i:1747737682;s:65:"/var/www/html/plugins/DevicePlugins/Columns/PluginSilverlight.php";i:1747737682;s:66:"/var/www/html/plugins/DevicePlugins/Columns/PluginWindowsMedia.php";i:1747737682;s:55:"/var/www/html/plugins/DevicesDetection/Columns/Base.php";i:1747737683;s:64:"/var/www/html/plugins/DevicesDetection/Columns/BrowserEngine.php";i:1747737683;s:62:"/var/www/html/plugins/DevicesDetection/Columns/BrowserName.php";i:1747737683;s:65:"/var/www/html/plugins/DevicesDetection/Columns/BrowserVersion.php";i:1747737683;s:61:"/var/www/html/plugins/DevicesDetection/Columns/ClientType.php";i:1747737683;s:62:"/var/www/html/plugins/DevicesDetection/Columns/DeviceBrand.php";i:1747737683;s:62:"/var/www/html/plugins/DevicesDetection/Columns/DeviceModel.php";i:1747737683;s:61:"/var/www/html/plugins/DevicesDetection/Columns/DeviceType.php";i:1747737683;s:53:"/var/www/html/plugins/DevicesDetection/Columns/Os.php";i:1747737683;s:60:"/var/www/html/plugins/DevicesDetection/Columns/OsVersion.php";i:1747737683;s:58:"/var/www/html/plugins/Ecommerce/Columns/BaseConversion.php";i:1747737683;s:49:"/var/www/html/plugins/Ecommerce/Columns/Items.php";i:1747737683;s:49:"/var/www/html/plugins/Ecommerce/Columns/Order.php";i:1747737683;s:59:"/var/www/html/plugins/Ecommerce/Columns/ProductCategory.php";i:1747737683;s:55:"/var/www/html/plugins/Ecommerce/Columns/ProductName.php";i:1747737683;s:56:"/var/www/html/plugins/Ecommerce/Columns/ProductPrice.php";i:1747737683;s:59:"/var/www/html/plugins/Ecommerce/Columns/ProductQuantity.php";i:1747737683;s:54:"/var/www/html/plugins/Ecommerce/Columns/ProductSku.php";i:1747737683;s:63:"/var/www/html/plugins/Ecommerce/Columns/ProductViewCategory.php";i:1747737683;s:64:"/var/www/html/plugins/Ecommerce/Columns/ProductViewCategory2.php";i:1747737683;s:64:"/var/www/html/plugins/Ecommerce/Columns/ProductViewCategory3.php";i:1747737683;s:64:"/var/www/html/plugins/Ecommerce/Columns/ProductViewCategory4.php";i:1747737683;s:64:"/var/www/html/plugins/Ecommerce/Columns/ProductViewCategory5.php";i:1747737683;s:59:"/var/www/html/plugins/Ecommerce/Columns/ProductViewName.php";i:1747737683;s:60:"/var/www/html/plugins/Ecommerce/Columns/ProductViewPrice.php";i:1747737683;s:58:"/var/www/html/plugins/Ecommerce/Columns/ProductViewSku.php";i:1747737683;s:51:"/var/www/html/plugins/Ecommerce/Columns/Revenue.php";i:1747737683;s:59:"/var/www/html/plugins/Ecommerce/Columns/RevenueDiscount.php";i:1747737683;s:59:"/var/www/html/plugins/Ecommerce/Columns/RevenueShipping.php";i:1747737683;s:59:"/var/www/html/plugins/Ecommerce/Columns/RevenueSubtotal.php";i:1747737683;s:54:"/var/www/html/plugins/Ecommerce/Columns/RevenueTax.php";i:1747737683;s:52:"/var/www/html/plugins/Events/Columns/EventAction.php";i:1747737683;s:54:"/var/www/html/plugins/Events/Columns/EventCategory.php";i:1747737683;s:50:"/var/www/html/plugins/Events/Columns/EventName.php";i:1747737683;s:49:"/var/www/html/plugins/Events/Columns/EventUrl.php";i:1747737683;s:51:"/var/www/html/plugins/Events/Columns/EventValue.php";i:1747737683;s:66:"/var/www/html/plugins/Events/Columns/Metrics/AverageEventValue.php";i:1747737683;s:52:"/var/www/html/plugins/Events/Columns/TotalEvents.php";i:1747737683;s:56:"/var/www/html/plugins/Goals/Columns/DaysToConversion.php";i:1747737682;s:48:"/var/www/html/plugins/Goals/Columns/GoalName.php";i:1747737682;s:46:"/var/www/html/plugins/Goals/Columns/IdGoal.php";i:1747737682;s:67:"/var/www/html/plugins/Goals/Columns/Metrics/AverageOrderRevenue.php";i:1747737682;s:60:"/var/www/html/plugins/Goals/Columns/Metrics/AveragePrice.php";i:1747737682;s:63:"/var/www/html/plugins/Goals/Columns/Metrics/AverageQuantity.php";i:1747737682;s:66:"/var/www/html/plugins/Goals/Columns/Metrics/GoalConversionRate.php";i:1747737682;s:80:"/var/www/html/plugins/Goals/Columns/Metrics/GoalSpecific/AverageOrderRevenue.php";i:1747737682;s:80:"/var/www/html/plugins/Goals/Columns/Metrics/GoalSpecific/ConversionEntryRate.php";i:1747737682;s:79:"/var/www/html/plugins/Goals/Columns/Metrics/GoalSpecific/ConversionPageRate.php";i:1747737682;s:75:"/var/www/html/plugins/Goals/Columns/Metrics/GoalSpecific/ConversionRate.php";i:1747737682;s:72:"/var/www/html/plugins/Goals/Columns/Metrics/GoalSpecific/Conversions.php";i:1747737682;s:78:"/var/www/html/plugins/Goals/Columns/Metrics/GoalSpecific/ConversionsAttrib.php";i:1747737682;s:77:"/var/www/html/plugins/Goals/Columns/Metrics/GoalSpecific/ConversionsEntry.php";i:1747737682;s:71:"/var/www/html/plugins/Goals/Columns/Metrics/GoalSpecific/ItemsCount.php";i:1747737682;s:68:"/var/www/html/plugins/Goals/Columns/Metrics/GoalSpecific/Revenue.php";i:1747737682;s:74:"/var/www/html/plugins/Goals/Columns/Metrics/GoalSpecific/RevenueAttrib.php";i:1747737682;s:73:"/var/www/html/plugins/Goals/Columns/Metrics/GoalSpecific/RevenueEntry.php";i:1747737682;s:76:"/var/www/html/plugins/Goals/Columns/Metrics/GoalSpecific/RevenuePerEntry.php";i:1747737682;s:76:"/var/www/html/plugins/Goals/Columns/Metrics/GoalSpecific/RevenuePerVisit.php";i:1747737682;s:75:"/var/www/html/plugins/Goals/Columns/Metrics/GoalSpecificProcessedMetric.php";i:1747737682;s:69:"/var/www/html/plugins/Goals/Columns/Metrics/ProductConversionRate.php";i:1747737682;s:63:"/var/www/html/plugins/Goals/Columns/Metrics/RevenuePerVisit.php";i:1747737682;s:55:"/var/www/html/plugins/Goals/Columns/PageviewsBefore.php";i:1747737682;s:47:"/var/www/html/plugins/Goals/Columns/Revenue.php";i:1747737682;s:61:"/var/www/html/plugins/Goals/Columns/VisitsUntilConversion.php";i:1747737682;s:81:"/var/www/html/plugins/MultiSites/Columns/Metrics/EcommerceOnlyEvolutionMetric.php";i:1747737682;s:52:"/var/www/html/plugins/MultiSites/Columns/Website.php";i:1747737682;s:54:"/var/www/html/plugins/PagePerformance/Columns/Base.php";i:1747737682;s:77:"/var/www/html/plugins/PagePerformance/Columns/Metrics/AveragePageLoadTime.php";i:1747737682;s:82:"/var/www/html/plugins/PagePerformance/Columns/Metrics/AveragePerformanceMetric.php";i:1747737682;s:82:"/var/www/html/plugins/PagePerformance/Columns/Metrics/AverageTimeDomCompletion.php";i:1747737682;s:82:"/var/www/html/plugins/PagePerformance/Columns/Metrics/AverageTimeDomProcessing.php";i:1747737682;s:76:"/var/www/html/plugins/PagePerformance/Columns/Metrics/AverageTimeNetwork.php";i:1747737682;s:75:"/var/www/html/plugins/PagePerformance/Columns/Metrics/AverageTimeOnLoad.php";i:1747737682;s:75:"/var/www/html/plugins/PagePerformance/Columns/Metrics/AverageTimeServer.php";i:1747737682;s:77:"/var/www/html/plugins/PagePerformance/Columns/Metrics/AverageTimeTransfer.php";i:1747737682;s:67:"/var/www/html/plugins/PagePerformance/Columns/TimeDomCompletion.php";i:1747737682;s:67:"/var/www/html/plugins/PagePerformance/Columns/TimeDomProcessing.php";i:1747737682;s:61:"/var/www/html/plugins/PagePerformance/Columns/TimeNetwork.php";i:1747737682;s:60:"/var/www/html/plugins/PagePerformance/Columns/TimeOnLoad.php";i:1747737682;s:60:"/var/www/html/plugins/PagePerformance/Columns/TimeServer.php";i:1747737682;s:62:"/var/www/html/plugins/PagePerformance/Columns/TimeTransfer.php";i:1747737682;s:48:"/var/www/html/plugins/Referrers/Columns/Base.php";i:1747737683;s:52:"/var/www/html/plugins/Referrers/Columns/Campaign.php";i:1747737683;s:51:"/var/www/html/plugins/Referrers/Columns/Keyword.php";i:1747737683;s:79:"/var/www/html/plugins/Referrers/Columns/Metrics/VisitorsFromReferrerPercent.php";i:1747737683;s:52:"/var/www/html/plugins/Referrers/Columns/Referrer.php";i:1747737683;s:56:"/var/www/html/plugins/Referrers/Columns/ReferrerName.php";i:1747737683;s:56:"/var/www/html/plugins/Referrers/Columns/ReferrerType.php";i:1747737683;s:55:"/var/www/html/plugins/Referrers/Columns/ReferrerUrl.php";i:1747737683;s:56:"/var/www/html/plugins/Referrers/Columns/SearchEngine.php";i:1747737683;s:57:"/var/www/html/plugins/Referrers/Columns/SocialNetwork.php";i:1747737683;s:51:"/var/www/html/plugins/Referrers/Columns/Website.php";i:1747737683;s:55:"/var/www/html/plugins/Referrers/Columns/WebsitePage.php";i:1747737683;s:58:"/var/www/html/plugins/Resolution/Columns/Configuration.php";i:1747737682;s:55:"/var/www/html/plugins/Resolution/Columns/Resolution.php";i:1747737682;s:50:"/var/www/html/plugins/UserCountry/Columns/Base.php";i:1747737683;s:50:"/var/www/html/plugins/UserCountry/Columns/City.php";i:1747737683;s:55:"/var/www/html/plugins/UserCountry/Columns/Continent.php";i:1747737683;s:53:"/var/www/html/plugins/UserCountry/Columns/Country.php";i:1747737683;s:54:"/var/www/html/plugins/UserCountry/Columns/Latitude.php";i:1747737683;s:55:"/var/www/html/plugins/UserCountry/Columns/Longitude.php";i:1747737683;s:52:"/var/www/html/plugins/UserCountry/Columns/Region.php";i:1747737683;s:47:"/var/www/html/plugins/UserId/Columns/UserId.php";i:1747737683;s:55:"/var/www/html/plugins/UserLanguage/Columns/Language.php";i:1747737683;s:72:"/var/www/html/plugins/VisitFrequency/Columns/Metrics/ReturningMetric.php";i:1747737682;s:56:"/var/www/html/plugins/VisitTime/Columns/DayOfTheWeek.php";i:1747737683;s:55:"/var/www/html/plugins/VisitTime/Columns/LocalMinute.php";i:1747737683;s:53:"/var/www/html/plugins/VisitTime/Columns/LocalTime.php";i:1747737683;s:63:"/var/www/html/plugins/VisitorInterest/Columns/PagesPerVisit.php";i:1747737682;s:63:"/var/www/html/plugins/VisitorInterest/Columns/VisitDuration.php";i:1747737682;s:70:"/var/www/html/plugins/VisitorInterest/Columns/VisitorDaysSinceLast.php";i:1747737682;s:73:"/var/www/html/plugins/VisitorInterest/Columns/VisitorSecondsSinceLast.php";i:1747737682;s:69:"/var/www/html/plugins/VisitorInterest/Columns/VisitsbyVisitNumber.php";i:1747737682;}s:59:"PluginCoreVueReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:68:"PluginCorePluginsAdminReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:65:"PluginCoreAdminHomeReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:60:"PluginCoreHomeReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:69:"PluginWebsiteMeasurableReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:70:"PluginIntranetMeasurableReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:63:"PluginDiagnosticsReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:70:"PluginCoreVisualizationsReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:57:"PluginProxyReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:55:"PluginAPIReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:61:"PluginWidgetizeReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:63:"PluginTransitionsReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:68:"PluginLanguagesManagerReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:59:"PluginActionsReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:61:"PluginDashboardReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:62:"PluginMultiSitesReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:61:"PluginReferrersReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:64:"PluginUserLanguageReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:68:"PluginDevicesDetectionReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:57:"PluginGoalsReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:61:"PluginEcommerceReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:55:"PluginSEOReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:58:"PluginEventsReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:63:"PluginUserCountryReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:58:"PluginGeoIp2ReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:65:"PluginVisitsSummaryReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:66:"PluginVisitFrequencyReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:61:"PluginVisitTimeReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:67:"PluginVisitorInterestReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:61:"PluginRssWidgetReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:60:"PluginFeedbackReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:59:"PluginMonologReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:57:"PluginLoginReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:65:"PluginTwoFactorAuthReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:64:"PluginUsersManagerReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:64:"PluginSitesManagerReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:64:"PluginInstallationReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:63:"PluginCoreUpdaterReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:5:{s:63:"/var/www/html/plugins/CoreUpdater/ReleaseChannel/LatestBeta.php";s:51:"Piwik\\Plugins\\CoreUpdater\\ReleaseChannel\\LatestBeta";s:70:"/var/www/html/plugins/CoreUpdater/ReleaseChannel/LatestCurrentBeta.php";s:58:"Piwik\\Plugins\\CoreUpdater\\ReleaseChannel\\LatestCurrentBeta";s:72:"/var/www/html/plugins/CoreUpdater/ReleaseChannel/LatestCurrentStable.php";s:60:"Piwik\\Plugins\\CoreUpdater\\ReleaseChannel\\LatestCurrentStable";s:66:"/var/www/html/plugins/CoreUpdater/ReleaseChannel/LatestPreview.php";s:54:"Piwik\\Plugins\\CoreUpdater\\ReleaseChannel\\LatestPreview";s:65:"/var/www/html/plugins/CoreUpdater/ReleaseChannel/LatestStable.php";s:53:"Piwik\\Plugins\\CoreUpdater\\ReleaseChannel\\LatestStable";}s:63:"PluginCoreConsoleReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:68:"PluginScheduledReportsReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:66:"PluginUserCountryMapReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:56:"PluginLiveReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:66:"PluginPrivacyManagerReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:62:"PluginImageGraphReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:63:"PluginAnnotationsReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:67:"PluginMobileMessagingReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:59:"PluginOverlayReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:65:"PluginSegmentEditorReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:60:"PluginInsightsReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:60:"PluginMorpheusReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:60:"PluginContentsReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:64:"PluginBulkTrackingReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:62:"PluginResolutionReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:65:"PluginDevicePluginsReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:61:"PluginHeartbeatReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:56:"PluginIntlReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:63:"PluginMarketplaceReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:72:"PluginProfessionalServicesReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:58:"PluginUserIdReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:67:"PluginCustomJsTrackerReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:56:"PluginTourReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:67:"PluginPagePerformanceReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:68:"PluginCustomDimensionsReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:73:"PluginJsTrackerInstallCheckReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:64:"PluginFeatureFlagsReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:66:"PluginQueuedTrackingReleaseChannelPiwik\\UpdateCheck\\ReleaseChannel";a:0:{}s:42:"PluginCoreVueRendererPiwik\\API\\ApiRenderer";a:0:{}s:51:"PluginCorePluginsAdminRendererPiwik\\API\\ApiRenderer";a:0:{}s:48:"PluginCoreAdminHomeRendererPiwik\\API\\ApiRenderer";a:0:{}s:43:"PluginCoreHomeRendererPiwik\\API\\ApiRenderer";a:0:{}s:52:"PluginWebsiteMeasurableRendererPiwik\\API\\ApiRenderer";a:0:{}s:53:"PluginIntranetMeasurableRendererPiwik\\API\\ApiRenderer";a:0:{}s:46:"PluginDiagnosticsRendererPiwik\\API\\ApiRenderer";a:0:{}s:53:"PluginCoreVisualizationsRendererPiwik\\API\\ApiRenderer";a:0:{}s:40:"PluginProxyRendererPiwik\\API\\ApiRenderer";a:0:{}s:38:"PluginAPIRendererPiwik\\API\\ApiRenderer";a:8:{s:46:"/var/www/html/plugins/API/Renderer/Console.php";s:34:"Piwik\\Plugins\\API\\Renderer\\Console";s:42:"/var/www/html/plugins/API/Renderer/Csv.php";s:30:"Piwik\\Plugins\\API\\Renderer\\Csv";s:43:"/var/www/html/plugins/API/Renderer/Html.php";s:31:"Piwik\\Plugins\\API\\Renderer\\Html";s:43:"/var/www/html/plugins/API/Renderer/Json.php";s:31:"Piwik\\Plugins\\API\\Renderer\\Json";s:47:"/var/www/html/plugins/API/Renderer/Original.php";s:35:"Piwik\\Plugins\\API\\Renderer\\Original";s:42:"/var/www/html/plugins/API/Renderer/Rss.php";s:30:"Piwik\\Plugins\\API\\Renderer\\Rss";s:42:"/var/www/html/plugins/API/Renderer/Tsv.php";s:30:"Piwik\\Plugins\\API\\Renderer\\Tsv";s:42:"/var/www/html/plugins/API/Renderer/Xml.php";s:30:"Piwik\\Plugins\\API\\Renderer\\Xml";}s:44:"PluginWidgetizeRendererPiwik\\API\\ApiRenderer";a:0:{}s:46:"PluginTransitionsRendererPiwik\\API\\ApiRenderer";a:0:{}s:51:"PluginLanguagesManagerRendererPiwik\\API\\ApiRenderer";a:0:{}s:42:"PluginActionsRendererPiwik\\API\\ApiRenderer";a:0:{}s:44:"PluginDashboardRendererPiwik\\API\\ApiRenderer";a:0:{}s:45:"PluginMultiSitesRendererPiwik\\API\\ApiRenderer";a:0:{}s:44:"PluginReferrersRendererPiwik\\API\\ApiRenderer";a:0:{}s:47:"PluginUserLanguageRendererPiwik\\API\\ApiRenderer";a:0:{}s:51:"PluginDevicesDetectionRendererPiwik\\API\\ApiRenderer";a:0:{}s:40:"PluginGoalsRendererPiwik\\API\\ApiRenderer";a:0:{}s:44:"PluginEcommerceRendererPiwik\\API\\ApiRenderer";a:0:{}s:38:"PluginSEORendererPiwik\\API\\ApiRenderer";a:0:{}s:41:"PluginEventsRendererPiwik\\API\\ApiRenderer";a:0:{}s:46:"PluginUserCountryRendererPiwik\\API\\ApiRenderer";a:0:{}s:41:"PluginGeoIp2RendererPiwik\\API\\ApiRenderer";a:0:{}s:48:"PluginVisitsSummaryRendererPiwik\\API\\ApiRenderer";a:0:{}s:49:"PluginVisitFrequencyRendererPiwik\\API\\ApiRenderer";a:0:{}s:44:"PluginVisitTimeRendererPiwik\\API\\ApiRenderer";a:0:{}s:50:"PluginVisitorInterestRendererPiwik\\API\\ApiRenderer";a:0:{}s:44:"PluginRssWidgetRendererPiwik\\API\\ApiRenderer";a:0:{}s:43:"PluginFeedbackRendererPiwik\\API\\ApiRenderer";a:0:{}s:42:"PluginMonologRendererPiwik\\API\\ApiRenderer";a:0:{}s:40:"PluginLoginRendererPiwik\\API\\ApiRenderer";a:0:{}s:48:"PluginTwoFactorAuthRendererPiwik\\API\\ApiRenderer";a:0:{}s:47:"PluginUsersManagerRendererPiwik\\API\\ApiRenderer";a:0:{}s:47:"PluginSitesManagerRendererPiwik\\API\\ApiRenderer";a:0:{}s:47:"PluginInstallationRendererPiwik\\API\\ApiRenderer";a:0:{}s:46:"PluginCoreUpdaterRendererPiwik\\API\\ApiRenderer";a:0:{}s:46:"PluginCoreConsoleRendererPiwik\\API\\ApiRenderer";a:0:{}s:51:"PluginScheduledReportsRendererPiwik\\API\\ApiRenderer";a:0:{}s:49:"PluginUserCountryMapRendererPiwik\\API\\ApiRenderer";a:0:{}s:39:"PluginLiveRendererPiwik\\API\\ApiRenderer";a:0:{}s:49:"PluginPrivacyManagerRendererPiwik\\API\\ApiRenderer";a:0:{}s:45:"PluginImageGraphRendererPiwik\\API\\ApiRenderer";a:0:{}s:46:"PluginAnnotationsRendererPiwik\\API\\ApiRenderer";a:0:{}s:50:"PluginMobileMessagingRendererPiwik\\API\\ApiRenderer";a:0:{}s:42:"PluginOverlayRendererPiwik\\API\\ApiRenderer";a:0:{}s:48:"PluginSegmentEditorRendererPiwik\\API\\ApiRenderer";a:0:{}s:43:"PluginInsightsRendererPiwik\\API\\ApiRenderer";a:0:{}s:43:"PluginMorpheusRendererPiwik\\API\\ApiRenderer";a:0:{}s:43:"PluginContentsRendererPiwik\\API\\ApiRenderer";a:0:{}s:47:"PluginBulkTrackingRendererPiwik\\API\\ApiRenderer";a:0:{}s:45:"PluginResolutionRendererPiwik\\API\\ApiRenderer";a:0:{}s:48:"PluginDevicePluginsRendererPiwik\\API\\ApiRenderer";a:0:{}s:44:"PluginHeartbeatRendererPiwik\\API\\ApiRenderer";a:0:{}s:39:"PluginIntlRendererPiwik\\API\\ApiRenderer";a:0:{}s:46:"PluginMarketplaceRendererPiwik\\API\\ApiRenderer";a:0:{}s:55:"PluginProfessionalServicesRendererPiwik\\API\\ApiRenderer";a:0:{}s:41:"PluginUserIdRendererPiwik\\API\\ApiRenderer";a:0:{}s:50:"PluginCustomJsTrackerRendererPiwik\\API\\ApiRenderer";a:0:{}s:39:"PluginTourRendererPiwik\\API\\ApiRenderer";a:0:{}s:50:"PluginPagePerformanceRendererPiwik\\API\\ApiRenderer";a:0:{}s:51:"PluginCustomDimensionsRendererPiwik\\API\\ApiRenderer";a:0:{}s:56:"PluginJsTrackerInstallCheckRendererPiwik\\API\\ApiRenderer";a:0:{}s:47:"PluginFeatureFlagsRendererPiwik\\API\\ApiRenderer";a:0:{}s:49:"PluginQueuedTrackingRendererPiwik\\API\\ApiRenderer";a:0:{}s:19:"availableLanguages0";a:58:{i:0;a:3:{s:4:"code";s:2:"am";s:4:"name";s:12:"አማርኛ";s:12:"english_name";s:7:"Amharic";}i:1;a:3:{s:4:"code";s:2:"ar";s:4:"name";s:14:"العربية";s:12:"english_name";s:6:"Arabic";}i:2;a:3:{s:4:"code";s:2:"be";s:4:"name";s:20:"Беларуская";s:12:"english_name";s:10:"Belarusian";}i:3;a:3:{s:4:"code";s:2:"bg";s:4:"name";s:18:"Български";s:12:"english_name";s:9:"Bulgarian";}i:4;a:3:{s:4:"code";s:2:"bn";s:4:"name";s:15:"বাংলা";s:12:"english_name";s:6:"Bangla";}i:5;a:3:{s:4:"code";s:2:"bs";s:4:"name";s:8:"Bosanski";s:12:"english_name";s:7:"Bosnian";}i:6;a:3:{s:4:"code";s:2:"ca";s:4:"name";s:7:"Català";s:12:"english_name";s:7:"Catalan";}i:7;a:3:{s:4:"code";s:2:"cs";s:4:"name";s:9:"Čeština";s:12:"english_name";s:5:"Czech";}i:8;a:3:{s:4:"code";s:2:"cy";s:4:"name";s:7:"Cymraeg";s:12:"english_name";s:5:"Welsh";}i:9;a:3:{s:4:"code";s:2:"da";s:4:"name";s:5:"Dansk";s:12:"english_name";s:6:"Danish";}i:10;a:3:{s:4:"code";s:2:"de";s:4:"name";s:7:"Deutsch";s:12:"english_name";s:6:"German";}i:11;a:3:{s:4:"code";s:2:"el";s:4:"name";s:16:"Ελληνικά";s:12:"english_name";s:5:"Greek";}i:12;a:3:{s:4:"code";s:2:"en";s:4:"name";s:7:"English";s:12:"english_name";s:7:"English";}i:13;a:3:{s:4:"code";s:2:"eo";s:4:"name";s:9:"Esperanto";s:12:"english_name";s:9:"Esperanto";}i:14;a:3:{s:4:"code";s:5:"es-ar";s:4:"name";s:20:"Español (Argentina)";s:12:"english_name";s:19:"Spanish (Argentina)";}i:15;a:3:{s:4:"code";s:2:"es";s:4:"name";s:8:"Español";s:12:"english_name";s:7:"Spanish";}i:16;a:3:{s:4:"code";s:2:"et";s:4:"name";s:5:"Eesti";s:12:"english_name";s:8:"Estonian";}i:17;a:3:{s:4:"code";s:2:"eu";s:4:"name";s:7:"Euskara";s:12:"english_name";s:6:"Basque";}i:18;a:3:{s:4:"code";s:2:"fa";s:4:"name";s:10:"فارسی";s:12:"english_name";s:7:"Persian";}i:19;a:3:{s:4:"code";s:2:"fi";s:4:"name";s:5:"Suomi";s:12:"english_name";s:7:"Finnish";}i:20;a:3:{s:4:"code";s:2:"fr";s:4:"name";s:9:"Français";s:12:"english_name";s:6:"French";}i:21;a:3:{s:4:"code";s:2:"gl";s:4:"name";s:6:"Galego";s:12:"english_name";s:8:"Galician";}i:22;a:3:{s:4:"code";s:2:"gu";s:4:"name";s:21:"ગુજરાતી";s:12:"english_name";s:8:"Gujarati";}i:23;a:3:{s:4:"code";s:2:"he";s:4:"name";s:10:"עברית";s:12:"english_name";s:6:"Hebrew";}i:24;a:3:{s:4:"code";s:2:"hi";s:4:"name";s:18:"हिन्दी";s:12:"english_name";s:5:"Hindi";}i:25;a:3:{s:4:"code";s:2:"hr";s:4:"name";s:8:"Hrvatski";s:12:"english_name";s:8:"Croatian";}i:26;a:3:{s:4:"code";s:2:"hu";s:4:"name";s:6:"Magyar";s:12:"english_name";s:9:"Hungarian";}i:27;a:3:{s:4:"code";s:2:"id";s:4:"name";s:9:"Indonesia";s:12:"english_name";s:10:"Indonesian";}i:28;a:3:{s:4:"code";s:2:"is";s:4:"name";s:9:"Íslenska";s:12:"english_name";s:9:"Icelandic";}i:29;a:3:{s:4:"code";s:2:"it";s:4:"name";s:8:"Italiano";s:12:"english_name";s:7:"Italian";}i:30;a:3:{s:4:"code";s:2:"ja";s:4:"name";s:9:"日本語";s:12:"english_name";s:8:"Japanese";}i:31;a:3:{s:4:"code";s:2:"ka";s:4:"name";s:21:"Ქართული";s:12:"english_name";s:8:"Georgian";}i:32;a:3:{s:4:"code";s:2:"ko";s:4:"name";s:9:"한국어";s:12:"english_name";s:6:"Korean";}i:33;a:3:{s:4:"code";s:2:"ku";s:4:"name";s:18:"Kurdî (kurmancî)";s:12:"english_name";s:7:"Kurdish";}i:34;a:3:{s:4:"code";s:2:"lt";s:4:"name";s:9:"Lietuvių";s:12:"english_name";s:10:"Lithuanian";}i:35;a:3:{s:4:"code";s:2:"lv";s:4:"name";s:9:"Latviešu";s:12:"english_name";s:7:"Latvian";}i:36;a:3:{s:4:"code";s:2:"nb";s:4:"name";s:13:"Norsk bokmål";s:12:"english_name";s:17:"Norwegian Bokmål";}i:37;a:3:{s:4:"code";s:2:"nl";s:4:"name";s:10:"Nederlands";s:12:"english_name";s:5:"Dutch";}i:38;a:3:{s:4:"code";s:2:"nn";s:4:"name";s:13:"Norsk nynorsk";s:12:"english_name";s:17:"Norwegian Nynorsk";}i:39;a:3:{s:4:"code";s:2:"pl";s:4:"name";s:6:"Polski";s:12:"english_name";s:6:"Polish";}i:40;a:3:{s:4:"code";s:5:"pt-br";s:4:"name";s:10:"Português";s:12:"english_name";s:20:"Brazilian Portuguese";}i:41;a:3:{s:4:"code";s:2:"pt";s:4:"name";s:18:"Português europeu";s:12:"english_name";s:10:"Portuguese";}i:42;a:3:{s:4:"code";s:2:"ro";s:4:"name";s:8:"Română";s:12:"english_name";s:8:"Romanian";}i:43;a:3:{s:4:"code";s:2:"ru";s:4:"name";s:14:"Русский";s:12:"english_name";s:7:"Russian";}i:44;a:3:{s:4:"code";s:2:"sk";s:4:"name";s:11:"Slovenčina";s:12:"english_name";s:6:"Slovak";}i:45;a:3:{s:4:"code";s:2:"sl";s:4:"name";s:13:"Slovenščina";s:12:"english_name";s:9:"Slovenian";}i:46;a:3:{s:4:"code";s:2:"sq";s:4:"name";s:5:"Shqip";s:12:"english_name";s:8:"Albanian";}i:47;a:3:{s:4:"code";s:2:"sr";s:4:"name";s:12:"Српски";s:12:"english_name";s:7:"Serbian";}i:48;a:3:{s:4:"code";s:2:"sv";s:4:"name";s:7:"Svenska";s:12:"english_name";s:7:"Swedish";}i:49;a:3:{s:4:"code";s:2:"ta";s:4:"name";s:15:"தமிழ்";s:12:"english_name";s:5:"Tamil";}i:50;a:3:{s:4:"code";s:2:"te";s:4:"name";s:18:"తెలుగు";s:12:"english_name";s:6:"Telugu";}i:51;a:3:{s:4:"code";s:2:"th";s:4:"name";s:9:"ไทย";s:12:"english_name";s:4:"Thai";}i:52;a:3:{s:4:"code";s:2:"tl";s:4:"name";s:7:"Tagalog";s:12:"english_name";s:7:"Tagalog";}i:53;a:3:{s:4:"code";s:2:"tr";s:4:"name";s:8:"Türkçe";s:12:"english_name";s:7:"Turkish";}i:54;a:3:{s:4:"code";s:2:"uk";s:4:"name";s:20:"Українська";s:12:"english_name";s:9:"Ukrainian";}i:55;a:3:{s:4:"code";s:2:"vi";s:4:"name";s:14:"Tiếng Việt";s:12:"english_name";s:10:"Vietnamese";}i:56;a:3:{s:4:"code";s:5:"zh-cn";s:4:"name";s:12:"简体中文";s:12:"english_name";s:18:"Simplified Chinese";}i:57;a:3:{s:4:"code";s:5:"zh-tw";s:4:"name";s:12:"繁體中文";s:12:"english_name";s:19:"Traditional Chinese";}}}}', ['allowed_classes' => false]);