<?php return unserialize('a:2:{s:8:"lifetime";i:1753701387;s:4:"data";a:35:{i:0;a:3:{s:5:"regex";s:32:"Outlook-Express(?:/(\\d+[.\\d]+))?";s:4:"name";s:15:"Outlook Express";s:7:"version";s:2:"$1";}i:1;a:3:{s:5:"regex";s:36:"^Outlook-iOS/(?:.+\\((\\d+[.\\d]+)\\)$)?";s:4:"name";s:17:"Microsoft Outlook";s:7:"version";s:2:"$1";}i:2;a:3:{s:5:"regex";s:57:"(?:(?:Microsoft )?Outlook|MacOutlook)(?:[/ ](\\d+[.\\d]+))?";s:4:"name";s:17:"Microsoft Outlook";s:7:"version";s:2:"$1";}i:3;a:3:{s:5:"regex";s:27:"WindowsMail(?:/(\\d+[.\\d]+))";s:4:"name";s:12:"Windows Mail";s:7:"version";s:2:"$1";}i:4;a:3:{s:5:"regex";s:49:"(?:Thunderbird|Icedove|Shredder)(?:/(\\d+[.\\d]+))?";s:4:"name";s:11:"Thunderbird";s:7:"version";s:2:"$1";}i:5;a:3:{s:5:"regex";s:23:"Spicebird/(\\d+\\.[.\\d]+)";s:4:"name";s:9:"Spicebird";s:7:"version";s:2:"$1";}i:6;a:3:{s:5:"regex";s:24:"Airmail(?: (\\d+[.\\d]+))?";s:4:"name";s:7:"Airmail";s:7:"version";s:2:"$1";}i:7;a:3:{s:5:"regex";s:28:"Lotus-Notes(?:/(\\d+[.\\d]+))?";s:4:"name";s:11:"Lotus Notes";s:7:"version";s:2:"$1";}i:8;a:3:{s:5:"regex";s:33:"Barca(?:Pro)?(?:[/ ](\\d+[.\\d]+))?";s:4:"name";s:5:"Barca";s:7:"version";s:2:"$1";}i:9;a:3:{s:5:"regex";s:27:"Postbox(?:[/ ](\\d+[.\\d]+))?";s:4:"name";s:7:"Postbox";s:7:"version";s:2:"$1";}i:10;a:3:{s:5:"regex";s:27:"MailBar(?:[/ ](\\d+[.\\d]+))?";s:4:"name";s:7:"MailBar";s:7:"version";s:2:"$1";}i:11;a:3:{s:5:"regex";s:41:"The Bat!(?: Voyager)?(?:[/ ](\\d+[.\\d]+))?";s:4:"name";s:8:"The Bat!";s:7:"version";s:2:"$1";}i:12;a:3:{s:5:"regex";s:25:"DAVdroid(?:/(\\d+[.\\d]+))?";s:4:"name";s:8:"DAVdroid";s:7:"version";s:2:"$1";}i:13;a:3:{s:5:"regex";s:37:"(?:SeaMonkey|Iceape)(?:/(\\d+[.\\d]+))?";s:4:"name";s:9:"SeaMonkey";s:7:"version";s:2:"$1";}i:14;a:3:{s:5:"regex";s:19:"Live5ch/(\\d+[.\\d]+)";s:4:"name";s:7:"Live5ch";s:7:"version";s:2:"$1";}i:15;a:3:{s:5:"regex";s:9:"JaneView/";s:4:"name";s:8:"JaneView";s:7:"version";s:0:"";}i:16;a:3:{s:5:"regex";s:12:"BathyScaphe/";s:4:"name";s:11:"BathyScaphe";s:7:"version";s:0:"";}i:17;a:3:{s:5:"regex";s:24:"Raindrop\\.io/(\\d+[.\\d]+)";s:4:"name";s:11:"Raindrop.io";s:7:"version";s:2:"$1";}i:18;a:3:{s:5:"regex";s:17:"Franz/(\\d+[.\\d]+)";s:4:"name";s:5:"Franz";s:7:"version";s:2:"$1";}i:19;a:3:{s:5:"regex";s:22:"Mailspring/(\\d+[.\\d]+)";s:4:"name";s:10:"Mailspring";s:7:"version";s:2:"$1";}i:20;a:3:{s:5:"regex";s:18:"Notion/(\\d+[.\\d]+)";s:4:"name";s:6:"Notion";s:7:"version";s:2:"$1";}i:21;a:3:{s:5:"regex";s:26:"Basecamp[0-9]/?(\\d+[.\\d]+)";s:4:"name";s:8:"Basecamp";s:7:"version";s:2:"$1";}i:22;a:3:{s:5:"regex";s:21:"Evernote/?(\\d+[.\\d]+)";s:4:"name";s:8:"Evernote";s:7:"version";s:2:"$1";}i:23;a:3:{s:5:"regex";s:24:"ramboxpro/(\\d+\\.[.\\d]+)?";s:4:"name";s:10:"Rambox Pro";s:7:"version";s:2:"$1";}i:24;a:3:{s:5:"regex";s:23:"Mailbird/(\\d+\\.[.\\d]+)/";s:4:"name";s:8:"Mailbird";s:7:"version";s:2:"$1";}i:25;a:3:{s:5:"regex";s:12:"Yahoo%20Mail";s:4:"name";s:10:"Yahoo Mail";s:7:"version";s:0:"";}i:26;a:3:{s:5:"regex";s:26:"jp.co.yahoo.ymail/([\\d.]+)";s:4:"name";s:11:"Yahoo! Mail";s:7:"version";s:2:"$1";}i:27;a:3:{s:5:"regex";s:24:"eM ?Client/(\\d+\\.[.\\d]+)";s:4:"name";s:9:"eM Client";s:7:"version";s:2:"$1";}i:28;a:3:{s:5:"regex";s:26:"NaverMailApp/(\\d+\\.[.\\d]+)";s:4:"name";s:10:"NAVER Mail";s:7:"version";s:2:"$1";}i:29;a:3:{s:5:"regex";s:14:"^Mail/([\\d.]+)";s:4:"name";s:10:"Apple Mail";s:7:"version";s:2:"$1";}i:30;a:3:{s:5:"regex";s:19:"Foxmail/(\\d+[.\\d]+)";s:4:"name";s:7:"Foxmail";s:7:"version";s:2:"$1";}i:31;a:3:{s:5:"regex";s:45:"MailMaster(?:PC|_Android_Mobile)?/(\\d+[.\\d]+)";s:4:"name";s:11:"Mail Master";s:7:"version";s:2:"$1";}i:32;a:3:{s:5:"regex";s:20:"BlueMail/(\\d+[.\\d]+)";s:4:"name";s:8:"BlueMail";s:7:"version";s:2:"$1";}i:33;a:3:{s:5:"regex";s:21:"mailapp/(\\d+\\.[.\\d]+)";s:4:"name";s:7:"mailapp";s:7:"version";s:2:"$1";}i:34;a:3:{s:5:"regex";s:13:"Android-Gmail";s:4:"name";s:5:"Gmail";s:7:"version";s:0:"";}}}', ['allowed_classes' => false]);