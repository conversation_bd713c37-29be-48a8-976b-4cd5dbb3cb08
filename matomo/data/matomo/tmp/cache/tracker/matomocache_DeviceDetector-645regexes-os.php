<?php return unserialize('a:2:{s:8:"lifetime";i:1753701387;s:4:"data";a:308:{i:0;a:3:{s:5:"regex";s:34:"Coolita OS QJY/.+LiteOS(\\d+[.\\d]+)";s:4:"name";s:10:"Coolita OS";s:7:"version";s:2:"$1";}i:1;a:3:{s:5:"regex";s:26:"Coolita OS QJY/(\\d+[.\\d]+)";s:4:"name";s:10:"Coolita OS";s:7:"version";s:2:"$1";}i:2;a:3:{s:5:"regex";s:6:"^NEXT ";s:4:"name";s:11:"RTOS & Next";s:7:"version";s:0:"";}i:3;a:3:{s:5:"regex";s:66:"(?:Pacific|(?<!like )Quest).+OculusBrowser|Standalone HMD|PortalGo";s:4:"name";s:12:"Meta Horizon";s:7:"version";s:0:"";}i:4;a:3:{s:5:"regex";s:6:"LeafOS";s:4:"name";s:6:"LeafOS";s:7:"version";s:0:"";}i:5;a:3:{s:5:"regex";s:11:"Cloud Phone";s:4:"name";s:9:"Puffin OS";s:7:"version";s:0:"";}i:6;a:3:{s:5:"regex";s:8:"RisingOS";s:4:"name";s:8:"risingOS";s:7:"version";s:0:"";}i:7;a:3:{s:5:"regex";s:16:".+.cm(\\d).x86_64";s:4:"name";s:11:"Azure Linux";s:7:"version";s:2:"$1";}i:8;a:3:{s:5:"regex";s:29:"Linux[/-].+-(azure|Microsoft)";s:4:"name";s:11:"Azure Linux";s:7:"version";s:0:"";}i:9;a:3:{s:5:"regex";s:18:"ViziOS/(\\d+[.\\d]+)";s:4:"name";s:6:"ViziOS";s:7:"version";s:2:"$1";}i:10;a:3:{s:5:"regex";s:24:"VIZIO(?: SmartCast|-DTV)";s:4:"name";s:6:"ViziOS";s:7:"version";s:0:"";}i:11;a:3:{s:5:"regex";s:15:"blackPanther OS";s:4:"name";s:15:"blackPanther OS";s:7:"version";s:0:"";}i:12;a:3:{s:5:"regex";s:24:"WoPhone(?:/(\\d+[.\\d]+))?";s:4:"name";s:7:"WoPhone";s:7:"version";s:2:"$1";}i:13;a:3:{s:5:"regex";s:28:"KIN\\.(?:One|Two) (\\d+[.\\d]+)";s:4:"name";s:6:"KIN OS";s:7:"version";s:2:"$1";}i:14;a:3:{s:5:"regex";s:13:"Star-Blade OS";s:4:"name";s:13:"Star-Blade OS";s:7:"version";s:0:"";}i:15;a:3:{s:5:"regex";s:18:"Qtopia/(\\d+[.\\d]+)";s:4:"name";s:6:"Qtopia";s:7:"version";s:2:"$1";}i:16;a:3:{s:5:"regex";s:20:"OpenVMS V(\\d+[.\\d]+)";s:4:"name";s:7:"OpenVMS";s:7:"version";s:2:"$1";}i:17;a:3:{s:5:"regex";s:4:"AROS";s:4:"name";s:4:"AROS";s:7:"version";s:0:"";}i:18;a:3:{s:5:"regex";s:8:"\\(NEXT\\)";s:4:"name";s:8:"NeXTSTEP";s:7:"version";s:0:"";}i:19;a:3:{s:5:"regex";s:19:"NEWS-OS (\\d+[.\\d]+)";s:4:"name";s:7:"NEWS-OS";s:7:"version";s:2:"$1";}i:20;a:3:{s:5:"regex";s:18:"ULTRIX (\\d+[.\\d]+)";s:4:"name";s:6:"ULTRIX";s:7:"version";s:2:"$1";}i:21;a:3:{s:5:"regex";s:22:"Turbolinux/(\\d+[.\\d]+)";s:4:"name";s:10:"Turbolinux";s:7:"version";s:2:"$1";}i:22;a:3:{s:5:"regex";s:19:"Joli OS/(\\d+[.\\d]+)";s:4:"name";s:7:"Joli OS";s:7:"version";s:2:"$1";}i:23;a:3:{s:5:"regex";s:17:"GENIX (\\d+[.\\d]+)";s:4:"name";s:5:"GENIX";s:7:"version";s:2:"$1";}i:24;a:3:{s:5:"regex";s:25:"gNewSense/.*\\((\\d+[.\\d]+)";s:4:"name";s:9:"gNewSense";s:7:"version";s:2:"$1";}i:25;a:3:{s:5:"regex";s:16:"Geos (\\d+[.\\d]+)";s:4:"name";s:4:"GEOS";s:7:"version";s:2:"$1";}i:26;a:3:{s:5:"regex";s:14:"MOT-.*LinuxOS/";s:4:"name";s:12:"Motorola EZX";s:7:"version";s:0:"";}i:27;a:3:{s:5:"regex";s:8:"Baidu Yi";s:4:"name";s:8:"Baidu Yi";s:7:"version";s:0:"";}i:28;a:3:{s:5:"regex";s:54:"붉은별|Fedora/(?:\\d+\\.[\\d.-]+)\\.rs(\\d+(?:[_.]\\d+)*)";s:4:"name";s:8:"Red Star";s:7:"version";s:2:"$1";}i:29;a:3:{s:5:"regex";s:38:"Linux/(?:\\d+\\.[\\d.-]+)-(?:pve|v(\\d)\\+)";s:4:"name";s:10:"Proxmox VE";s:7:"version";s:2:"$1";}i:30;a:3:{s:5:"regex";s:53:"Linux/(?:\\d+\\.[\\d.-]+)-(?:\\d+[.\\d]+)stab(?:\\d+[.\\d]+)";s:4:"name";s:6:"OpenVZ";s:7:"version";s:0:"";}i:31;a:3:{s:5:"regex";s:17:"rocky/(\\d+[.\\d]+)";s:4:"name";s:11:"Rocky Linux";s:7:"version";s:2:"$1";}i:32;a:3:{s:5:"regex";s:26:"clear-linux-os/(\\d+[.\\d]+)";s:4:"name";s:14:"Clear Linux OS";s:7:"version";s:2:"$1";}i:33;a:3:{s:5:"regex";s:18:"alpine/(\\d+[.\\d]+)";s:4:"name";s:12:"Alpine Linux";s:7:"version";s:2:"$1";}i:34;a:3:{s:5:"regex";s:22:"scientific/(\\d+[.\\d]+)";s:4:"name";s:16:"Scientific Linux";s:7:"version";s:2:"$1";}i:35;a:3:{s:5:"regex";s:18:"eulerosv(\\d)r(\\d+)";s:4:"name";s:7:"EulerOS";s:7:"version";s:5:"$1.$2";}i:36;a:3:{s:5:"regex";s:27:"loongnix-server/(\\d+[.\\d]+)";s:4:"name";s:8:"Loongnix";s:7:"version";s:2:"$1";}i:37;a:3:{s:5:"regex";s:57:"Linux/(?:\\d+\\.[\\d.-]+)\\.lns(\\d+(?:[_.]\\d+)*)\\.loongarch64";s:4:"name";s:8:"Loongnix";s:7:"version";s:2:"$1";}i:38;a:3:{s:5:"regex";s:7:"AOSC OS";s:4:"name";s:7:"AOSC OS";s:7:"version";s:0:"";}i:39;a:3:{s:5:"regex";s:20:"SerenityOS|Ladybird/";s:4:"name";s:10:"SerenityOS";s:7:"version";s:0:"";}i:40;a:3:{s:5:"regex";s:11:"Helix Phone";s:4:"name";s:8:"HELIX OS";s:7:"version";s:0:"";}i:41;a:3:{s:5:"regex";s:9:"Armadillo";s:4:"name";s:12:"Armadillo OS";s:7:"version";s:0:"";}i:42;a:3:{s:5:"regex";s:6:"Webian";s:4:"name";s:6:"Webian";s:7:"version";s:0:"";}i:43;a:3:{s:5:"regex";s:12:"Linux.*Liri/";s:4:"name";s:7:"Liri OS";s:7:"version";s:0:"";}i:44;a:3:{s:5:"regex";s:26:"FRITZ!OS(?:/0(\\d+[.\\d]+))?";s:4:"name";s:8:"FRITZ!OS";s:7:"version";s:2:"$1";}i:45;a:3:{s:5:"regex";s:4:"UOS$";s:4:"name";s:3:"UOS";s:7:"version";s:0:"";}i:46;a:3:{s:5:"regex";s:20:"Raspbian/(\\d+[.\\d]+)";s:4:"name";s:8:"Raspbian";s:7:"version";s:2:"$1";}i:47;a:3:{s:5:"regex";s:8:"Raspbian";s:4:"name";s:15:"Raspberry Pi OS";s:7:"version";s:0:"";}i:48;a:3:{s:5:"regex";s:36:"BrightSign/(?:[A-Z0-9]+)/(\\d+[.\\d]+)";s:4:"name";s:12:"BrightSignOS";s:7:"version";s:2:"$1";}i:49;a:3:{s:5:"regex";s:22:"BrightSign/(\\d+[.\\d]+)";s:4:"name";s:12:"BrightSignOS";s:7:"version";s:2:"$1";}i:50;a:3:{s:5:"regex";s:6:"LuneOS";s:4:"name";s:6:"LuneOS";s:7:"version";s:0:"";}i:51;a:3:{s:5:"regex";s:46:"Linux/(?:\\d+\\.[\\d.-]+)\\.el(\\d+(?:[_.]\\d+)*)uek";s:4:"name";s:12:"Oracle Linux";s:7:"version";s:2:"$1";}i:52;a:3:{s:5:"regex";s:10:".+kali(\\d)";s:4:"name";s:4:"Kali";s:7:"version";s:2:"$1";}i:53;a:3:{s:5:"regex";s:18:"TiVoOS/(\\d+[.\\d]+)";s:4:"name";s:7:"TiVo OS";s:7:"version";s:2:"$1";}i:54;a:3:{s:5:"regex";s:17:"VIDAA/(\\d+[.\\d]+)";s:4:"name";s:5:"VIDAA";s:7:"version";s:2:"$1";}i:55;a:3:{s:5:"regex";s:19:"PICO.+OS(\\d+[.\\d]*)";s:4:"name";s:7:"PICO OS";s:7:"version";s:2:"$1";}i:56;a:3:{s:5:"regex";s:13:"RED OS; Linux";s:4:"name";s:5:"RedOS";s:7:"version";s:0:"";}i:57;a:3:{s:5:"regex";s:16:"Mikrotik/(\\d)\\.x";s:4:"name";s:8:"RouterOS";s:7:"version";s:2:"$1";}i:58;a:3:{s:5:"regex";s:9:"FINNEY U1";s:4:"name";s:8:"Sirin OS";s:7:"version";s:0:"";}i:59;a:3:{s:5:"regex";s:11:"Nova; Linux";s:4:"name";s:4:"Nova";s:7:"version";s:0:"";}i:60;a:3:{s:5:"regex";s:23:"Android 10.+bliss_maple";s:4:"name";s:8:"Bliss OS";s:7:"version";s:2:"12";}i:61;a:3:{s:5:"regex";s:12:".+tlinux(\\d)";s:4:"name";s:9:"TencentOS";s:7:"version";s:2:"$1";}i:62;a:3:{s:5:"regex";s:38:".+.amzn(\\d)(?:int)?.x86_64|Linux/.+aws";s:4:"name";s:12:"Amazon Linux";s:7:"version";s:2:"$1";}i:63;a:3:{s:5:"regex";s:12:"Linux/.+-c9$";s:4:"name";s:12:"Amazon Linux";s:7:"version";s:4:"2023";}i:64;a:3:{s:5:"regex";s:17:" COS like Android";s:4:"name";s:8:"China OS";s:7:"version";s:0:"";}i:65;a:3:{s:5:"regex";s:31:"Android (\\d+[.\\d]*); ClearPHONE";s:4:"name";s:14:"ClearOS Mobile";s:7:"version";s:2:"$1";}i:66;a:3:{s:5:"regex";s:13:"Plasma Mobile";s:4:"name";s:13:"Plasma Mobile";s:7:"version";s:0:"";}i:67;a:3:{s:5:"regex";s:17:"KreaTV/0\\.0\\.0\\.0";s:4:"name";s:6:"KreaTV";s:7:"version";s:0:"";}i:68;a:3:{s:5:"regex";s:18:"KreaTV/(\\d+[.\\d]+)";s:4:"name";s:6:"KreaTV";s:7:"version";s:2:"$1";}i:69;a:3:{s:5:"regex";s:66:"Linux (?:[^;]+); Opera TV(?: Store)?/|^Opera/\\d+\\.\\d+ \\(Linux mips";s:4:"name";s:8:"Opera TV";s:7:"version";s:0:"";}i:70;a:3:{s:5:"regex";s:26:"OPR/.+TV Store/(\\d+[.\\d]+)";s:4:"name";s:8:"Opera TV";s:7:"version";s:2:"$1";}i:71;a:3:{s:5:"regex";s:19:"Grid OS (\\d+[.\\d]+)";s:4:"name";s:6:"GridOS";s:7:"version";s:2:"$1";}i:72;a:3:{s:5:"regex";s:11:"CaixaMagica";s:4:"name";s:13:"Caixa Mágica";s:7:"version";s:0:"";}i:73;a:3:{s:5:"regex";s:13:"Mageia; Linux";s:4:"name";s:6:"Mageia";s:7:"version";s:0:"";}i:74;a:3:{s:5:"regex";s:27:"(?:WH|WhaleTV)/?(\\d+[.\\d]+)";s:4:"name";s:8:"Whale OS";s:7:"version";s:2:"$1";}i:75;a:3:{s:5:"regex";s:21:"Zeasn/.*TBrowser/2\\.0";s:4:"name";s:8:"Whale OS";s:7:"version";s:1:"1";}i:76;a:3:{s:5:"regex";s:6:"Zeasn/";s:4:"name";s:8:"Whale OS";s:7:"version";s:0:"";}i:77;a:3:{s:5:"regex";s:22:"Tizen[ /]?(\\d+[.\\d]+)?";s:4:"name";s:5:"Tizen";s:7:"version";s:2:"$1";}i:78;a:3:{s:5:"regex";s:55:"Maple (?!III)(?:\\d+[.\\d]+)|Maple_?\\d{4}|HbbTV/.+Samsung";s:4:"name";s:5:"Tizen";s:7:"version";s:0:"";}i:79;a:3:{s:5:"regex";s:30:"(?:Ali)?YunOS[ /]?(\\d+[.\\d]+)?";s:4:"name";s:5:"YunOS";s:7:"version";s:2:"$1";}i:80;a:3:{s:5:"regex";s:30:"Windows Phone;FBSV/(\\d+[.\\d]+)";s:4:"name";s:13:"Windows Phone";s:7:"version";s:2:"$1";}i:81;a:3:{s:5:"regex";s:45:"(?:Windows Phone (?:OS)?|wds)[ /]?(\\d+[.\\d]+)";s:4:"name";s:13:"Windows Phone";s:7:"version";s:2:"$1";}i:82;a:3:{s:5:"regex";s:20:"XBLWP7|Windows Phone";s:4:"name";s:13:"Windows Phone";s:7:"version";s:0:"";}i:83;a:3:{s:5:"regex";s:27:"Windows CE(?: (\\d+[.\\d]+))?";s:4:"name";s:10:"Windows CE";s:7:"version";s:2:"$1";}i:84;a:3:{s:5:"regex";s:45:"(?:IEMobile|Windows ?Mobile)(?: (\\d+[.\\d]+))?";s:4:"name";s:14:"Windows Mobile";s:7:"version";s:2:"$1";}i:85;a:3:{s:5:"regex";s:21:"Windows NT 6\\.2; ARM;";s:4:"name";s:10:"Windows RT";s:7:"version";s:0:"";}i:86;a:3:{s:5:"regex";s:21:"Windows NT 6\\.3; ARM;";s:4:"name";s:10:"Windows RT";s:7:"version";s:3:"8.1";}i:87;a:3:{s:5:"regex";s:17:"Windows IoT 10\\.0";s:4:"name";s:11:"Windows IoT";s:7:"version";s:2:"10";}i:88;a:3:{s:5:"regex";s:22:"KAIOS(?:/(\\d+[.\\d]+))?";s:4:"name";s:5:"KaiOS";s:7:"version";s:2:"$1";}i:89;a:3:{s:5:"regex";s:29:"HarmonyOS(?:[/ ](\\d+[.\\d]+))?";s:4:"name";s:9:"HarmonyOS";s:7:"version";s:2:"$1";}i:90;a:3:{s:5:"regex";s:6:"Hmos/1";s:4:"name";s:9:"HarmonyOS";s:7:"version";s:5:"1.0.0";}i:91;a:3:{s:5:"regex";s:27:"RazoDroiD(?: v(\\d+[.\\d]*))?";s:4:"name";s:9:"RazoDroiD";s:7:"version";s:2:"$1";}i:92;a:3:{s:5:"regex";s:28:"MildWild(?: CM-(\\d+[.\\d]*))?";s:4:"name";s:8:"MildWild";s:7:"version";s:2:"$1";}i:93;a:3:{s:5:"regex";s:39:"CyanogenMod(?:[\\-/](?:CM)?(\\d+[.\\d]*))?";s:4:"name";s:11:"CyanogenMod";s:7:"version";s:2:"$1";}i:94;a:3:{s:5:"regex";s:34:"(?:.*_)?MocorDroid(?:(\\d+[.\\d]*))?";s:4:"name";s:10:"MocorDroid";s:7:"version";s:2:"$1";}i:95;a:3:{s:5:"regex";s:6:"FydeOS";s:4:"name";s:6:"FydeOS";s:7:"version";s:0:"";}i:96;a:3:{s:5:"regex";s:24:"Fire OS(?:/(\\d+[.\\d]*))?";s:4:"name";s:7:"Fire OS";s:7:"version";s:2:"$1";}i:97;a:3:{s:5:"regex";s:97:"(?:Andr[o0]id (\\d([\\d.])*);? |Amazon;|smarttv_)AFT|AEO[ACBHKT]|KF[ADFGJKMORSTQ]|.*FIRETVSTICK2018";s:4:"name";s:7:"Fire OS";s:8:"versions";a:10:{i:0;a:2:{s:5:"regex";s:43:"Andr[o0]id 1[01].+ (?:AFT|KF[ADFGJKMORSTQ])";s:7:"version";s:1:"8";}i:1;a:2:{s:5:"regex";s:57:"Andr[o0]id 9.+ (?:AEO[AHT]|AFT|KF[ADFGJKMORSTQ])|AFTSO001";s:7:"version";s:1:"7";}i:2;a:2:{s:5:"regex";s:30:"Andr[o0]id 7|.+FIRETVSTICK2018";s:7:"version";s:1:"6";}i:3;a:2:{s:5:"regex";s:15:"Andr[o0]id 5\\.1";s:7:"version";s:1:"5";}i:4;a:2:{s:5:"regex";s:18:"Andr[o0]id 4\\.4\\.3";s:7:"version";s:5:"4.5.1";}i:5;a:2:{s:5:"regex";s:18:"Andr[o0]id 4\\.4\\.2";s:7:"version";s:1:"4";}i:6;a:2:{s:5:"regex";s:18:"Andr[o0]id 4\\.2\\.2";s:7:"version";s:1:"3";}i:7;a:2:{s:5:"regex";s:21:"Andr[o0]id 4\\.0\\.[34]";s:7:"version";s:1:"3";}i:8;a:2:{s:5:"regex";s:15:"Andr[o0]id 4\\.0";s:7:"version";s:1:"2";}i:9;a:2:{s:5:"regex";s:18:"Andr[o0]id 2\\.3\\.3";s:7:"version";s:1:"1";}}}i:98;a:3:{s:5:"regex";s:21:"cordova-amazon-fireos";s:4:"name";s:7:"Fire OS";s:7:"version";s:0:"";}i:99;a:3:{s:5:"regex";s:12:"revengeos_x2";s:4:"name";s:10:"Revenge OS";s:7:"version";s:1:"2";}i:100;a:3:{s:5:"regex";s:19:"Lineage_(\\d+[.\\d]*)";s:4:"name";s:10:"Lineage OS";s:7:"version";s:2:"$1";}i:101;a:3:{s:5:"regex";s:39:"(?:Android (\\d([\\d.])*);? )?lineage_\\w+";s:4:"name";s:10:"Lineage OS";s:8:"versions";a:19:{i:0;a:2:{s:5:"regex";s:10:"Android 15";s:7:"version";s:2:"22";}i:1;a:2:{s:5:"regex";s:10:"Android 14";s:7:"version";s:2:"21";}i:2;a:2:{s:5:"regex";s:10:"Android 13";s:7:"version";s:4:"20.0";}i:3;a:2:{s:5:"regex";s:13:"Android 12\\.1";s:7:"version";s:4:"19.1";}i:4;a:2:{s:5:"regex";s:10:"Android 12";s:7:"version";s:4:"19.0";}i:5;a:2:{s:5:"regex";s:10:"Android 11";s:7:"version";s:4:"18.0";}i:6;a:2:{s:5:"regex";s:10:"Android 10";s:7:"version";s:4:"17.0";}i:7;a:2:{s:5:"regex";s:9:"Android 9";s:7:"version";s:4:"16.0";}i:8;a:2:{s:5:"regex";s:12:"Android 8\\.1";s:7:"version";s:4:"15.1";}i:9;a:2:{s:5:"regex";s:9:"Android 8";s:7:"version";s:4:"15.0";}i:10;a:2:{s:5:"regex";s:12:"Android 7\\.1";s:7:"version";s:4:"14.1";}i:11;a:2:{s:5:"regex";s:9:"Android 7";s:7:"version";s:4:"14.0";}i:12;a:2:{s:5:"regex";s:9:"Android 6";s:7:"version";s:4:"13.0";}i:13;a:2:{s:5:"regex";s:12:"Android 5\\.1";s:7:"version";s:4:"12.1";}i:14;a:2:{s:5:"regex";s:9:"Android 5";s:7:"version";s:4:"12.0";}i:15;a:2:{s:5:"regex";s:12:"Android 4\\.4";s:7:"version";s:4:"11.0";}i:16;a:2:{s:5:"regex";s:12:"Android 4\\.3";s:7:"version";s:4:"10.2";}i:17;a:2:{s:5:"regex";s:12:"Android 4\\.2";s:7:"version";s:4:"10.1";}i:18;a:2:{s:5:"regex";s:9:"Android 4";s:7:"version";s:5:"9.1.0";}}}i:102;a:3:{s:5:"regex";s:46:"Android 8(?:[\\d.]*);(?: [\\w-]+;)? rr_fortuna3g";s:4:"name";s:21:"Resurrection Remix OS";s:7:"version";s:1:"6";}i:103;a:3:{s:5:"regex";s:26:"RemixOS|Remix (?:Pro|Mini)";s:4:"name";s:8:"Remix OS";s:8:"versions";a:3:{i:0;a:2:{s:5:"regex";s:9:"RemixOS 5";s:7:"version";s:1:"1";}i:1;a:2:{s:5:"regex";s:20:"RemixOS 6|Remix Mini";s:7:"version";s:1:"2";}i:2;a:2:{s:5:"regex";s:9:"Remix Pro";s:7:"version";s:1:"3";}}}i:104;a:3:{s:5:"regex";s:16:"FreeBSD.+Android";s:4:"name";s:7:"FreeBSD";s:7:"version";s:0:"";}i:105;a:3:{s:5:"regex";s:48:"Chrome/(\\d+\\.[.\\d]+) Odd/|SM-R(?:8[6-9]|9)|LEM14";s:4:"name";s:7:"Wear OS";s:7:"version";s:0:"";}i:106;a:3:{s:5:"regex";s:26:"SeewoOS x86_64 (\\d+[.\\d]+)";s:4:"name";s:7:"SeewoOS";s:7:"version";s:2:"$1";}i:107;a:3:{s:5:"regex";s:56:"(?:CrOS [a-z0-9_]+ |.*Build/R\\d+-)(\\d+[.\\d]+)|Chromebook";s:4:"name";s:9:"Chrome OS";s:7:"version";s:2:"$1";}i:108;a:3:{s:5:"regex";s:314:"(?:Android (?:9|1[0-6])[.\\d]*|Linux x86_64); (?:asurada|atlas|brask|brya|cherry|coral|corsola|dedede|drallion|elm|eve|fizz|geralt|grunt|guybrush|hana|hatch|jacuzzi|kalista|kukui|nami|nautilus|nissa|nocturne|octopus|puff|pyro|rammus|reef|rex|sand|sarien|skyrim|snappy|soraka|staryu|strongbad|trogdor|volteer|zork)\\)";s:4:"name";s:9:"Chrome OS";s:7:"version";s:0:"";}i:109;a:3:{s:5:"regex";s:29:"Linux; Andr0id[; ](\\d+[.\\d]*)";s:4:"name";s:10:"Android TV";s:7:"version";s:2:"$1";}i:110;a:3:{s:5:"regex";s:101:"Android[; ](\\d+[.\\d]*).+(?:(?:Android(?: UHD)?|AT&T|Google|Smart)[ _]?TV|AOSP on r33a0|BRAVIA|wv-atv)";s:4:"name";s:10:"Android TV";s:7:"version";s:2:"$1";}i:111;a:3:{s:5:"regex";s:61:"Windows.+Andr0id TV|.+(?:K_?Android_?TV_|AndroidTV|GoogleTV_)";s:4:"name";s:10:"Android TV";s:7:"version";s:0:"";}i:112;a:3:{s:5:"regex";s:105:"(?:Android API \\d+|\\d+/tclwebkit(?:\\d+[.\\d]*)|(?:(?<!\\d )Android/\\d{2}|Android (?!1[0-6])\\d{2})(?![^; ]))";s:4:"name";s:7:"Android";s:8:"versions";a:22:{i:0;a:2:{s:5:"regex";s:41:"Android API 36|36/tclwebkit|Android[ /]36";s:7:"version";s:2:"16";}i:1;a:2:{s:5:"regex";s:41:"Android API 35|35/tclwebkit|Android[ /]35";s:7:"version";s:2:"15";}i:2;a:2:{s:5:"regex";s:41:"Android API 34|34/tclwebkit|Android[ /]34";s:7:"version";s:2:"14";}i:3;a:2:{s:5:"regex";s:41:"Android API 33|33/tclwebkit|Android[ /]33";s:7:"version";s:2:"13";}i:4;a:2:{s:5:"regex";s:41:"Android API 32|32/tclwebkit|Android[ /]32";s:7:"version";s:4:"12.1";}i:5;a:2:{s:5:"regex";s:41:"Android API 31|31/tclwebkit|Android[ /]31";s:7:"version";s:2:"12";}i:6;a:2:{s:5:"regex";s:41:"Android API 30|30/tclwebkit|Android[ /]30";s:7:"version";s:2:"11";}i:7;a:2:{s:5:"regex";s:41:"Android API 29|29/tclwebkit|Android[ /]29";s:7:"version";s:2:"10";}i:8;a:2:{s:5:"regex";s:41:"Android API 28|28/tclwebkit|Android[ /]28";s:7:"version";s:1:"9";}i:9;a:2:{s:5:"regex";s:41:"Android API 27|27/tclwebkit|Android[ /]27";s:7:"version";s:3:"8.1";}i:10;a:2:{s:5:"regex";s:41:"Android API 26|26/tclwebkit|Android[ /]26";s:7:"version";s:1:"8";}i:11;a:2:{s:5:"regex";s:41:"Android API 25|25/tclwebkit|Android[ /]25";s:7:"version";s:3:"7.1";}i:12;a:2:{s:5:"regex";s:41:"Android API 24|24/tclwebkit|Android[ /]24";s:7:"version";s:1:"7";}i:13;a:2:{s:5:"regex";s:41:"Android API 23|23/tclwebkit|Android[ /]23";s:7:"version";s:1:"6";}i:14;a:2:{s:5:"regex";s:41:"Android API 22|22/tclwebkit|Android[ /]22";s:7:"version";s:3:"5.1";}i:15;a:2:{s:5:"regex";s:41:"Android API 21|21/tclwebkit|Android[ /]21";s:7:"version";s:1:"5";}i:16;a:2:{s:5:"regex";s:62:"Android API (?:20|19)|(?:20|19)/tclwebkit|Android[ /](?:20|19)";s:7:"version";s:3:"4.4";}i:17;a:2:{s:5:"regex";s:41:"Android API 18|18/tclwebkit|Android[ /]18";s:7:"version";s:3:"4.3";}i:18;a:2:{s:5:"regex";s:41:"Android API 17|17/tclwebkit|Android[ /]17";s:7:"version";s:3:"4.2";}i:19;a:2:{s:5:"regex";s:41:"Android API 16|16/tclwebkit|Android[ /]16";s:7:"version";s:3:"4.1";}i:20;a:2:{s:5:"regex";s:41:"Android API 15|15/tclwebkit|Android[ /]15";s:7:"version";s:5:"4.0.3";}i:21;a:2:{s:5:"regex";s:41:"Android API 14|14/tclwebkit|Android[ /]14";s:7:"version";s:5:"4.0.1";}}}i:113;a:3:{s:5:"regex";s:19:"Android Marshmallow";s:4:"name";s:7:"Android";s:7:"version";s:1:"6";}i:114;a:3:{s:5:"regex";s:58:"(?:Podbean|Podimo)(?:.*)/Android|Rutube(?:TV)?BlackAndroid";s:4:"name";s:7:"Android";s:7:"version";s:0:"";}i:115;a:3:{s:5:"regex";s:73:"(?:Android OS|OMDroid|sdk_gphone64_arm64-userdebug|StarOS)[ /](\\d+[.\\d]*)";s:4:"name";s:7:"Android";s:7:"version";s:2:"$1";}i:116;a:3:{s:5:"regex";s:55:"Pinterest for Android(?: Tablet)?/.*; (\\d(?:[\\d.]*))\\)$";s:4:"name";s:7:"Android";s:7:"version";s:2:"$1";}i:117;a:3:{s:5:"regex";s:29:"Android; (\\d+[.\\d]*); Mobile;";s:4:"name";s:7:"Android";s:7:"version";s:2:"$1";}i:118;a:3:{s:5:"regex";s:45:"[ ]([\\d.]+)\\) AppleWebKit.*ROBLOX Android App";s:4:"name";s:7:"Android";s:7:"version";s:2:"$1";}i:119;a:3:{s:5:"regex";s:112:"(?:(?:Orca-)?(?<!like |/|RadioPublic |Anghami |Callpod Keeper for )Android|Adr|AOSP)[ /]?(?:[a-z]+ )?(\\d+[.\\d]*)";s:4:"name";s:7:"Android";s:7:"version";s:2:"$1";}i:120;a:3:{s:5:"regex";s:54:"(?:Allview_TX1_Quasar|Cosmote_My_mini_Tab) (\\d+[.\\d]*)";s:4:"name";s:7:"Android";s:7:"version";s:2:"$1";}i:121;a:3:{s:5:"regex";s:79:"Android ?(?:jelly bean|Kit Kat|S\\.O\\. Ginger Bread|The FireCyano|:) (\\d+[.\\d]*)";s:4:"name";s:7:"Android";s:7:"version";s:2:"$1";}i:122;a:3:{s:5:"regex";s:40:"(?:Orca-Android|FB4A).*FBSV/(\\d+[.\\d]*);";s:4:"name";s:7:"Android";s:7:"version";s:2:"$1";}i:123;a:3:{s:5:"regex";s:47:"(?:TwitterAndroid).*[ /](?:[a-z]+ )?(\\d+[.\\d]*)";s:4:"name";s:7:"Android";s:7:"version";s:2:"$1";}i:124;a:3:{s:5:"regex";s:31:"\\(Android:.*\\); API (\\d+[.\\d]*)";s:4:"name";s:7:"Android";s:7:"version";s:2:"$1";}i:125;a:3:{s:5:"regex";s:20:"Android-(\\d+[.\\d]*);";s:4:"name";s:7:"Android";s:7:"version";s:2:"$1";}i:126;a:3:{s:5:"regex";s:56:" Adr |.*(?<!like |/ )Android|Silk-Accelerated=[a-z]{4,5}";s:4:"name";s:7:"Android";s:7:"version";s:0:"";}i:127;a:3:{s:5:"regex";s:98:"BeyondPod|AntennaPod|Podkicker|DoggCatcher|okhttp|Podcatcher Deluxe|Sonos/.+\\(ACR_|.*WhatsApp/.*A$";s:4:"name";s:7:"Android";s:7:"version";s:0:"";}i:128;a:3:{s:5:"regex";s:29:"Linux; diordnA[; ](\\d+[.\\d]*)";s:4:"name";s:7:"Android";s:7:"version";s:2:"$1";}i:129;a:3:{s:5:"regex";s:15:"^A/(\\d+[.\\d]*)/";s:4:"name";s:7:"Android";s:7:"version";s:2:"$1";}i:130;a:3:{s:5:"regex";s:14:"Sailfish|Jolla";s:4:"name";s:11:"Sailfish OS";s:7:"version";s:0:"";}i:131;a:3:{s:5:"regex";s:22:"AmigaOS[ ]?(\\d+[.\\d]+)";s:4:"name";s:7:"AmigaOS";s:7:"version";s:2:"$1";}i:132;a:3:{s:5:"regex";s:31:"AmigaOS|AmigaVoyager|Amiga-AWeb";s:4:"name";s:7:"AmigaOS";s:7:"version";s:0:"";}i:133;a:3:{s:5:"regex";s:32:"ThreadX(?:_OS)?(?:/(\\d+[.\\d]*))?";s:4:"name";s:7:"ThreadX";s:7:"version";s:2:"$1";}i:134;a:3:{s:5:"regex";s:32:"Nucleus(?:(?: |/v?)(\\d+[.\\d]*))?";s:4:"name";s:13:"MTK / Nucleus";s:7:"version";s:2:"$1";}i:135;a:3:{s:5:"regex";s:28:"MTK(?:(?: |/v?)(\\d+[.\\d]*))?";s:4:"name";s:13:"MTK / Nucleus";s:7:"version";s:2:"$1";}i:136;a:3:{s:5:"regex";s:23:"MRE/(\\d+)\\.(\\d+).*;MAUI";s:4:"name";s:3:"MRE";s:7:"version";s:5:"$1.$2";}i:137;a:3:{s:5:"regex";s:8:"Linspire";s:4:"name";s:8:"Linspire";s:7:"version";s:0:"";}i:138;a:3:{s:5:"regex";s:9:"LindowsOS";s:4:"name";s:9:"LindowsOS";s:7:"version";s:0:"";}i:139;a:3:{s:5:"regex";s:17:"Zenwalk GNU Linux";s:4:"name";s:7:"Zenwalk";s:7:"version";s:0:"";}i:140;a:3:{s:5:"regex";s:14:"Linux.+kanotix";s:4:"name";s:7:"Kanotix";s:7:"version";s:0:"";}i:141;a:3:{s:5:"regex";s:19:"moonOS/(\\d+.[\\d.]+)";s:4:"name";s:6:"moonOS";s:7:"version";s:2:"$1";}i:142;a:3:{s:5:"regex";s:15:"Foresight Linux";s:4:"name";s:15:"Foresight Linux";s:7:"version";s:0:"";}i:143;a:3:{s:5:"regex";s:19:"Pardus/(\\d+.[\\d.]+)";s:4:"name";s:6:"Pardus";s:7:"version";s:2:"$1";}i:144;a:3:{s:5:"regex";s:8:"Librem 5";s:4:"name";s:6:"PureOS";s:7:"version";s:0:"";}i:145;a:3:{s:5:"regex";s:13:"uclient-fetch";s:4:"name";s:7:"OpenWrt";s:7:"version";s:0:"";}i:146;a:3:{s:5:"regex";s:28:"RokuOS/.+RokuOS (\\d+.[\\d.]+)";s:4:"name";s:7:"Roku OS";s:7:"version";s:2:"$1";}i:147;a:3:{s:5:"regex";s:46:"Roku(?:OS|4640X)?/(?:DVP|Pluto)?-?(\\d+[.\\d]+)?";s:4:"name";s:7:"Roku OS";s:7:"version";s:2:"$1";}i:148;a:3:{s:5:"regex";s:28:"Roku; (?:AP|UI); (\\d+[.\\d]+)";s:4:"name";s:7:"Roku OS";s:7:"version";s:2:"$1";}i:149;a:3:{s:5:"regex";s:45:"RokuBrowser/.+\\(TV-([a-z0-9]+)-(\\d+.[\\d.]+)\\)";s:4:"name";s:7:"Roku OS";s:7:"version";s:2:"$2";}i:150;a:3:{s:5:"regex";s:8:"dvkbuntu";s:4:"name";s:8:"DVKBuntu";s:7:"version";s:0:"";}i:151;a:3:{s:5:"regex";s:17:"Helio/(\\d+[.\\d]+)";s:4:"name";s:8:"Lumin OS";s:7:"version";s:2:"$1";}i:152;a:3:{s:5:"regex";s:23:"HasCodingOs (\\d+[.\\d]+)";s:4:"name";s:11:"HasCodingOS";s:7:"version";s:2:"$1";}i:153;a:3:{s:5:"regex";s:21:"PCLinuxOS/(\\d+[.\\d]+)";s:4:"name";s:9:"PCLinuxOS";s:7:"version";s:2:"$1";}i:154;a:3:{s:5:"regex";s:22:"(Ordissimo|webissimo3)";s:4:"name";s:9:"Ordissimo";s:7:"version";s:0:"";}i:155;a:3:{s:5:"regex";s:21:"(?:Win|Sistema )Fenix";s:4:"name";s:5:"Fenix";s:7:"version";s:0:"";}i:156;a:3:{s:5:"regex";s:10:"TOS; Linux";s:4:"name";s:6:"TmaxOS";s:7:"version";s:0:"";}i:157;a:3:{s:5:"regex";s:5:"Maemo";s:4:"name";s:5:"Maemo";s:7:"version";s:0:"";}i:158;a:3:{s:5:"regex";s:33:"Arch ?Linux(?:[ /\\-](\\d+[.\\d]+))?";s:4:"name";s:10:"Arch Linux";s:7:"version";s:2:"$1";}i:159;a:3:{s:5:"regex";s:46:"VectorLinux(?: package)?(?:[ /\\-](\\d+[.\\d]+))?";s:4:"name";s:11:"VectorLinux";s:7:"version";s:2:"$1";}i:160;a:3:{s:5:"regex";s:16:"sles/(\\d+[.\\d]+)";s:4:"name";s:4:"SUSE";s:7:"version";s:2:"$1";}i:161;a:3:{s:5:"regex";s:52:"(?:rhel|Red Hat Enterprise Linux Server)/(\\d+[.\\d]+)";s:4:"name";s:7:"Red Hat";s:7:"version";s:2:"$1";}i:162;a:3:{s:5:"regex";s:55:".+redhat-linux-gnu|rhel|Red Hat Enterprise Linux Server";s:4:"name";s:7:"Red Hat";s:7:"version";s:0:"";}i:163;a:3:{s:5:"regex";s:18:"CentOS Stream (\\d)";s:4:"name";s:13:"CentOS Stream";s:7:"version";s:2:"$1";}i:164;a:3:{s:5:"regex";s:30:"centos(?: Linux)?/(\\d+[.\\d]+) ";s:4:"name";s:6:"CentOS";s:7:"version";s:2:"$1";}i:165;a:3:{s:5:"regex";s:40:".+.el(\\d+(?:[_.]\\d+)*).(?:centos|x86_64)";s:4:"name";s:6:"CentOS";s:7:"version";s:2:"$1";}i:166;a:3:{s:5:"regex";s:17:"CentOS Linux (\\d)";s:4:"name";s:6:"CentOS";s:7:"version";s:2:"$1";}i:167;a:3:{s:5:"regex";s:17:"Fedora/.+.fc(\\d+)";s:4:"name";s:6:"Fedora";s:7:"version";s:2:"$1";}i:168;a:3:{s:5:"regex";s:36:"Mandriva(?: Linux)?/.+mdv(\\d+[.\\d]+)";s:4:"name";s:8:"Mandriva";s:7:"version";s:2:"$1";}i:169;a:3:{s:5:"regex";s:15:"Linux Mint/(\\d)";s:4:"name";s:4:"Mint";s:7:"version";s:2:"$1";}i:170;a:3:{s:5:"regex";s:14:"Zorin OS (\\d+)";s:4:"name";s:7:"ZorinOS";s:7:"version";s:2:"$1";}i:171;a:3:{s:5:"regex";s:15:"Ubuntu[-/]hardy";s:4:"name";s:6:"Ubuntu";s:7:"version";s:4:"8.04";}i:172;a:3:{s:5:"regex";s:14:"debian/stretch";s:4:"name";s:6:"Debian";s:7:"version";s:4:"9.13";}i:173;a:3:{s:5:"regex";s:16:"Ubuntu[-/]feisty";s:4:"name";s:6:"Ubuntu";s:7:"version";s:4:"7.04";}i:174;a:3:{s:5:"regex";s:14:"Ubuntu[-/]edgy";s:4:"name";s:6:"Ubuntu";s:7:"version";s:4:"6.10";}i:175;a:3:{s:5:"regex";s:16:"Ubuntu[-/]dapper";s:4:"name";s:6:"Ubuntu";s:7:"version";s:4:"6.06";}i:176;a:3:{s:5:"regex";s:16:"Ubuntu[-/]breezy";s:4:"name";s:6:"Ubuntu";s:7:"version";s:4:"5.10";}i:177;a:3:{s:5:"regex";s:21:"Ubuntu[ /](\\d+[.\\d]+)";s:4:"name";s:6:"Ubuntu";s:7:"version";s:2:"$1";}i:178;a:3:{s:5:"regex";s:151:"Linux; .*((?:Debian|Knoppix|Mint|Ubuntu|Kubuntu|Xubuntu|Lubuntu|Fedora|Red Hat|Mandriva|Gentoo|Sabayon|Slackware|SUSE|CentOS|BackTrack))[ /](\\d+[.\\d]+)";s:4:"name";s:2:"$1";s:7:"version";s:2:"$2";}i:179;a:3:{s:5:"regex";s:21:"Deepin[ /](\\d+[.\\d]+)";s:4:"name";s:6:"Deepin";s:7:"version";s:2:"$1";}i:180;a:3:{s:5:"regex";s:201:"(Debian|Knoppix|Mint(?! Browser)|Ubuntu|Kubuntu|Xubuntu|Lubuntu|Fedora|Red Hat|Mandriva|Gentoo|Sabayon|Slackware|SUSE|CentOS|BackTrack|Freebox|ASPLinux)(?:(?: Enterprise)? Linux)?(?:[ /\\-](\\d+[.\\d]+))?";s:4:"name";s:2:"$1";s:7:"version";s:2:"$2";}i:181;a:3:{s:5:"regex";s:14:"OS ROSA; Linux";s:4:"name";s:4:"Rosa";s:7:"version";s:0:"";}i:182;a:4:{s:5:"regex";s:67:"(?:Web0S; .*WEBOS|webOS|web0S|Palm webOS|hpwOS)(?:[/]?(\\d+[.\\d]+))?";s:4:"name";s:5:"webOS";s:7:"version";s:2:"$1";s:8:"versions";a:11:{i:0;a:2:{s:5:"regex";s:16:"WEBOS(\\d+[.\\d]+)";s:7:"version";s:2:"$1";}i:1;a:2:{s:5:"regex";s:35:"Web0S; Linux/SmartTV.+Chr[o0]me/108";s:7:"version";s:2:"24";}i:2;a:2:{s:5:"regex";s:34:"Web0S; Linux/SmartTV.+Chr[o0]me/94";s:7:"version";s:2:"23";}i:3;a:2:{s:5:"regex";s:34:"Web0S; Linux/SmartTV.+Chr[o0]me/87";s:7:"version";s:2:"22";}i:4;a:2:{s:5:"regex";s:34:"Web0S; Linux/SmartTV.+Chr[o0]me/79";s:7:"version";s:1:"6";}i:5;a:2:{s:5:"regex";s:34:"Web0S; Linux/SmartTV.+Chr[o0]me/68";s:7:"version";s:1:"5";}i:6;a:2:{s:5:"regex";s:34:"Web0S; Linux/SmartTV.+Chr[o0]me/53";s:7:"version";s:1:"4";}i:7;a:2:{s:5:"regex";s:34:"Web0S; Linux/SmartTV.+Chr[o0]me/38";s:7:"version";s:1:"3";}i:8;a:2:{s:5:"regex";s:6:"WEBOS1";s:7:"version";s:1:"1";}i:9;a:2:{s:5:"regex";s:32:"Web0S; Linux/SmartTV.+Safari/538";s:7:"version";s:1:"2";}i:10;a:2:{s:5:"regex";s:32:"Web0S; Linux/SmartTV.+Safari/537";s:7:"version";s:1:"1";}}}i:183;a:3:{s:5:"regex";s:43:"(?:PalmOS|Palm OS)(?:[/ ](\\d+[.\\d]+))?|Palm";s:4:"name";s:6:"palmOS";s:7:"version";s:2:"$1";}i:184;a:3:{s:5:"regex";s:27:"Xiino(?:.*v\\. (\\d+[.\\d]+))?";s:4:"name";s:6:"palmOS";s:7:"version";s:2:"$1";}i:185;a:3:{s:5:"regex";s:27:"MorphOS(?:[ /](\\d+[.\\d]+))?";s:4:"name";s:7:"MorphOS";s:7:"version";s:2:"$1";}i:186;a:3:{s:5:"regex";s:22:"FBW.+FBSV/(\\d+[.\\d]*);";s:4:"name";s:7:"Windows";s:7:"version";s:2:"$1";}i:187;a:3:{s:5:"regex";s:24:"Windows.+OS: (\\d+[.\\d]*)";s:4:"name";s:7:"Windows";s:7:"version";s:2:"$1";}i:188;a:3:{s:5:"regex";s:22:"Windows; ?(\\d+[.\\d]*);";s:4:"name";s:7:"Windows";s:7:"version";s:2:"$1";}i:189;a:3:{s:5:"regex";s:29:"mingw32|winhttp|WhatsApp/.*W$";s:4:"name";s:7:"Windows";s:7:"version";s:0:"";}i:190;a:3:{s:5:"regex";s:118:"(?:Windows(?:-Update-Agent)?|Microsoft-(?:CryptoAPI|Delivery-Optimization|WebDAV-MiniRedir|WNS)|WINDOWS_64)/(\\d+\\.\\d+)";s:4:"name";s:7:"Windows";s:7:"version";s:2:"$1";}i:191;a:3:{s:5:"regex";s:16:"Windows-(1[01])-";s:4:"name";s:7:"Windows";s:7:"version";s:2:"$1";}i:192;a:3:{s:5:"regex";s:51:"CYGWIN_NT-10\\.0|Win(?:dows )?NT 10\\.0|Windows[ /]10";s:4:"name";s:7:"Windows";s:7:"version";s:2:"10";}i:193;a:3:{s:5:"regex";s:47:"CYGWIN_NT-6\\.4|Windows NT 6\\.4|Windows 10|win10";s:4:"name";s:7:"Windows";s:7:"version";s:2:"10";}i:194;a:3:{s:5:"regex";s:38:"Windows[/-](2016|2019|2022|2025)Server";s:4:"name";s:7:"Windows";s:7:"version";s:9:"Server $1";}i:195;a:3:{s:5:"regex";s:20:"Windows/2012ServerR2";s:4:"name";s:7:"Windows";s:7:"version";s:14:"Server 2012 R2";}i:196;a:3:{s:5:"regex";s:46:"CYGWIN_NT-6\\.3|Windows NT 6\\.3|Windows[ /]8\\.1";s:4:"name";s:7:"Windows";s:7:"version";s:3:"8.1";}i:197;a:3:{s:5:"regex";s:18:"Windows/2012Server";s:4:"name";s:7:"Windows";s:7:"version";s:11:"Server 2012";}i:198;a:3:{s:5:"regex";s:55:"CYGWIN_NT-6\\.2|Windows NT 6\\.2|Windows 8|post2008Server";s:4:"name";s:7:"Windows";s:7:"version";s:1:"8";}i:199;a:3:{s:5:"regex";s:20:"Windows/2008ServerR2";s:4:"name";s:7:"Windows";s:7:"version";s:14:"Server 2008 R2";}i:200;a:3:{s:5:"regex";s:63:"CYGWIN_NT-6\\.1|Windows NT 6\\.1|Windows[ /]7|win7|Windows \\(6\\.1";s:4:"name";s:7:"Windows";s:7:"version";s:1:"7";}i:201;a:3:{s:5:"regex";s:44:"CYGWIN_NT-6\\.0|Windows NT 6\\.0|Windows Vista";s:4:"name";s:7:"Windows";s:7:"version";s:5:"Vista";}i:202;a:3:{s:5:"regex";s:78:"CYGWIN_NT-5\\.2|Windows NT 5\\.2|Windows Server 2003 / XP x64|Windows/2003Server";s:4:"name";s:7:"Windows";s:7:"version";s:11:"Server 2003";}i:203;a:3:{s:5:"regex";s:41:"CYGWIN_NT-5\\.1|Windows NT 5\\.1|Windows XP";s:4:"name";s:7:"Windows";s:7:"version";s:2:"XP";}i:204;a:3:{s:5:"regex";s:43:"CYGWIN_NT-5\\.0|Windows NT 5\\.0|Windows 2000";s:4:"name";s:7:"Windows";s:7:"version";s:4:"2000";}i:205;a:3:{s:5:"regex";s:54:"CYGWIN_NT-4\\.0|Windows NT 4\\.0|WinNT(?! 10)|Windows NT";s:4:"name";s:7:"Windows";s:7:"version";s:2:"NT";}i:206;a:3:{s:5:"regex";s:39:"CYGWIN_ME-4\\.90|Win 9x 4\\.90|Windows ME";s:4:"name";s:7:"Windows";s:7:"version";s:2:"ME";}i:207;a:3:{s:5:"regex";s:32:"CYGWIN_98-4\\.10|Win98|Windows 98";s:4:"name";s:7:"Windows";s:7:"version";s:2:"98";}i:208;a:3:{s:5:"regex";s:48:"CYGWIN_95-4\\.0|Win32|Win95|Windows 95|Windows_95";s:4:"name";s:7:"Windows";s:7:"version";s:2:"95";}i:209;a:3:{s:5:"regex";s:12:"Windows 3\\.1";s:4:"name";s:7:"Windows";s:7:"version";s:3:"3.1";}i:210;a:3:{s:5:"regex";s:79:"Windows|.+win32|Win64|MSDW|HandBrake Win Upd|Microsoft BITS|ms-office; MSOffice";s:4:"name";s:7:"Windows";s:7:"version";s:0:"";}i:211;a:3:{s:5:"regex";s:34:"OS/Microsoft_Windows_NT_(\\d+\\.\\d+)";s:4:"name";s:7:"Windows";s:7:"version";s:2:"$1";}i:212;a:3:{s:5:"regex";s:5:"Haiku";s:4:"name";s:8:"Haiku OS";s:7:"version";s:0:"";}i:213;a:3:{s:5:"regex";s:63:"Apple ?TV.*CPU (?:iPhone )?OS ((?:9|1[0-8])[_.]\\d+(?:[_.]\\d+)*)";s:4:"name";s:4:"tvOS";s:7:"version";s:2:"$1";}i:214;a:3:{s:5:"regex";s:47:"Apple TV; iOS ((?:9|1[0-8])[_.]\\d+(?:[_.]\\d+)*)";s:4:"name";s:4:"tvOS";s:7:"version";s:2:"$1";}i:215;a:3:{s:5:"regex";s:61:"iOS(?:; |/)((?:9|1[0-8])\\.\\d+(?:[_.]\\d+)*) (?:model/)?AppleTV";s:4:"name";s:4:"tvOS";s:7:"version";s:2:"$1";}i:216;a:3:{s:5:"regex";s:42:"tvOS[ /]?((?:9|1[0-8])\\.\\d+(?:[_.]\\d+)*);?";s:4:"name";s:4:"tvOS";s:7:"version";s:2:"$1";}i:217;a:3:{s:5:"regex";s:25:"AppleTV(?:/?(\\d+[.\\d]+))?";s:4:"name";s:4:"tvOS";s:7:"version";s:2:"$1";}i:218;a:3:{s:5:"regex";s:51:"(?:Watch1,[12]/|Watch OS,|watchOS[ /]?)(\\d+[.\\d]*)?";s:4:"name";s:7:"watchOS";s:7:"version";s:2:"$1";}i:219;a:3:{s:5:"regex";s:16:"Apple Watch(?!;)";s:4:"name";s:7:"watchOS";s:7:"version";s:0:"";}i:220;a:3:{s:5:"regex";s:40:"FBMD/iPad;.*FBSV/ ?(1[3-8]).(\\d+[.\\d]*);";s:4:"name";s:6:"iPadOS";s:7:"version";s:5:"$1.$2";}i:221;a:3:{s:5:"regex";s:36:"iPad(?:OS)?[ /](1[3-8])\\.(\\d+[.\\d]*)";s:4:"name";s:6:"iPadOS";s:7:"version";s:5:"$1.$2";}i:222;a:3:{s:5:"regex";s:41:"^iPad(?:\\d+[\\,\\d]*)/(1[3-8])\\.(\\d+[.\\d]*)";s:4:"name";s:6:"iPadOS";s:7:"version";s:5:"$1.$2";}i:223;a:3:{s:5:"regex";s:85:"iPad(?:; (?:iOS|iPadOS|iPhone OS)|.+CPU (?:iPad |iPhone )?OS) ((1[3-8])+(?:[_.]\\d+)*)";s:4:"name";s:6:"iPadOS";s:7:"version";s:2:"$1";}i:224;a:3:{s:5:"regex";s:37:"iOS/(1[3-8])\\.(\\d+[.\\d]*).+Apple/iPad";s:4:"name";s:6:"iPadOS";s:7:"version";s:5:"$1.$2";}i:225;a:3:{s:5:"regex";s:37:"iPhone OS,(1[3-8])\\.(\\d+[.\\d]*).+iPad";s:4:"name";s:6:"iPadOS";s:7:"version";s:5:"$1.$2";}i:226;a:3:{s:5:"regex";s:35:"iphoneos(1[3-8])\\.(\\d+[.\\d]*); iPad";s:4:"name";s:6:"iPadOS";s:7:"version";s:5:"$1.$2";}i:227;a:3:{s:5:"regex";s:46:"Pinterest for iOS/.*iPad.*; (\\d(?:[\\d.]*))[)]$";s:4:"name";s:6:"iPadOS";s:7:"version";s:2:"$1";}i:228;a:3:{s:5:"regex";s:31:"iPad/([89]|1[012])\\.(\\d+[.\\d]*)";s:4:"name";s:3:"iOS";s:7:"version";s:5:"$1.$2";}i:229;a:3:{s:5:"regex";s:45:"^(?:iPad|iPhone)(?:\\d+[\\,\\d]*)[/_](\\d+[.\\d]+)";s:4:"name";s:3:"iOS";s:7:"version";s:2:"$1";}i:230;a:3:{s:5:"regex";s:37:"iphoneos(1[3-8])\\.(\\d+[.\\d]*); iPhone";s:4:"name";s:3:"iOS";s:7:"version";s:5:"$1.$2";}i:231;a:3:{s:5:"regex";s:48:"Pinterest for iOS/.*iPhone.*; (\\d(?:[\\d.]*))[)]$";s:4:"name";s:3:"iOS";s:7:"version";s:2:"$1";}i:232;a:3:{s:5:"regex";s:17:"iOS (\\d+[.\\d]+)\\)";s:4:"name";s:3:"iOS";s:7:"version";s:2:"$1";}i:233;a:3:{s:5:"regex";s:40:"iPhone OS ([0-9]{1})([0-9]{1})([0-9]{1})";s:4:"name";s:3:"iOS";s:7:"version";s:8:"$1.$2.$3";}i:234;a:3:{s:5:"regex";s:80:"(?:CPU OS|iPh(?:one)?[ _]OS|iPhone.+ OS|PodMN.+iPhone|iOS)[ _/](\\d+(?:[_.]\\d+)*)";s:4:"name";s:3:"iOS";s:7:"version";s:2:"$1";}i:235;a:3:{s:5:"regex";s:52:"(?:iPhone ?OS|iOS(?: Version)?)(?:/|; |,)(\\d+[.\\d]+)";s:4:"name";s:3:"iOS";s:7:"version";s:2:"$1";}i:236;a:3:{s:5:"regex";s:154:"^(?!com\\.apple\\.Safari\\.SearchHelper|Safari|NetworkingExtension).*(?:CFNetwork|Mana)/.+ Darwin/(\\d+[.\\d]+)(?!.*(?:x86_64|i386|PowerMac|Power%20Macintosh))";s:4:"name";s:3:"iOS";s:8:"versions";a:80:{i:0;a:2:{s:5:"regex";s:15:"Darwin/24\\.3\\.0";s:7:"version";s:4:"18.3";}i:1;a:2:{s:5:"regex";s:15:"Darwin/24\\.2\\.0";s:7:"version";s:4:"18.2";}i:2;a:2:{s:5:"regex";s:15:"Darwin/24\\.1\\.0";s:7:"version";s:4:"18.1";}i:3;a:2:{s:5:"regex";s:15:"Darwin/24\\.0\\.0";s:7:"version";s:4:"18.0";}i:4;a:2:{s:5:"regex";s:15:"Darwin/23\\.6\\.0";s:7:"version";s:4:"17.6";}i:5;a:2:{s:5:"regex";s:15:"Darwin/23\\.5\\.0";s:7:"version";s:4:"17.5";}i:6;a:2:{s:5:"regex";s:15:"Darwin/23\\.4\\.0";s:7:"version";s:4:"17.4";}i:7;a:2:{s:5:"regex";s:15:"Darwin/23\\.3\\.0";s:7:"version";s:4:"17.3";}i:8;a:2:{s:5:"regex";s:15:"Darwin/23\\.2\\.0";s:7:"version";s:4:"17.2";}i:9;a:2:{s:5:"regex";s:15:"Darwin/23\\.1\\.0";s:7:"version";s:4:"17.1";}i:10;a:2:{s:5:"regex";s:15:"Darwin/23\\.0\\.0";s:7:"version";s:4:"17.0";}i:11;a:2:{s:5:"regex";s:15:"Darwin/22\\.6\\.0";s:7:"version";s:4:"16.6";}i:12;a:2:{s:5:"regex";s:15:"Darwin/22\\.5\\.0";s:7:"version";s:4:"16.5";}i:13;a:2:{s:5:"regex";s:15:"Darwin/22\\.4\\.0";s:7:"version";s:4:"16.4";}i:14;a:2:{s:5:"regex";s:15:"Darwin/22\\.3\\.0";s:7:"version";s:4:"16.3";}i:15;a:2:{s:5:"regex";s:15:"Darwin/22\\.2\\.0";s:7:"version";s:4:"16.2";}i:16;a:2:{s:5:"regex";s:15:"Darwin/22\\.1\\.0";s:7:"version";s:4:"16.1";}i:17;a:2:{s:5:"regex";s:15:"Darwin/22\\.0\\.0";s:7:"version";s:4:"16.0";}i:18;a:2:{s:5:"regex";s:15:"Darwin/21\\.6\\.0";s:7:"version";s:4:"15.6";}i:19;a:2:{s:5:"regex";s:15:"Darwin/21\\.5\\.0";s:7:"version";s:4:"15.5";}i:20;a:2:{s:5:"regex";s:15:"Darwin/21\\.4\\.0";s:7:"version";s:4:"15.4";}i:21;a:2:{s:5:"regex";s:15:"Darwin/21\\.3\\.0";s:7:"version";s:4:"15.3";}i:22;a:2:{s:5:"regex";s:15:"Darwin/21\\.2\\.0";s:7:"version";s:4:"15.2";}i:23;a:2:{s:5:"regex";s:15:"Darwin/21\\.1\\.0";s:7:"version";s:4:"15.1";}i:24;a:2:{s:5:"regex";s:15:"Darwin/21\\.0\\.0";s:7:"version";s:4:"15.0";}i:25;a:2:{s:5:"regex";s:15:"Darwin/20\\.6\\.0";s:7:"version";s:4:"14.7";}i:26;a:2:{s:5:"regex";s:15:"Darwin/20\\.5\\.0";s:7:"version";s:4:"14.6";}i:27;a:2:{s:5:"regex";s:15:"Darwin/20\\.4\\.0";s:7:"version";s:4:"14.5";}i:28;a:2:{s:5:"regex";s:15:"Darwin/20\\.3\\.0";s:7:"version";s:4:"14.4";}i:29;a:2:{s:5:"regex";s:15:"Darwin/20\\.2\\.0";s:7:"version";s:4:"14.3";}i:30;a:2:{s:5:"regex";s:15:"Darwin/20\\.1\\.0";s:7:"version";s:4:"14.2";}i:31;a:2:{s:5:"regex";s:15:"Darwin/20\\.0\\.0";s:7:"version";s:4:"14.0";}i:32;a:2:{s:5:"regex";s:15:"Darwin/19\\.6\\.0";s:7:"version";s:4:"13.6";}i:33;a:2:{s:5:"regex";s:15:"Darwin/19\\.5\\.0";s:7:"version";s:4:"13.5";}i:34;a:2:{s:5:"regex";s:15:"Darwin/19\\.4\\.0";s:7:"version";s:4:"13.4";}i:35;a:2:{s:5:"regex";s:15:"Darwin/19\\.3\\.0";s:7:"version";s:6:"13.3.1";}i:36;a:2:{s:5:"regex";s:15:"Darwin/19\\.2\\.0";s:7:"version";s:4:"13.3";}i:37;a:2:{s:5:"regex";s:15:"Darwin/19\\.0\\.0";s:7:"version";s:4:"13.0";}i:38;a:2:{s:5:"regex";s:15:"Darwin/18\\.7\\.0";s:7:"version";s:4:"12.4";}i:39;a:2:{s:5:"regex";s:15:"Darwin/18\\.6\\.0";s:7:"version";s:4:"12.3";}i:40;a:2:{s:5:"regex";s:15:"Darwin/18\\.5\\.0";s:7:"version";s:4:"12.2";}i:41;a:2:{s:5:"regex";s:15:"Darwin/18\\.2\\.0";s:7:"version";s:4:"12.1";}i:42;a:2:{s:5:"regex";s:15:"Darwin/18\\.0\\.0";s:7:"version";s:4:"12.0";}i:43;a:2:{s:5:"regex";s:15:"Darwin/17\\.7\\.0";s:7:"version";s:6:"11.4.1";}i:44;a:2:{s:5:"regex";s:15:"Darwin/17\\.6\\.0";s:7:"version";s:4:"11.4";}i:45;a:2:{s:5:"regex";s:15:"Darwin/17\\.5\\.0";s:7:"version";s:4:"11.3";}i:46;a:2:{s:5:"regex";s:15:"Darwin/17\\.4\\.0";s:7:"version";s:6:"11.2.6";}i:47;a:2:{s:5:"regex";s:15:"Darwin/17\\.3\\.0";s:7:"version";s:4:"11.2";}i:48;a:2:{s:5:"regex";s:13:"CFNetwork/889";s:7:"version";s:4:"11.1";}i:49;a:2:{s:5:"regex";s:13:"CFNetwork/887";s:7:"version";s:4:"11.0";}i:50;a:2:{s:5:"regex";s:13:"CFNetwork/811";s:7:"version";s:4:"10.3";}i:51;a:2:{s:5:"regex";s:16:"CFNetwork/808\\.3";s:7:"version";s:4:"10.3";}i:52;a:2:{s:5:"regex";s:16:"CFNetwork/808\\.2";s:7:"version";s:4:"10.2";}i:53;a:2:{s:5:"regex";s:16:"CFNetwork/808\\.1";s:7:"version";s:4:"10.1";}i:54;a:2:{s:5:"regex";s:16:"CFNetwork/808\\.0";s:7:"version";s:4:"10.0";}i:55;a:2:{s:5:"regex";s:13:"CFNetwork/808";s:7:"version";s:2:"10";}i:56;a:2:{s:5:"regex";s:19:"CFNetwork/758\\.5\\.3";s:7:"version";s:5:"9.3.5";}i:57;a:2:{s:5:"regex";s:19:"CFNetwork/758\\.4\\.3";s:7:"version";s:5:"9.3.2";}i:58;a:2:{s:5:"regex";s:20:"CFNetwork/758\\.3\\.15";s:7:"version";s:3:"9.3";}i:59;a:2:{s:5:"regex";s:22:"CFNetwork/758\\.2\\.[78]";s:7:"version";s:3:"9.2";}i:60;a:2:{s:5:"regex";s:19:"CFNetwork/758\\.1\\.6";s:7:"version";s:3:"9.1";}i:61;a:2:{s:5:"regex";s:19:"CFNetwork/758\\.0\\.2";s:7:"version";s:3:"9.0";}i:62;a:2:{s:5:"regex";s:19:"CFNetwork/711\\.5\\.6";s:7:"version";s:5:"8.4.1";}i:63;a:2:{s:5:"regex";s:19:"CFNetwork/711\\.4\\.6";s:7:"version";s:3:"8.4";}i:64;a:2:{s:5:"regex";s:20:"CFNetwork/711\\.3\\.18";s:7:"version";s:3:"8.3";}i:65;a:2:{s:5:"regex";s:20:"CFNetwork/711\\.2\\.23";s:7:"version";s:3:"8.2";}i:66;a:2:{s:5:"regex";s:23:"CFNetwork/711\\.1\\.1[26]";s:7:"version";s:3:"8.1";}i:67;a:2:{s:5:"regex";s:19:"CFNetwork/711\\.0\\.6";s:7:"version";s:3:"8.0";}i:68;a:2:{s:5:"regex";s:16:"CFNetwork/672\\.1";s:7:"version";s:3:"7.1";}i:69;a:2:{s:5:"regex";s:16:"CFNetwork/672\\.0";s:7:"version";s:3:"7.0";}i:70;a:2:{s:5:"regex";s:16:"CFNetwork/609\\.1";s:7:"version";s:3:"6.1";}i:71;a:2:{s:5:"regex";s:16:"CFNetwork/60[29]";s:7:"version";s:3:"6.0";}i:72;a:2:{s:5:"regex";s:16:"CFNetwork/548\\.1";s:7:"version";s:3:"5.1";}i:73;a:2:{s:5:"regex";s:16:"CFNetwork/548\\.0";s:7:"version";s:3:"5.0";}i:74;a:2:{s:5:"regex";s:17:"CFNetwork/485\\.13";s:7:"version";s:3:"4.3";}i:75;a:2:{s:5:"regex";s:17:"CFNetwork/485\\.12";s:7:"version";s:3:"4.2";}i:76;a:2:{s:5:"regex";s:17:"CFNetwork/485\\.10";s:7:"version";s:3:"4.1";}i:77;a:2:{s:5:"regex";s:16:"CFNetwork/485\\.2";s:7:"version";s:3:"4.0";}i:78;a:2:{s:5:"regex";s:17:"CFNetwork/467\\.12";s:7:"version";s:3:"3.2";}i:79;a:2:{s:5:"regex";s:13:"CFNetwork/459";s:7:"version";s:3:"3.1";}}}i:237;a:3:{s:5:"regex";s:42:"(?:iPhone|iPod_touch)/(\\d+[.\\d]*)(?: hw)?/";s:4:"name";s:3:"iOS";s:7:"version";s:2:"$1";}i:238;a:3:{s:5:"regex";s:18:"iOS(\\d+\\.\\d+\\.\\d+)";s:4:"name";s:3:"iOS";s:7:"version";s:2:"$1";}i:239;a:3:{s:5:"regex";s:16:"iOS(\\d+)\\.(\\d+)0";s:4:"name";s:3:"iOS";s:7:"version";s:5:"$1.$2";}i:240;a:3:{s:5:"regex";s:33:"FBMD/iPhone;.*FBSV/ ?(\\d+[.\\d]+);";s:4:"name";s:3:"iOS";s:7:"version";s:2:"$1";}i:241;a:3:{s:5:"regex";s:55:"(?:FBIOS|Messenger(?:Lite)?ForiOS).*FBSV/ ?(\\d+[.\\d]*);";s:4:"name";s:3:"iOS";s:7:"version";s:2:"$1";}i:242;a:3:{s:5:"regex";s:35:"iPhone OS,([\\d.]+).+(?:iPhone|iPod)";s:4:"name";s:3:"iOS";s:7:"version";s:2:"$1";}i:243;a:3:{s:5:"regex";s:20:"iPad.+; (\\d+[.\\d]+);";s:4:"name";s:3:"iOS";s:7:"version";s:2:"$1";}i:244;a:3:{s:5:"regex";s:29:"iPhone.+; Version (\\d+[.\\d]+)";s:4:"name";s:3:"iOS";s:7:"version";s:2:"$1";}i:245;a:3:{s:5:"regex";s:25:"OS=iOS;OSVer=(\\d+[.\\d]+);";s:4:"name";s:3:"iOS";s:7:"version";s:2:"$1";}i:246;a:3:{s:5:"regex";s:36:"os=Apple-iOS.+osversion=(\\d+[.\\d]+)/";s:4:"name";s:3:"iOS";s:7:"version";s:2:"$1";}i:247;a:3:{s:5:"regex";s:84:"(?:Apple-)?(?<!like )(?:iPhone|iPad|iPod)(?:.*Mac OS X.*Version/(\\d+\\.\\d+)|; Opera)?";s:4:"name";s:3:"iOS";s:7:"version";s:2:"$1";}i:248;a:3:{s:5:"regex";s:33:"dv\\(iPh.+ov\\((\\d+(?:[_.]\\d+)*)\\);";s:4:"name";s:3:"iOS";s:7:"version";s:2:"$1";}i:249;a:3:{s:5:"regex";s:178:"(?:Podcasts/(?:[\\d.]+)|Instacast(?:HD)?/(?:\\d\\.[\\d\\.abc]+)|Pocket Casts, iOS|\\(iOS\\)|iOS; Opera|Overcast|Castro|Podcat|iCatcher|RSSRadio/|MobileSafari/|WhatsApp/.*i$)(?!.*x86_64)";s:4:"name";s:3:"iOS";s:7:"version";s:0:"";}i:250;a:3:{s:5:"regex";s:44:"iTunes-(AppleTV|iPod|iPad|iPhone)/(?:[\\d.]+)";s:4:"name";s:3:"iOS";s:7:"version";s:0:"";}i:251;a:3:{s:5:"regex";s:23:"iOS[ /]Version ([\\d.]+)";s:4:"name";s:3:"iOS";s:7:"version";s:2:"$1";}i:252;a:3:{s:5:"regex";s:15:"Sonos/.+\\(ICRU_";s:4:"name";s:3:"iOS";s:7:"version";s:0:"";}i:253;a:3:{s:5:"regex";s:48:"CaptiveNetworkSupport|AirPlay|.*[ \\.\\-/]iOS(?!@)";s:4:"name";s:3:"iOS";s:7:"version";s:0:"";}i:254;a:3:{s:5:"regex";s:220:"(?:CFNetwork|Mana|StudioDisplay)/.+Darwin(?:/|; )(?:[\\d.]+).+(?:x86_64|i386|Power%20Macintosh)|(?:x86_64-apple-)?darwin(?:[\\d.]+)|C?Python.*Darwin|PowerMac|com\\.apple\\.Safari\\.SearchHelper|^(?:NetworkingExtension|Safari)";s:4:"name";s:3:"Mac";s:8:"versions";a:93:{i:0;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?24\\.3\\.0";s:7:"version";s:4:"15.3";}i:1;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?24\\.2\\.0";s:7:"version";s:4:"15.2";}i:2;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?24\\.1\\.0";s:7:"version";s:4:"15.1";}i:3;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?24\\.0\\.0";s:7:"version";s:4:"15.0";}i:4;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?23\\.6\\.0";s:7:"version";s:4:"14.6";}i:5;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?23\\.5\\.0";s:7:"version";s:4:"14.5";}i:6;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?23\\.4\\.0";s:7:"version";s:4:"14.4";}i:7;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?23\\.3\\.0";s:7:"version";s:4:"14.3";}i:8;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?23\\.2\\.0";s:7:"version";s:4:"14.2";}i:9;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?23\\.1\\.0";s:7:"version";s:4:"14.1";}i:10;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?23\\.0\\.0";s:7:"version";s:4:"14.0";}i:11;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?22\\.6\\.0";s:7:"version";s:4:"13.5";}i:12;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?22\\.5\\.0";s:7:"version";s:4:"13.4";}i:13;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?22\\.4\\.0";s:7:"version";s:4:"13.3";}i:14;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?22\\.3\\.0";s:7:"version";s:4:"13.2";}i:15;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?22\\.2\\.0";s:7:"version";s:4:"13.1";}i:16;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?22\\.1\\.0";s:7:"version";s:6:"13.0.1";}i:17;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?22\\.0\\.0";s:7:"version";s:4:"13.0";}i:18;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?21\\.6\\.0";s:7:"version";s:4:"12.5";}i:19;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?21\\.5\\.0";s:7:"version";s:4:"12.4";}i:20;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?21\\.4\\.0";s:7:"version";s:4:"12.3";}i:21;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?21\\.3\\.0";s:7:"version";s:4:"12.2";}i:22;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?21\\.2\\.0";s:7:"version";s:4:"12.1";}i:23;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?21\\.1\\.0";s:7:"version";s:6:"12.0.1";}i:24;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?21\\.0\\.0";s:7:"version";s:4:"12.0";}i:25;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?20\\.6\\.0";s:7:"version";s:4:"11.5";}i:26;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?20\\.5\\.0";s:7:"version";s:4:"11.4";}i:27;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?20\\.4\\.0";s:7:"version";s:4:"11.3";}i:28;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?20\\.3\\.0";s:7:"version";s:4:"11.2";}i:29;a:2:{s:5:"regex";s:43:"(?:x86_64-apple-)?Darwin(?:/|; )?20\\.2\\.0.*";s:7:"version";s:4:"11.1";}i:30;a:2:{s:5:"regex";s:44:"(?:x86_64-apple-)?Darwin(?:/|; )?20\\.[01]\\.0";s:7:"version";s:4:"11.0";}i:31;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?19\\.6\\.0";s:7:"version";s:7:"10.15.6";}i:32;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?19\\.5\\.0";s:7:"version";s:7:"10.15.5";}i:33;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?19\\.4\\.0";s:7:"version";s:7:"10.15.4";}i:34;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?19\\.3\\.0";s:7:"version";s:7:"10.15.3";}i:35;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?19\\.2\\.0";s:7:"version";s:7:"10.15.2";}i:36;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?19\\.0\\.0";s:7:"version";s:5:"10.15";}i:37;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?18\\.7\\.0";s:7:"version";s:5:"10.14";}i:38;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?18\\.6\\.0";s:7:"version";s:7:"10.14.5";}i:39;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?18\\.5\\.0";s:7:"version";s:7:"10.14.4";}i:40;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?18\\.2\\.0";s:7:"version";s:7:"10.14.1";}i:41;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?18\\.0\\.0";s:7:"version";s:5:"10.14";}i:42;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?17\\.7\\.0";s:7:"version";s:7:"10.13.6";}i:43;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?17\\.6\\.0";s:7:"version";s:7:"10.13.5";}i:44;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?17\\.5\\.0";s:7:"version";s:7:"10.13.4";}i:45;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?17\\.4\\.0";s:7:"version";s:7:"10.13.3";}i:46;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?17\\.3\\.0";s:7:"version";s:7:"10.13.2";}i:47;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?17\\.2\\.0";s:7:"version";s:7:"10.13.1";}i:48;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?17\\.0\\.0";s:7:"version";s:5:"10.13";}i:49;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?16\\.7\\.0";s:7:"version";s:7:"10.12.6";}i:50;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?16\\.6\\.0";s:7:"version";s:7:"10.12.5";}i:51;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?16\\.5\\.0";s:7:"version";s:7:"10.12.4";}i:52;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?16\\.4\\.0";s:7:"version";s:7:"10.12.3";}i:53;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?16\\.3\\.0";s:7:"version";s:7:"10.12.2";}i:54;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?16\\.2\\.0";s:7:"version";s:7:"10.12.2";}i:55;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?16\\.1\\.0";s:7:"version";s:7:"10.12.1";}i:56;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?16\\.0\\.0";s:7:"version";s:5:"10.12";}i:57;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?15\\.6\\.0";s:7:"version";s:7:"10.11.6";}i:58;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?15\\.5\\.0";s:7:"version";s:7:"10.11.5";}i:59;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?15\\.4\\.0";s:7:"version";s:7:"10.11.4";}i:60;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?15\\.3\\.0";s:7:"version";s:7:"10.11.3";}i:61;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?15\\.2\\.0";s:7:"version";s:7:"10.11.2";}i:62;a:2:{s:5:"regex";s:13:"CFNetwork/760";s:7:"version";s:5:"10.11";}i:63;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?14\\.5\\.0";s:7:"version";s:7:"10.10.5";}i:64;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?14\\.4\\.0";s:7:"version";s:7:"10.10.4";}i:65;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?14\\.3\\.0";s:7:"version";s:7:"10.10.3";}i:66;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?14\\.1\\.0";s:7:"version";s:7:"10.10.2";}i:67;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?14\\.0\\.0";s:7:"version";s:5:"10.10";}i:68;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?13\\.4\\.0";s:7:"version";s:6:"10.9.5";}i:69;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?13\\.3\\.0";s:7:"version";s:6:"10.9.4";}i:70;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?13\\.2\\.0";s:7:"version";s:6:"10.9.3";}i:71;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?13\\.1\\.0";s:7:"version";s:6:"10.9.2";}i:72;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?13\\.0\\.0";s:7:"version";s:6:"10.9.0";}i:73;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?12\\.6\\.0";s:7:"version";s:6:"10.8.5";}i:74;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?12\\.5\\.0";s:7:"version";s:6:"10.8.5";}i:75;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?12\\.4\\.0";s:7:"version";s:6:"10.8.4";}i:76;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?12\\.3\\.0";s:7:"version";s:6:"10.8.3";}i:77;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?12\\.2\\.0";s:7:"version";s:6:"10.8.2";}i:78;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?12\\.1\\.0";s:7:"version";s:6:"10.8.1";}i:79;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?11\\.5\\.0";s:7:"version";s:6:"10.7.5";}i:80;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?11\\.4\\.2";s:7:"version";s:6:"10.7.5";}i:81;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?11\\.4\\.0";s:7:"version";s:6:"10.7.4";}i:82;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?11\\.3\\.0";s:7:"version";s:6:"10.7.3";}i:83;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?11\\.2\\.0";s:7:"version";s:6:"10.7.2";}i:84;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?11\\.1\\.0";s:7:"version";s:6:"10.7.1";}i:85;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?10\\.8\\.0";s:7:"version";s:6:"10.6.8";}i:86;a:2:{s:5:"regex";s:44:"(?:x86_64-apple-)?Darwin(?:/|; )?10\\.7\\.[34]";s:7:"version";s:6:"10.6.7";}i:87;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?10\\.3\\.0";s:7:"version";s:6:"10.6.3";}i:88;a:2:{s:5:"regex";s:41:"(?:x86_64-apple-)?Darwin(?:/|; )?10\\.0\\.0";s:7:"version";s:4:"10.6";}i:89;a:2:{s:5:"regex";s:40:"(?:x86_64-apple-)?Darwin(?:/|; )?9\\.8\\.0";s:7:"version";s:6:"10.5.8";}i:90;a:2:{s:5:"regex";s:40:"(?:x86_64-apple-)?Darwin(?:/|; )?9\\.7\\.1";s:7:"version";s:6:"10.5.7";}i:91;a:2:{s:5:"regex";s:40:"(?:x86_64-apple-)?Darwin(?:/|; )?9\\.6\\.2";s:7:"version";s:6:"10.5.6";}i:92;a:2:{s:5:"regex";s:43:"(?:x86_64-apple-)?Darwin(?:/|; )?9\\.5\\.[05]";s:7:"version";s:6:"10.5.5";}}}i:255;a:3:{s:5:"regex";s:35:"Macintosh;Mac OS X \\((\\d+[.\\d]+)\\);";s:4:"name";s:3:"Mac";s:7:"version";s:2:"$1";}i:256;a:3:{s:5:"regex";s:55:"Mac[ +]OS[ +]?X(?:[ /,](?:Version )?(\\d+(?:[_.]\\d+)+))?";s:4:"name";s:3:"Mac";s:7:"version";s:2:"$1";}i:257;a:3:{s:5:"regex";s:29:"Mac (?:OS/)?(\\d+(?:[_.]\\d+)+)";s:4:"name";s:3:"Mac";s:7:"version";s:2:"$1";}i:258;a:3:{s:5:"regex";s:54:"(?:macOS(?:\\(Catalyst\\))?[ /,]|Mac(?:os)?-)(\\d+[.\\d]+)";s:4:"name";s:3:"Mac";s:7:"version";s:2:"$1";}i:259;a:3:{s:5:"regex";s:27:"Macintosh; OS X (\\d+[.\\d]+)";s:4:"name";s:3:"Mac";s:7:"version";s:2:"$1";}i:260;a:3:{s:5:"regex";s:15:"OSX/(\\d+[.\\d]+)";s:4:"name";s:3:"Mac";s:7:"version";s:2:"$1";}i:261;a:3:{s:5:"regex";s:130:"Darwin|Macintosh|Mac[ _]PowerPC|PPC|iMac|MacBook|.*macOS|AppleExchangeWebServices|com\\.apple\\.trustd|Sonos/.+\\(MDCR_|WhatsApp/.*N$";s:4:"name";s:3:"Mac";s:7:"version";s:0:"";}i:262;a:3:{s:5:"regex";s:7:"Fuchsia";s:4:"name";s:7:"Fuchsia";s:7:"version";s:0:"";}i:263;a:3:{s:5:"regex";s:76:"(?:BB10;.+Version|Black[Bb]erry[0-9a-z]+|Black[Bb]erry.+Version)/(\\d+[.\\d]+)";s:4:"name";s:13:"BlackBerry OS";s:7:"version";s:2:"$1";}i:264;a:3:{s:5:"regex";s:25:"RIM Tablet OS (\\d+[.\\d]+)";s:4:"name";s:20:"BlackBerry Tablet OS";s:7:"version";s:2:"$1";}i:265;a:3:{s:5:"regex";s:29:"RIM Tablet OS|QNX|Play[Bb]ook";s:4:"name";s:20:"BlackBerry Tablet OS";s:7:"version";s:0:"";}i:266;a:3:{s:5:"regex";s:10:"BlackBerry";s:4:"name";s:13:"BlackBerry OS";s:7:"version";s:0:"";}i:267;a:3:{s:5:"regex";s:4:"bPod";s:4:"name";s:13:"BlackBerry OS";s:7:"version";s:0:"";}i:268;a:3:{s:5:"regex";s:20:"BeOS[ +]?(\\d[.\\d]*)?";s:4:"name";s:4:"BeOS";s:7:"version";s:2:"$1";}i:269;a:3:{s:5:"regex";s:28:"Symbian/3.+NokiaBrowser/7\\.3";s:4:"name";s:9:"Symbian^3";s:7:"version";s:4:"Anna";}i:270;a:3:{s:5:"regex";s:28:"Symbian/3.+NokiaBrowser/7\\.4";s:4:"name";s:9:"Symbian^3";s:7:"version";s:5:"Belle";}i:271;a:3:{s:5:"regex";s:9:"Symbian/3";s:4:"name";s:9:"Symbian^3";s:7:"version";s:0:"";}i:272;a:3:{s:5:"regex";s:51:"(?:Series ?60|SymbOS|S60)(?:[ /]?(\\d+[.\\d]+|V\\d+))?";s:4:"name";s:20:"Symbian OS Series 60";s:7:"version";s:2:"$1";}i:273;a:3:{s:5:"regex";s:8:"Series40";s:4:"name";s:20:"Symbian OS Series 40";s:7:"version";s:0:"";}i:274;a:3:{s:5:"regex";s:21:"SymbianOS/(\\d+[.\\d]+)";s:4:"name";s:10:"Symbian OS";s:7:"version";s:2:"$1";}i:275;a:3:{s:5:"regex";s:11:"MeeGo|WeTab";s:4:"name";s:5:"MeeGo";s:7:"version";s:0:"";}i:276;a:3:{s:5:"regex";s:22:"Symbian(?: OS)?|SymbOS";s:4:"name";s:10:"Symbian OS";s:7:"version";s:0:"";}i:277;a:3:{s:5:"regex";s:5:"Nokia";s:4:"name";s:7:"Symbian";s:7:"version";s:0:"";}i:278;a:3:{s:5:"regex";s:36:"(?:Mobile|Tablet);.+Firefox/\\d+\\.\\d+";s:4:"name";s:10:"Firefox OS";s:7:"version";s:0:"";}i:279;a:3:{s:5:"regex";s:35:"RISC OS(?:-NC)?(?:[ /](\\d+[.\\d]+))?";s:4:"name";s:7:"RISC OS";s:7:"version";s:2:"$1";}i:280;a:3:{s:5:"regex";s:27:"Inferno(?:[ /](\\d+[.\\d]+))?";s:4:"name";s:7:"Inferno";s:7:"version";s:2:"$1";}i:281;a:3:{s:5:"regex";s:24:"bada(?:[ /](\\d+[.\\d]+))?";s:4:"name";s:4:"Bada";s:7:"version";s:2:"$1";}i:282;a:3:{s:5:"regex";s:7:"REX; U;";s:4:"name";s:3:"REX";s:7:"version";s:0:"";}i:283;a:3:{s:5:"regex";s:51:"(?:Brew(?!-Applet)(?: MP)?|BMP)(?:[ /](\\d+[.\\d]+))?";s:4:"name";s:4:"Brew";s:7:"version";s:2:"$1";}i:284;a:3:{s:5:"regex";s:28:"GoogleTV(?:[ /](\\d+[.\\d]+))?";s:4:"name";s:9:"Google TV";s:7:"version";s:2:"$1";}i:285;a:3:{s:5:"regex";s:17:"WebTV/(\\d+[.\\d]+)";s:4:"name";s:5:"WebTV";s:7:"version";s:2:"$1";}i:286;a:3:{s:5:"regex";s:37:"(?:SunOS|Solaris)(?:[/ ](\\d+[.\\d]+))?";s:4:"name";s:7:"Solaris";s:7:"version";s:2:"$1";}i:287;a:3:{s:5:"regex";s:24:"AIX(?:[/ ]?(\\d+[.\\d]+))?";s:4:"name";s:3:"AIX";s:7:"version";s:2:"$1";}i:288;a:3:{s:5:"regex";s:26:"HP-UX(?:[/ ]?(\\d+[.\\d]+))?";s:4:"name";s:5:"HP-UX";s:7:"version";s:2:"$1";}i:289;a:3:{s:5:"regex";s:31:"ElectroBSD(?:[/ ]?(\\d+[.\\d]+))?";s:4:"name";s:10:"ElectroBSD";s:7:"version";s:2:"$1";}i:290;a:3:{s:5:"regex";s:28:"FreeBSD(?:[/ ]?(\\d+[.\\d]+))?";s:4:"name";s:7:"FreeBSD";s:7:"version";s:2:"$1";}i:291;a:3:{s:5:"regex";s:27:"NetBSD(?:[/ ]?(\\d+[.\\d]+))?";s:4:"name";s:6:"NetBSD";s:7:"version";s:2:"$1";}i:292;a:3:{s:5:"regex";s:28:"OpenBSD(?:[/ ]?(\\d+[.\\d]+))?";s:4:"name";s:7:"OpenBSD";s:7:"version";s:2:"$1";}i:293;a:3:{s:5:"regex";s:30:"DragonFly(?:[/ ]?(\\d+[.\\d]+))?";s:4:"name";s:9:"DragonFly";s:7:"version";s:2:"$1";}i:294;a:3:{s:5:"regex";s:29:"Syllable(?:[/ ]?(\\d+[.\\d]+))?";s:4:"name";s:8:"Syllable";s:7:"version";s:2:"$1";}i:295;a:3:{s:5:"regex";s:32:"IRIX(?:;64)?(?:[/ ]?(\\d+[.\\d]+))";s:4:"name";s:4:"IRIX";s:7:"version";s:2:"$1";}i:296;a:3:{s:5:"regex";s:27:"OSF1(?:[/ ]?v?(\\d+[.\\d]+))?";s:4:"name";s:4:"OSF1";s:7:"version";s:2:"$1";}i:297;a:3:{s:5:"regex";s:21:"Nintendo (Wii|Switch)";s:4:"name";s:8:"Nintendo";s:7:"version";s:2:"$1";}i:298;a:3:{s:5:"regex";s:32:"PlayStation.+; Linux (\\d+[.\\d]+)";s:4:"name";s:11:"PlayStation";s:7:"version";s:2:"$1";}i:299;a:3:{s:5:"regex";s:46:"PlayStation ?(\\d)(?:[/ ](?:Pro )?(\\d+[.\\d]+))?";s:4:"name";s:11:"PlayStation";s:7:"version";s:5:"$1.$2";}i:300;a:3:{s:5:"regex";s:21:"Xbox|KIN\\.(?:One|Two)";s:4:"name";s:4:"Xbox";s:7:"version";s:3:"360";}i:301;a:3:{s:5:"regex";s:27:"Nitro|Nintendo ([3]?DS[i]?)";s:4:"name";s:15:"Nintendo Mobile";s:7:"version";s:2:"$1";}i:302;a:3:{s:5:"regex";s:31:"PlayStation ((?:Portable|Vita))";s:4:"name";s:20:"PlayStation Portable";s:7:"version";s:2:"$1";}i:303;a:3:{s:5:"regex";s:22:"OS/2; Warp (\\d+[.\\d]+)";s:4:"name";s:4:"OS/2";s:7:"version";s:2:"$1";}i:304;a:3:{s:5:"regex";s:4:"OS/2";s:4:"name";s:4:"OS/2";s:7:"version";s:0:"";}i:305;a:3:{s:5:"regex";s:17:"Linux/(\\d+[.\\d]+)";s:4:"name";s:9:"GNU/Linux";s:7:"version";s:2:"$1";}i:306;a:3:{s:5:"regex";s:65:"Linux[^a-z]|Cinnamon/(?:\\d+[.\\d]+)|.+(?:pc|unknown)-linux-gnu|X11";s:4:"name";s:9:"GNU/Linux";s:7:"version";s:0:"";}i:307;a:3:{s:5:"regex";s:95:"Java ME|(J2ME|Profile)/MIDP|MIDP-(?:\\d+[.\\d]+)/CLDC|Configuration/CLDC|Java; U; MIDP|MMP/\\d\\.\\d";s:4:"name";s:7:"Java ME";s:7:"version";s:0:"";}}}', ['allowed_classes' => false]);