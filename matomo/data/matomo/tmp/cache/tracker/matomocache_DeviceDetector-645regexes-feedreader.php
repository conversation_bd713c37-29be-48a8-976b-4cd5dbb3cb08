<?php return unserialize('a:2:{s:8:"lifetime";i:1753701387;s:4:"data";a:28:{i:0;a:4:{s:5:"regex";s:26:"Akregator(?:/(\\d+[.\\d]+))?";s:4:"name";s:9:"Akregator";s:7:"version";s:2:"$1";s:3:"url";s:33:"http://userbase.kde.org/Akregator";}i:1;a:4:{s:5:"regex";s:29:"Apple-PubSub(?:/(\\d+[.\\d]+))?";s:4:"name";s:12:"Apple PubSub";s:7:"version";s:2:"$1";s:3:"url";s:98:"https://developer.apple.com/library/mac/documentation/Darwin/Reference/ManPages/man1/pubsub.1.html";}i:2;a:4:{s:5:"regex";s:10:"BashPodder";s:4:"name";s:10:"BashPodder";s:7:"version";s:0:"";s:3:"url";s:31:"http://lincgeek.org/bashpodder/";}i:3;a:4:{s:5:"regex";s:18:"Breaker/v?([\\d.]+)";s:4:"name";s:7:"Breaker";s:7:"version";s:2:"$1";s:3:"url";s:26:"https://www.breaker.audio/";}i:4;a:4:{s:5:"regex";s:26:"FeedDemon(?:/(\\d+[.\\d]+))?";s:4:"name";s:9:"FeedDemon";s:7:"version";s:2:"$1";s:3:"url";s:25:"http://www.feeddemon.com/";}i:5;a:4:{s:5:"regex";s:39:"Feeddler(?:RSS|PRO)(?:[/ ](\\d+[.\\d]+))?";s:4:"name";s:19:"Feeddler RSS Reader";s:7:"version";s:2:"$1";s:3:"url";s:61:"http://www.chebinliu.com/projects/iphone/feeddler-rss-reader/";}i:6;a:4:{s:5:"regex";s:28:"QuiteRSS(?:[/ ](\\d+[.\\d]+))?";s:4:"name";s:8:"QuiteRSS";s:7:"version";s:2:"$1";s:3:"url";s:20:"https://quiterss.org";}i:7;a:4:{s:5:"regex";s:16:"gPodder/([\\d.]+)";s:4:"name";s:7:"gPodder";s:7:"version";s:2:"$1";s:3:"url";s:19:"http://gpodder.org/";}i:8;a:4:{s:5:"regex";s:41:"JetBrains Omea Reader(?:[/ ](\\d+[.\\d]+))?";s:4:"name";s:21:"JetBrains Omea Reader";s:7:"version";s:2:"$1";s:3:"url";s:37:"http://www.jetbrains.com/omea/reader/";}i:9;a:4:{s:5:"regex";s:27:"Liferea(?:[/ ](\\d+[.\\d]+))?";s:4:"name";s:7:"Liferea";s:7:"version";s:2:"$1";s:3:"url";s:22:"http://liferea.sf.net/";}i:10;a:4:{s:5:"regex";s:52:"(?:NetNewsWire|Evergreen.+MacOS)(?:[/ ](\\d+[.\\d]+))?";s:4:"name";s:11:"NetNewsWire";s:7:"version";s:2:"$1";s:3:"url";s:26:"http://netnewswireapp.com/";}i:11;a:4:{s:5:"regex";s:46:"NewsBlur (?:iPhone|iPad) App(?: v(\\d+[.\\d]+))?";s:4:"name";s:19:"NewsBlur Mobile App";s:7:"version";s:2:"$1";s:3:"url";s:23:"http://www.newsblur.com";}i:12;a:4:{s:5:"regex";s:24:"NewsBlur(?:/(\\d+[.\\d]+))";s:4:"name";s:8:"NewsBlur";s:7:"version";s:2:"$1";s:3:"url";s:23:"http://www.newsblur.com";}i:13;a:4:{s:5:"regex";s:30:"newsbeuter(?:[/ ](\\d+[.\\d]+))?";s:4:"name";s:10:"Newsbeuter";s:7:"version";s:2:"$1";s:3:"url";s:26:"http://www.newsbeuter.org/";}i:14;a:4:{s:5:"regex";s:20:"PritTorrent/([\\d.]+)";s:4:"name";s:11:"PritTorrent";s:7:"version";s:2:"$1";s:3:"url";s:18:"http://bitlove.org";}i:15;a:4:{s:5:"regex";s:19:"Pulp[/ ](\\d+[.\\d]+)";s:4:"name";s:4:"Pulp";s:7:"version";s:2:"$1";s:3:"url";s:32:"http://www.acrylicapps.com/pulp/";}i:16;a:4:{s:5:"regex";s:27:"ReadKit(?:[/ ](\\d+[.\\d]+))?";s:4:"name";s:7:"ReadKit";s:7:"version";s:2:"$1";s:3:"url";s:22:"http://readkitapp.com/";}i:17;a:4:{s:5:"regex";s:21:"Reeder[/ ](\\d+[.\\d]+)";s:4:"name";s:6:"Reeder";s:7:"version";s:2:"$1";s:3:"url";s:21:"http://reederapp.com/";}i:18;a:4:{s:5:"regex";s:29:"RSSBandit(?:[/ ](\\d+[.\\d]+))?";s:4:"name";s:10:"RSS Bandit";s:7:"version";s:2:"$1";s:3:"url";s:25:"http://www.rssbandit.org)";}i:19;a:4:{s:5:"regex";s:30:"RSS Junkie(?:[/ ](\\d+[.\\d]+))?";s:4:"name";s:10:"RSS Junkie";s:7:"version";s:2:"$1";s:3:"url";s:69:"https://play.google.com/store/apps/details?id=com.bitpowder.rssjunkie";}i:20;a:4:{s:5:"regex";s:26:"RSSOwl(?:[/ ](\\d+[.\\d]+))?";s:4:"name";s:6:"RSSOwl";s:7:"version";s:2:"$1";s:3:"url";s:23:"https://www.rssowl.org/";}i:21;a:4:{s:5:"regex";s:8:"Stringer";s:4:"name";s:8:"Stringer";s:7:"version";s:0:"";s:3:"url";s:35:"https://github.com/swanson/stringer";}i:22;a:4:{s:5:"regex";s:22:"^castero (\\d+\\.[.\\d]+)";s:4:"name";s:7:"castero";s:7:"version";s:2:"$1";s:3:"url";s:30:"https://github.com/xgi/castero";}i:23;a:4:{s:5:"regex";s:22:"^castget (\\d+\\.[.\\d]+)";s:4:"name";s:7:"castget";s:7:"version";s:2:"$1";s:3:"url";s:28:"https://castget.johndal.com/";}i:24;a:4:{s:5:"regex";s:22:"^Newsboat/([a-z\\d\\.]+)";s:4:"name";s:8:"Newsboat";s:7:"version";s:2:"$1";s:3:"url";s:31:"https://newsboat.org/index.html";}i:25;a:4:{s:5:"regex";s:33:"^Playapod(?: Lite)?/(\\d+\\.[.\\d]+)";s:4:"name";s:8:"Playapod";s:7:"version";s:2:"$1";s:3:"url";s:21:"https://playapod.com/";}i:26;a:4:{s:5:"regex";s:22:"PodPuppy (\\d+\\.[.\\d]+)";s:4:"name";s:8:"PodPuppy";s:7:"version";s:2:"$1";s:3:"url";s:38:"https://github.com/felixwatts/PodPuppy";}i:27;a:4:{s:5:"regex";s:16:"^Reeder/([\\d.]+)";s:4:"name";s:6:"Reeder";s:7:"version";s:2:"$1";s:3:"url";s:22:"https://reederapp.com/";}}}', ['allowed_classes' => false]);