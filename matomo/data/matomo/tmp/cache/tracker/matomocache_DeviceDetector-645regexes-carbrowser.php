<?php return unserialize('a:2:{s:8:"lifetime";i:1753701387;s:4:"data";a:5:{s:3:"BMW";a:3:{s:5:"regex";s:23:"AFTLBT962E2(?:[);/ ]|$)";s:6:"device";s:11:"car browser";s:6:"models";a:1:{i:0;a:2:{s:5:"regex";s:23:"AFTLBT962E2(?:[);/ ]|$)";s:5:"model";s:10:"Car (2022)";}}}s:4:"Jeep";a:3:{s:5:"regex";s:23:"AFTLFT962X3(?:[);/ ]|$)";s:6:"device";s:11:"car browser";s:6:"models";a:1:{i:0;a:2:{s:5:"regex";s:23:"AFTLFT962X3(?:[);/ ]|$)";s:5:"model";s:8:"Wagoneer";}}}s:5:"Tesla";a:3:{s:5:"regex";s:76:"(?:Tesla/(?:(?:develop|feature|terminal-das-fsd-eap)-)?[0-9.]+|QtCarBrowser)";s:6:"device";s:11:"car browser";s:6:"models";a:2:{i:0;a:2:{s:5:"regex";s:12:"QtCarBrowser";s:5:"model";s:7:"Model S";}i:1;a:2:{s:5:"regex";s:13:"Tesla/[0-9.]+";s:5:"model";s:0:"";}}}s:9:"MAC AUDIO";a:3:{s:5:"regex";s:14:"Mac Audio Spro";s:6:"device";s:11:"car browser";s:6:"models";a:1:{i:0;a:2:{s:5:"regex";s:4:"Spro";s:5:"model";s:5:"S Pro";}}}s:6:"Topway";a:3:{s:5:"regex";s:16:"sp9853i_1h10_vmm";s:6:"device";s:11:"car browser";s:6:"models";a:1:{i:0;a:2:{s:5:"regex";s:16:"sp9853i_1h10_vmm";s:5:"model";s:3:"TS9";}}}}}', ['allowed_classes' => false]);