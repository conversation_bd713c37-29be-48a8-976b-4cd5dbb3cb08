<?php return unserialize('a:2:{s:8:"lifetime";i:1753701387;s:4:"data";a:15:{s:5:"Apple";a:3:{s:5:"regex";s:15:"(?:Apple-)?iPod";s:6:"device";s:21:"portable media player";s:6:"models";a:8:{i:0;a:2:{s:5:"regex";s:23:"(?:Apple-)?iPod1[C,_]?1";s:5:"model";s:13:"iPod Touch 1G";}i:1;a:2:{s:5:"regex";s:23:"(?:Apple-)?iPod2[C,_]?1";s:5:"model";s:13:"iPod Touch 2G";}i:2;a:2:{s:5:"regex";s:23:"(?:Apple-)?iPod3[C,_]?1";s:5:"model";s:12:"iPod Touch 3";}i:3;a:2:{s:5:"regex";s:23:"(?:Apple-)?iPod4[C,_]?1";s:5:"model";s:12:"iPod Touch 4";}i:4;a:2:{s:5:"regex";s:23:"(?:Apple-)?iPod5[C,_]?1";s:5:"model";s:12:"iPod Touch 5";}i:5;a:2:{s:5:"regex";s:23:"(?:Apple-)?iPod7[C,_]?1";s:5:"model";s:12:"iPod Touch 6";}i:6;a:2:{s:5:"regex";s:34:"(?:Apple-)?iPod9[C,_]?1|iPodTouch7";s:5:"model";s:12:"iPod Touch 7";}i:7;a:2:{s:5:"regex";s:15:"(?:Apple-)?iPod";s:5:"model";s:10:"iPod Touch";}}}s:5:"Cowon";a:3:{s:5:"regex";s:20:"COWON ([^;/]+) Build";s:6:"device";s:21:"portable media player";s:5:"model";s:2:"$1";}s:4:"FiiO";a:3:{s:5:"regex";s:4:"FiiO";s:6:"device";s:21:"portable media player";s:6:"models";a:2:{i:0;a:2:{s:5:"regex";s:12:"M11 Plus LTD";s:5:"model";s:12:"M11 Plus LTD";}i:1;a:2:{s:5:"regex";s:20:"FiiO M(11S|1[157]|6)";s:5:"model";s:3:"M$1";}}}s:9:"Microsoft";a:3:{s:5:"regex";s:16:"Microsoft ZuneHD";s:6:"device";s:21:"portable media player";s:5:"model";s:7:"Zune HD";}s:9:"Panasonic";a:3:{s:5:"regex";s:10:"(SV-MV100)";s:6:"device";s:21:"portable media player";s:5:"model";s:2:"$1";}s:7:"Samsung";a:3:{s:5:"regex";s:27:"YP-(G[SIPB]?1|G[57]0|GB70D)";s:6:"device";s:21:"portable media player";s:6:"models";a:7:{i:0;a:2:{s:5:"regex";s:9:"YP-G[B]?1";s:5:"model";s:17:"Galaxy Player 4.0";}i:1;a:2:{s:5:"regex";s:6:"YP-G70";s:5:"model";s:17:"Galaxy Player 5.0";}i:2;a:2:{s:5:"regex";s:6:"YP-GS1";s:5:"model";s:17:"Galaxy Player 3.6";}i:3;a:2:{s:5:"regex";s:6:"YP-GI1";s:5:"model";s:17:"Galaxy Player 4.2";}i:4;a:2:{s:5:"regex";s:6:"YP-GP1";s:5:"model";s:17:"Galaxy Player 5.8";}i:5;a:2:{s:5:"regex";s:6:"YP-G50";s:5:"model";s:16:"Galaxy Player 50";}i:6;a:2:{s:5:"regex";s:8:"YP-GB70D";s:5:"model";s:21:"Galaxy Player 70 Plus";}}}s:4:"Wizz";a:3:{s:5:"regex";s:24:"(DV-PTB1080)(?:[);/ ]|$)";s:6:"device";s:21:"portable media player";s:5:"model";s:2:"$1";}s:5:"SONOS";a:3:{s:5:"regex";s:31:"(?:Sonos/.+\\((?:ZP.+)\\)|Sonos;)";s:6:"device";s:21:"portable media player";s:6:"models";a:8:{i:0;a:2:{s:5:"regex";s:40:"\\((ZPS(?:[13569]|1[1-578]|2[03])|ZP90)\\)";s:5:"model";s:2:"$1";}i:1;a:2:{s:5:"regex";s:11:"Sonos;Beam;";s:5:"model";s:4:"Beam";}i:2;a:2:{s:5:"regex";s:11:"Sonos;Roam;";s:5:"model";s:4:"Roam";}i:3;a:2:{s:5:"regex";s:16:"Sonos;Bookshelf;";s:5:"model";s:9:"Bookshelf";}i:4;a:2:{s:5:"regex";s:11:"Sonos;Move;";s:5:"model";s:4:"Move";}i:5;a:2:{s:5:"regex";s:15:"Sonos;Play(\\d);";s:5:"model";s:7:"Play:$1";}i:6;a:2:{s:5:"regex";s:10:"Sonos;One;";s:5:"model";s:3:"One";}i:7;a:2:{s:5:"regex";s:11:"Sonos;OneSL";s:5:"model";s:5:"OneSL";}}}s:8:"Shanling";a:3:{s:5:"regex";s:11:"Shanling M6";s:6:"device";s:21:"portable media player";s:6:"models";a:1:{i:0;a:2:{s:5:"regex";s:19:"Shanling (M6\\(21\\))";s:5:"model";s:2:"$1";}}}s:8:"Sylvania";a:3:{s:5:"regex";s:15:"(SLTDVD102[34])";s:6:"device";s:21:"portable media player";s:5:"model";s:2:"$1";}s:5:"KuGou";a:3:{s:5:"regex";s:14:"KuGou[_ -](P5)";s:6:"device";s:21:"portable media player";s:5:"model";s:2:"$1";}s:7:"Surfans";a:3:{s:5:"regex";s:18:"(Y57A)(?:[);/ ]|$)";s:6:"device";s:21:"portable media player";s:5:"model";s:2:"$1";}s:6:"Oilsky";a:3:{s:5:"regex";s:39:"Oilsky (M501|M303)(?:-Pro)?(?:[);/ ]|$)";s:6:"device";s:21:"portable media player";s:6:"models";a:2:{i:0;a:2:{s:5:"regex";s:8:"M303-Pro";s:5:"model";s:8:"M303 Pro";}i:1;a:2:{s:5:"regex";s:11:"(M501|M303)";s:5:"model";s:2:"$1";}}}s:6:"Diofox";a:3:{s:5:"regex";s:26:"Diofox[ _](M8)(?:[);/ ]|$)";s:6:"device";s:21:"portable media player";s:5:"model";s:2:"$1";}s:6:"MECHEN";a:3:{s:5:"regex";s:6:"MECHEN";s:6:"device";s:21:"portable media player";s:6:"models";a:2:{i:0;a:2:{s:5:"regex";s:43:"MECHEN[- _]([^;/)]+)[- _]Pro(?: Build|[);])";s:5:"model";s:6:"$1 Pro";}i:1;a:2:{s:5:"regex";s:35:"MECHEN[- _]([^;/)]+)(?: Build|[);])";s:5:"model";s:2:"$1";}}}}}', ['allowed_classes' => false]);