<?php return unserialize('a:2:{s:8:"lifetime";i:1753701387;s:4:"data";a:43:{i:0;a:3:{s:5:"regex";s:26:"Audacious(?:[ /]([\\d.]+))?";s:4:"name";s:9:"Audacious";s:7:"version";s:2:"$1";}i:1;a:3:{s:5:"regex";s:124:"(?:AlexaMediaPlayer/|^AlexaMediaPlayer/|^Echo/|Amazon;Echo(?:_|;)|^AlexaService/|^Alexa Mobile Voice/)([a-z\\d]+\\.[a-z.\\d]+)?";s:4:"name";s:5:"<PERSON><PERSON>";s:7:"version";s:2:"$1";}i:2;a:3:{s:5:"regex";s:24:"Banshee(?:[ /]([\\d.]+))?";s:4:"name";s:7:"Banshee";s:7:"version";s:2:"$1";}i:3;a:3:{s:5:"regex";s:22:"Boxee(?:[ /]([\\d.]+))?";s:4:"name";s:5:"Boxee";s:7:"version";s:2:"$1";}i:4;a:3:{s:5:"regex";s:27:"Clementine(?:[ /]([\\d.]+))?";s:4:"name";s:10:"Clementine";s:7:"version";s:2:"$1";}i:5;a:3:{s:5:"regex";s:20:"Deezer(?:/([\\d.]+))?";s:4:"name";s:6:"Deezer";s:7:"version";s:2:"$1";}i:6;a:3:{s:5:"regex";s:38:"iTunes(?:-iPhone|-iPad)?(?:/([\\d.]+))?";s:4:"name";s:6:"iTunes";s:7:"version";s:2:"$1";}i:7;a:3:{s:5:"regex";s:21:"FlyCast(?:/([\\d.]+))?";s:4:"name";s:7:"FlyCast";s:7:"version";s:2:"$1";}i:8;a:3:{s:5:"regex";s:24:"foobar2000(?:/([\\d.]+))?";s:4:"name";s:10:"Foobar2000";s:7:"version";s:2:"$1";}i:9;a:3:{s:5:"regex";s:31:"MediaMonkey(?:[ /](\\d+[.\\d]+))?";s:4:"name";s:11:"MediaMonkey";s:7:"version";s:2:"$1";}i:10;a:3:{s:5:"regex";s:21:"Miro(?:/(\\d+[.\\d]+))?";s:4:"name";s:4:"Miro";s:7:"version";s:2:"$1";}i:11;a:3:{s:5:"regex";s:26:"NexPlayer(?:/(\\d+[.\\d]+))?";s:4:"name";s:9:"NexPlayer";s:7:"version";s:2:"$1";}i:12;a:3:{s:5:"regex";s:25:"Nightingale(?:/([\\d.]+))?";s:4:"name";s:11:"Nightingale";s:7:"version";s:2:"$1";}i:13;a:3:{s:5:"regex";s:56:"QuickTime(?:(?:(?:.+qtver=)|(?:(?: E-)?[\\./]))([\\d.]+))?";s:4:"name";s:9:"QuickTime";s:7:"version";s:2:"$1";}i:14;a:3:{s:5:"regex";s:22:"Songbird(?:/([\\d.]+))?";s:4:"name";s:8:"Songbird";s:7:"version";s:2:"$1";}i:15;a:3:{s:5:"regex";s:23:"SubStream(?:/([\\d.]+))?";s:4:"name";s:9:"SubStream";s:7:"version";s:2:"$1";}i:16;a:3:{s:5:"regex";s:15:"Sonos/([\\d.]+)?";s:4:"name";s:5:"SONOS";s:7:"version";s:2:"$1";}i:17;a:3:{s:5:"regex";s:25:"(?:Lib)?VLC(?:/([\\d.]+))?";s:4:"name";s:3:"VLC";s:7:"version";s:2:"$1";}i:18;a:3:{s:5:"regex";s:32:"Winamp(?:MPEG)?(?:/(\\d+[.\\d]+))?";s:4:"name";s:6:"Winamp";s:7:"version";s:2:"$1";}i:19;a:3:{s:5:"regex";s:39:"J\\. River Internet Reader/(\\d+\\.[.\\d]+)";s:4:"name";s:19:"JRiver Media Center";s:7:"version";s:2:"$1";}i:20;a:3:{s:5:"regex";s:50:"(?:Windows-Media-Player|NSPlayer)(?:/(\\d+[.\\d]+))?";s:4:"name";s:20:"Windows Media Player";s:7:"version";s:2:"$1";}i:21;a:3:{s:5:"regex";s:18:"XBMC(?:/([\\d.]+))?";s:4:"name";s:4:"XBMC";s:7:"version";s:2:"$1";}i:22;a:3:{s:5:"regex";s:18:"Kodi(?:/([\\d.]+))?";s:4:"name";s:4:"Kodi";s:7:"version";s:2:"$1";}i:23;a:3:{s:5:"regex";s:25:"stagefright(?:/([\\d.]+))?";s:4:"name";s:11:"Stagefright";s:7:"version";s:2:"$1";}i:24;a:3:{s:5:"regex";s:28:"GoogleChirp(?:/(\\d+[.\\d]+))?";s:4:"name";s:15:"Google Podcasts";s:7:"version";s:2:"$1";}i:25;a:3:{s:5:"regex";s:36:"Music Player Daemon (?:(\\d+[.\\d]+))?";s:4:"name";s:19:"Music Player Daemon";s:7:"version";s:2:"$1";}i:26;a:3:{s:5:"regex";s:20:"mpv (?:(\\d+[.\\d]+))?";s:4:"name";s:3:"mpv";s:7:"version";s:2:"$1";}i:27;a:3:{s:5:"regex";s:20:"HTC Streaming Player";s:4:"name";s:20:"HTC Streaming Player";s:7:"version";s:0:"";}i:28;a:3:{s:5:"regex";s:22:"MediaGo(?:/([\\w\\.]+))?";s:4:"name";s:13:"Sony Media Go";s:7:"version";s:2:"$1";}i:29;a:3:{s:5:"regex";s:23:"MPlayer[ /](\\d+\\.[\\d.])";s:4:"name";s:7:"MPlayer";s:7:"version";s:2:"$1";}i:30;a:3:{s:5:"regex";s:23:"Downcast/(\\d+\\.[\\d.]+)?";s:4:"name";s:8:"Downcast";s:7:"version";s:2:"$1";}i:31;a:3:{s:5:"regex";s:15:"^Juice/([\\d.]+)";s:4:"name";s:5:"Juice";s:7:"version";s:2:"$1";}i:32;a:3:{s:5:"regex";s:24:"just_audio/(\\d+\\.[.\\d]+)";s:4:"name";s:10:"Just Audio";s:7:"version";s:2:"$1";}i:33;a:3:{s:5:"regex";s:20:"^Kasts/(\\d+\\.[.\\d]+)";s:4:"name";s:5:"Kasts";s:7:"version";s:2:"$1";}i:34;a:3:{s:5:"regex";s:28:"MixerBox(?:%20Pro)?/([.\\d]+)";s:4:"name";s:8:"MixerBox";s:7:"version";s:2:"$1";}i:35;a:3:{s:5:"regex";s:28:"^MusicBee(?:/(\\d+\\.[.\\d]+))?";s:4:"name";s:8:"MusicBee";s:7:"version";s:2:"$1";}i:36;a:3:{s:5:"regex";s:21:"^amarok/(\\d+\\.[.\\d]+)";s:4:"name";s:6:"Amarok";s:7:"version";s:2:"$1";}i:37;a:3:{s:5:"regex";s:18:"Hubhopper/([\\d.]+)";s:4:"name";s:9:"Hubhopper";s:7:"version";s:2:"$1";}i:38;a:3:{s:5:"regex";s:27:"StudioDisplay/(\\d+\\.[\\d.]+)";s:4:"name";s:13:"StudioDisplay";s:7:"version";s:2:"$1";}i:39;a:3:{s:5:"regex";s:18:"JHV/SWHV-([.\\d+]+)";s:4:"name";s:12:"JHelioviewer";s:7:"version";s:2:"$1";}i:40;a:3:{s:5:"regex";s:31:"com\\.devcoder\\.iptvxtreamplayer";s:4:"name";s:13:"Xtream Player";s:7:"version";s:0:"";}i:41;a:3:{s:5:"regex";s:27:"DIGA(?:Plus/(\\d+\\.[.\\d]+))?";s:4:"name";s:4:"DIGA";s:7:"version";s:2:"$1";}i:42;a:3:{s:5:"regex";s:30:"YouView(?:HTML/(\\d+\\.[.\\d]+))?";s:4:"name";s:7:"YouView";s:7:"version";s:2:"$1";}}}', ['allowed_classes' => false]);