# Documentation

This directory contains advanced usage and configurations of the project. Below is a brief introduction to each document:

- [healthcheck.md](healthcheck.md): Describes the health check mechanism for the container and its configuration.
- [host-connectivity.md](host-connectivity.md): Discusses potential host connectivity issue when using Zero Trust and provides three solutions.
- [warp-connector.md](warp-connector.md): Provides steps and a sample Docker Compose file for setting up the WARP Connector.
- [zero-trust.md](zero-trust.md): Details the steps to use the WARP client with Cloudflare Zero Trust.
- [masque.md](masque.md): Describes how to enable MASQUE, WARP's new protocol.
- [podman.md](podman.md): Provides information to run the container with Podman.
- [proxy-mode.md](proxy-mode.md): instructions on how to use the container in WARP's proxy mode.
- [tun-not-permitted.md](tun-not-permitted.md): Explains the error message `{ err: Os { code: 1, kind: PermissionDenied, message: "Operation not permitted" }, context: "open tun" }` and how to resolve it.
