dataRoot: ./data
listen: true
listenAddress:
  ipv4: 0.0.0.0
  ipv6: "[::]"
protocol:
  ipv4: true
  ipv6: false
dnsPreferIPv6: false
port: 8000
ssl:
  enabled: false
  certPath: ./certs/cert.pem
  keyPath: ./certs/privkey.pem
whitelistMode: false
enableForwardedWhitelist: false
whitelist:
  - ::1
  - 127.0.0.1
whitelistDockerHosts: false
basicAuthMode: true
basicAuthUser:
  username: redkaytop
  password: Redkay1994
enableCorsProxy: false
requestProxy:
  enabled: false
  url: socks5://username:<EMAIL>:1080
  bypass:
    - localhost
    - 127.0.0.1
enableUserAccounts: false
enableDiscreetLogin: false
autheliaAuth: false
perUserBasicAuth: false
sessionTimeout: -1
disableCsrfProtection: false
securityOverride: false
logging:
  enableAccessLog: true
  minLogLevel: 0
rateLimiting:
  preferRealIpHeader: false
backups:
  common:
    numberOfBackups: 50
  chat:
    enabled: true
    checkIntegrity: true
    maxTotalBackups: -1
    throttleInterval: 10000
thumbnails:
  enabled: true
  format: jpg
  quality: 95
  dimensions:
    bg:
      - 160
      - 90
    avatar:
      - 96
      - 144
    persona:
      - 96
      - 144
performance:
  lazyLoadCharacters: false
  memoryCacheCapacity: 100mb
  useDiskCache: true
allowKeysExposure: false
skipContentCheck: false
whitelistImportDomains:
  - localhost
  - cdn.discordapp.com
  - files.catbox.moe
  - raw.githubusercontent.com
  - char-archive.evulid.cc
requestOverrides: []
extensions:
  enabled: true
  autoUpdate: true
  models:
    autoDownload: true
    classification: Cohee/distilbert-base-uncased-go-emotions-onnx
    captioning: Xenova/vit-gpt2-image-captioning
    embedding: Cohee/jina-embeddings-v2-base-en
    speechToText: Xenova/whisper-small
    textToSpeech: Xenova/speecht5_tts
enableDownloadableTokenizers: true
promptPlaceholder: "[Start a new chat]"
openai:
  randomizeUserId: false
  captionSystemPrompt: ""
deepl:
  formality: default
mistral:
  enablePrefix: false
ollama:
  keepAlive: -1
  batchSize: -1
claude:
  enableSystemPromptCache: false
  cachingAtDepth: -1
  extendedTTL: false
gemini:
  apiVersion: v1beta
enableServerPlugins: false
enableServerPluginsAutoUpdate: true
browserLaunch:
  enabled: true
  hostname: auto
  port: -1
  avoidLocalhost: false
  browser: default
cacheBuster:
  enabled: false
  userAgentPattern: ""
